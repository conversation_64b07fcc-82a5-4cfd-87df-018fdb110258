/* ===== CLAN STONE TABLET STYLING ===== */

.clan-stone-tablet {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(20, 20, 20, 0.9));
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1rem;
}

/* Stone Tablet Header */
.stone-tablet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.stone-tablet-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.stone-tablet-title i {
  font-size: 1.5rem;
  color: var(--primary-gold);
}

.stone-tablet-title h2 {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-gold);
  margin: 0;
}

.clan-tag {
  background: rgba(212, 175, 55, 0.2);
  color: var(--primary-gold);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.stone-tablet-stats {
  display: flex;
  gap: 1rem;
}

.stone-tablet-stats .stat-item {
  text-align: center;
}

.stone-tablet-stats .stat-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-gold);
}

.stone-tablet-stats .stat-label {
  font-size: 0.75rem;
  color: var(--neutral-400);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Game Type Tabs */
.stone-tablet-game-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  justify-content: center;
}

.stone-tablet-game-tabs .game-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: var(--neutral-300);
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stone-tablet-game-tabs .game-tab:hover {
  background: rgba(212, 175, 55, 0.1);
  border-color: rgba(212, 175, 55, 0.3);
  color: var(--primary-gold);
}

.stone-tablet-game-tabs .game-tab.active {
  background: rgba(212, 175, 55, 0.2);
  border-color: var(--primary-gold);
  color: var(--primary-gold);
}

/* Post Composer */
.stone-tablet-composer {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.composer-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.composer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid rgba(212, 175, 55, 0.3);
}

.composer-input-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.composer-title {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 0.5rem;
  color: var(--neutral-100);
  font-size: 0.9rem;
}

.composer-title::placeholder {
  color: var(--neutral-500);
}

.composer-textarea {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 0.5rem;
  color: var(--neutral-100);
  font-size: 0.9rem;
  resize: vertical;
  min-height: 80px;
}

.composer-textarea::placeholder {
  color: var(--neutral-500);
}

.composer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.composer-tools {
  display: flex;
  gap: 0.5rem;
}

.composer-tool {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 0.5rem;
  color: var(--neutral-400);
  cursor: pointer;
  transition: all 0.3s ease;
}

.composer-tool:hover {
  background: rgba(212, 175, 55, 0.1);
  color: var(--primary-gold);
}

/* Posts Feed */
.stone-tablet-posts {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stone-tablet-post {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.stone-tablet-post:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(212, 175, 55, 0.2);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.post-author {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  color: var(--primary-gold);
  font-size: 0.9rem;
}

.post-date {
  font-size: 0.75rem;
  color: var(--neutral-500);
}

.post-game-type {
  color: var(--neutral-400);
  font-size: 0.8rem;
}

.post-content {
  margin-bottom: 1rem;
}

.post-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--neutral-100);
  margin: 0 0 0.5rem 0;
}

.post-text {
  color: var(--neutral-300);
  line-height: 1.5;
  font-size: 0.9rem;
}

.post-actions {
  display: flex;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  color: var(--neutral-400);
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(212, 175, 55, 0.1);
  color: var(--primary-gold);
  border-color: rgba(212, 175, 55, 0.3);
}

.action-btn.like-btn:hover {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.3);
}

.action-btn.celebrate-btn:hover {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border-color: rgba(245, 158, 11, 0.3);
}

/* Empty State */
.empty-stone-tablet {
  text-align: center;
  padding: 2rem;
  color: var(--neutral-500);
}

.empty-stone-tablet i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--neutral-600);
}

.empty-stone-tablet h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--neutral-400);
}

.empty-stone-tablet p {
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Loading State */
.clan-stone-tablet-loading {
  text-align: center;
  padding: 2rem;
  color: var(--neutral-400);
}

.clan-stone-tablet-loading .loading-spinner {
  margin-bottom: 1rem;
}

.clan-stone-tablet-loading i {
  font-size: 2rem;
  color: var(--primary-gold);
}

/* Error State */
.clan-stone-tablet-error {
  text-align: center;
  padding: 2rem;
  color: var(--error-red);
}

.clan-stone-tablet-error i {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.clan-stone-tablet-error h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.clan-stone-tablet-error p {
  margin-bottom: 1rem;
  color: var(--neutral-400);
}

/* Tablet Header */
.tablet-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.tablet-header h2 {
  font-family: var(--font-display);
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--primary-gold);
  margin: 0 0 0.5rem 0;
}

.tablet-subtitle {
  color: var(--neutral-400);
  font-size: 0.9rem;
  margin: 0;
}

/* Tablet Content */
.tablet-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.tablet-section {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
}

.tablet-section h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-gold);
  margin: 0 0 1rem 0;
}

.tablet-section h3 i {
  font-size: 1rem;
}

/* Clan Info Grid */
.clan-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.info-item .label {
  font-weight: 500;
  color: var(--neutral-400);
  font-size: 0.9rem;
}

.info-item .value {
  font-weight: 600;
  color: var(--neutral-100);
  font-size: 0.9rem;
}

.clan-description {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.clan-description h4 {
  color: var(--primary-gold);
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.clan-description p {
  color: var(--neutral-300);
  line-height: 1.5;
  margin: 0;
}

/* Members Tablet */
.members-tablet {
  max-height: 400px;
  overflow-y: auto;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.member-tablet-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.member-tablet-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(212, 175, 55, 0.3);
}

.member-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.member-avatar {
  width: 40px;
  height: 40px;
  background: rgba(212, 175, 55, 0.2);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-gold);
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-100);
  margin: 0 0 0.25rem 0;
}

.member-role {
  font-size: 0.8rem;
  color: var(--neutral-400);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.member-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
}

.stat-item .label {
  color: var(--neutral-500);
}

.stat-item .value {
  color: var(--neutral-200);
  font-weight: 500;
}

/* Clan Achievements */
.clan-achievements {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}

.achievement-item i {
  font-size: 1.25rem;
  color: var(--primary-gold);
}

.achievement-item span {
  color: var(--neutral-200);
  font-weight: 500;
}

/* No Clan Message */
.no-clan-message {
  text-align: center;
  padding: 2rem;
  color: var(--neutral-500);
}

.no-clan-message i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--neutral-600);
}

.no-clan-message h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--neutral-400);
}

.no-clan-message p {
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0.5rem 0;
}

.no-members {
  text-align: center;
  color: var(--neutral-500);
  font-style: italic;
  padding: 1rem;
}

/* Stone Tablet Filters */
.stone-tablet-filters {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
  margin-bottom: 1rem;
}

.clan-filter {
  display: flex;
  align-items: center;
}

.clan-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: var(--neutral-300);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clan-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(212, 175, 55, 0.3);
}

.clan-toggle-btn.active {
  background: rgba(212, 175, 55, 0.2);
  border-color: rgba(212, 175, 55, 0.5);
  color: var(--primary-gold);
}

.clan-toggle-btn.active i {
  color: var(--primary-gold);
}

/* Game Tabs */
.game-tabs {
  display: flex;
  gap: 0.5rem;
}

.game-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: var(--neutral-400);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.game-tab:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--neutral-200);
}

.game-tab.active {
  background: rgba(212, 175, 55, 0.15);
  border-color: rgba(212, 175, 55, 0.3);
  color: var(--primary-gold);
}

/* Stone Tablet Container Layout */
.stone-tablet-container {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.stone-tablet-main {
  min-width: 0; /* Prevents grid overflow */
}

.stone-tablet-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Sidebar Sections */
.sidebar-section {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
}

.sidebar-section h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-gold);
  margin: 0 0 1rem 0;
}

.sidebar-section h3 i {
  font-size: 0.9rem;
}

/* Clan Info Widget */
.clan-info-card h4 {
  color: var(--primary-gold);
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.clan-info-card .clan-game {
  color: var(--neutral-400);
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

.clan-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.clan-stats .stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
}

.clan-stats .label {
  color: var(--neutral-500);
}

.clan-stats .value {
  color: var(--neutral-200);
  font-weight: 500;
}

/* Achievement List */
.achievement-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.achievement-item i {
  color: var(--primary-gold);
  width: 16px;
}

.achievement-item span {
  color: var(--neutral-200);
}

/* Member List */
.member-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: var(--neutral-300);
}

.member-item i {
  color: var(--neutral-500);
  width: 14px;
}

/* Post Composer */
.post-composer {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.composer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.composer-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-gold);
  margin: 0;
}

.game-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: var(--neutral-200);
  padding: 0.5rem;
  font-size: 0.9rem;
}

.post-title-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: var(--neutral-100);
  padding: 0.75rem;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.post-content-input {
  width: 100%;
  min-height: 120px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: var(--neutral-100);
  padding: 0.75rem;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: vertical;
  margin-bottom: 1rem;
}

.composer-footer {
  display: flex;
  justify-content: flex-end;
}

/* No Clan Message */
.no-clan-message {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--neutral-500);
  grid-column: 1 / -1; /* Span full width */
}

.no-clan-message i {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  color: var(--neutral-600);
}

.no-clan-message h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--neutral-400);
}

.no-clan-message p {
  font-size: 1rem;
  line-height: 1.5;
  margin: 0.75rem 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.clan-actions {
  margin-top: 2rem;
}

/* Post Cards */
.post-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.post-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
}

.post-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.post-author {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.author-avatar {
  width: 40px;
  height: 40px;
  background: rgba(212, 175, 55, 0.2);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-gold);
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-100);
  margin: 0 0 0.25rem 0;
}

.post-meta {
  font-size: 0.8rem;
  color: var(--neutral-500);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.game-type {
  background: rgba(212, 175, 55, 0.15);
  color: var(--primary-gold);
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 500;
}

.post-pinned {
  color: var(--primary-gold);
  font-size: 1.1rem;
}

.post-content {
  margin-bottom: 1rem;
}

.post-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--neutral-100);
  margin: 0 0 0.75rem 0;
  line-height: 1.3;
}

.post-body {
  color: var(--neutral-300);
  line-height: 1.6;
  font-size: 0.95rem;
}

.post-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.post-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: var(--neutral-400);
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.post-action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--neutral-200);
}

.post-action-btn.like-btn:hover {
  color: #ff6b6b;
  border-color: rgba(255, 107, 107, 0.3);
}

.post-action-btn.comment-btn:hover {
  color: #4ecdc4;
  border-color: rgba(78, 205, 196, 0.3);
}

.post-action-btn.share-btn:hover {
  color: var(--primary-gold);
  border-color: rgba(212, 175, 55, 0.3);
}

/* Empty Posts */
.empty-posts {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--neutral-500);
}

.empty-posts i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--neutral-600);
}

.empty-posts h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--neutral-400);
}

.empty-posts p {
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0.5rem 0;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: var(--neutral-200);
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--primary-gold);
}

/* Responsive Design */
@media (max-width: 768px) {
  .stone-tablet-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .clan-filter {
    justify-content: center;
  }

  .game-tabs {
    justify-content: center;
    flex-wrap: wrap;
  }

  .stone-tablet-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1rem;
  }

  .stone-tablet-sidebar {
    order: -1; /* Move sidebar above main content on mobile */
  }

  .stone-tablet-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .stone-tablet-game-tabs {
    flex-wrap: wrap;
  }

  .composer-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .composer-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .post-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .post-actions {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .post-action-btn {
    flex: 1;
    justify-content: center;
    min-width: 80px;
  }

  .clan-info-grid {
    grid-template-columns: 1fr;
  }

  .members-grid {
    grid-template-columns: 1fr;
  }

  .clan-achievements {
    grid-template-columns: 1fr;
  }

  .tablet-header h2 {
    font-size: 1.5rem;
  }

  .clan-toggle-btn {
    justify-content: center;
  }

  .game-tab {
    flex: 1;
    justify-content: center;
    min-width: 80px;
  }
}