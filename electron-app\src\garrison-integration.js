const { ipcMain } = require('electron');
const axios = require('axios');
const path = require('path'); // Added missing import for path

class GarrisonIntegration {
  constructor(trayManager, store) {
    this.trayManager = trayManager;
    this.store = store;
    this.serverUrl = store.get('serverUrl', 'http://127.0.0.1:3001');
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000; // 5 seconds
    
    this.status = {
      online: false,
      unreadMessages: 0,
      activeChats: 0,
      notifications: 0,
      lastActivity: Date.now()
    };
    
    this.initialize();
  }

  initialize() {
    this.setupIPC();
    this.startConnection();
    this.startStatusPolling();
  }

  setupIPC() {
    // Handle garrison status updates from renderer
    ipcMain.handle('garrison-update-status', (event, status) => {
      this.updateStatus(status);
      return { success: true };
    });

    // Handle garrison notifications
    ipcMain.handle('garrison-notification', (event, notification) => {
      this.handleNotification(notification);
      return { success: true };
    });

    // Handle chat messages
    ipcMain.handle('garrison-chat-message', (event, message) => {
      this.handleChatMessage(message);
      return { success: true };
    });

    // Get current garrison status
    ipcMain.handle('garrison-get-status', () => {
      return this.status;
    });
  }

  startConnection() {
    this.connectToGarrison();
  }

  async connectToGarrison() {
    try {
      console.log('🏕️ Connecting to Garrison...');
      
      // Check if server is available
      const response = await axios.get(`${this.serverUrl}/api/health`, {
        timeout: 5000
      });
      
      if (response.status === 200) {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.status.online = true;
        console.log('✅ Connected to Garrison');
        
        // Update tray with new status
        this.trayManager.updateGarrisonStatus(this.status);
        
        // Start real-time updates
        this.startRealtimeUpdates();
      }
    } catch (error) {
      console.log('❌ Failed to connect to Garrison:', error.message);
      this.isConnected = false;
      this.status.online = false;
      this.trayManager.updateGarrisonStatus(this.status);
      
      // Attempt reconnection
      this.scheduleReconnect();
    }
  }

  scheduleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * this.reconnectAttempts;
      
      console.log(`🔄 Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay/1000}s`);
      
      setTimeout(() => {
        this.connectToGarrison();
      }, delay);
    } else {
      console.log('❌ Max reconnection attempts reached');
    }
  }

  startRealtimeUpdates() {
    // Poll for updates every 10 seconds
    setInterval(async () => {
      if (this.isConnected) {
        await this.fetchUpdates();
      }
    }, 10000);
  }

  async fetchUpdates() {
    try {
      const token = this.store.get('authToken');
      if (!token) return;

      const response = await axios.get(`${this.serverUrl}/api/garrison/status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 5000
      });

      if (response.data) {
        this.updateStatus(response.data);
      }
    } catch (error) {
      console.log('Failed to fetch garrison updates:', error.message);
      // Don't mark as disconnected for API errors, just log them
    }
  }

  startStatusPolling() {
    // Poll for basic status every 30 seconds
    setInterval(async () => {
      await this.checkBasicStatus();
    }, 30000);
  }

  async checkBasicStatus() {
    try {
      const token = this.store.get('authToken');
      if (!token) {
        this.status.online = false;
        this.trayManager.updateGarrisonStatus(this.status);
        return;
      }

      const response = await axios.get(`${this.serverUrl}/api/user/status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 3000
      });

      if (response.data) {
        this.status.online = response.data.online || false;
        this.trayManager.updateGarrisonStatus(this.status);
      }
    } catch (error) {
      // Silent fail for status checks
      this.status.online = false;
      this.trayManager.updateGarrisonStatus(this.status);
    }
  }

  updateStatus(newStatus) {
    this.status = { ...this.status, ...newStatus };
    this.status.lastActivity = Date.now();
    
    // Update tray manager
    this.trayManager.updateGarrisonStatus(this.status);
    
    console.log('🏕️ Garrison status updated:', this.status);
  }

  handleNotification(notification) {
    this.status.notifications++;
    this.status.lastActivity = Date.now();
    
    // Update tray
    this.trayManager.updateGarrisonStatus(this.status);
    
    // Show system notification if enabled
    if (this.store.get('showNotifications', true)) {
      this.showSystemNotification(notification);
    }
    
    console.log('🔔 Garrison notification:', notification);
  }

  handleChatMessage(message) {
    this.status.unreadMessages++;
    this.status.lastActivity = Date.now();
    
    // Update tray
    this.trayManager.updateGarrisonStatus(this.status);
    
    console.log('💬 Garrison chat message:', message);
  }

  showSystemNotification(notification) {
    const { Notification } = require('electron');
    
    if (Notification.isSupported()) {
      const systemNotification = new Notification({
        title: 'WC Arena Garrison',
        body: notification.message || 'New notification from Garrison',
        icon: path.join(__dirname, '..', 'assets', 'icon.png'),
        silent: false
      });
      
      systemNotification.show();
      
      // Auto-dismiss after 5 seconds
      setTimeout(() => {
        systemNotification.close();
      }, 5000);
    }
  }

  // Public methods for external access
  getStatus() {
    return this.status;
  }

  isOnline() {
    return this.status.online;
  }

  getUnreadCount() {
    return this.status.unreadMessages;
  }

  getNotificationCount() {
    return this.status.notifications;
  }

  // Clear notifications
  clearNotifications() {
    this.status.notifications = 0;
    this.trayManager.updateGarrisonStatus(this.status);
  }

  // Clear unread messages
  clearUnreadMessages() {
    this.status.unreadMessages = 0;
    this.trayManager.updateGarrisonStatus(this.status);
  }

  // Disconnect and cleanup
  disconnect() {
    this.isConnected = false;
    this.status.online = false;
    this.trayManager.updateGarrisonStatus(this.status);
    
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }

  // Reconnect manually
  reconnect() {
    this.reconnectAttempts = 0;
    this.connectToGarrison();
  }
}

module.exports = { GarrisonIntegration }; 