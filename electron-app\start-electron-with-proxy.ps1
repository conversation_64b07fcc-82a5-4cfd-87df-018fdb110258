Write-Host "========================================" -ForegroundColor Cyan
Write-Host "WC Arena Core - Electron with Proxy" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "[1/3] Installing dependencies..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "[2/3] Starting proxy server on port 3001..." -ForegroundColor Yellow
Start-Process -FilePath "node" -ArgumentList "electron-proxy.js" -WindowStyle Normal

Write-Host ""
Write-Host "[3/3] Waiting 3 seconds for proxy to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

Write-Host ""
Write-Host "[4/4] Starting Electron app..." -ForegroundColor Yellow
Start-Process -FilePath "npm" -ArgumentList "start" -WindowStyle Normal

Write-Host ""
Write-Host "✅ Both services started!" -ForegroundColor Green
Write-Host "📡 Proxy Server: http://127.0.0.1:3001" -ForegroundColor Cyan
Write-Host "🖥️  Electron App: WC Arena Core" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to stop all services..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Write-Host ""
Write-Host "🛑 Stopping all services..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "electron" -ErrorAction SilentlyContinue | Stop-Process -Force
Write-Host "✅ All services stopped." -ForegroundColor Green 