<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- DEVELOPMENT MODE CSP: TEMPORARILY DISABLED for testing. DO NOT USE IN PRODUCTION! -->
  <!-- <meta http-equiv="Content-Security-Policy" content="
    default-src 'self' http://127.0.0.1:3001 http://localhost:3001 https://warcraftarena.com;
    script-src 'self' 'unsafe-inline' 'unsafe-eval' http://127.0.0.1:3001 http://localhost:3001 https://warcraftarena.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://*.cloudflare.com https://*.jsdelivr.net;
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://*.cloudflare.com https://*.jsdelivr.net;
    font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://*.cloudflare.com https://*.jsdelivr.net;
    img-src 'self' data: https://warcraftarena.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://*.cloudflare.com https://*.jsdelivr.net;
    connect-src 'self' http://127.0.0.1:3001 http://localhost:3001 https://warcraftarena.com wss://warcraftarena.com https://*.googleapis.com https://*.google.com https://*.cloudflare.com https://*.jsdelivr.net;
    frame-src 'self' https://www.youtube.com;
  "> -->
  <!-- CSP temporarily disabled for Square payment testing -->
  <!-- <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://sandbox.web.squarecdn.com https://web.squarecdn.com https://pay.google.com https://js-sandbox.squarecdn.com https://js.squarecdn.com https://js.afterpay.com https://js-sandbox.afterpay.com https://o160250.ingest.sentry.io; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com https://sandbox.web.squarecdn.com https://web.squarecdn.com; font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com https://square-fonts-production-f.squarecdn.com https://d1g145x70srn7h.cloudfront.net; img-src 'self' data: https:; connect-src 'self' https://*.googleapis.com https://*.google.com https://connect.squareupsandbox.com https://connect.squareup.com https://pci-connect.squareupsandbox.com https://pci-connect.squareup.com https://pay.google.com https://google.com/pay https://*.afterpay.com https://*.clearpay.co.uk https://o160250.ingest.sentry.io https://*.ingest.sentry.io; frame-src 'self' https://www.youtube.com https://sandbox.web.squarecdn.com https://web.squarecdn.com https://pay.google.com https://*.afterpay.com https://*.clearpay.co.uk;"> -->
  <title>WC Arena - Epic PvP Battles Await</title>
  
  <!-- Preload critical resources -->
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
<link rel="stylesheet" href="/css/black-theme.css" />
<link rel="stylesheet" href="/css/player-modal-enhanced.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Universal Navbar Consistency - MUST be loaded last for proper override -->
  <link rel="stylesheet" href="/css/navbar-universal.css" />

  <style>
    .blacksmith-subtitle {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      margin-top: 1rem;
    }

    .subtitle-text {
      font-size: 1rem;
      color: rgba(255, 255, 255, 0.8);
      margin: 0;
      font-style: italic;
    }

    .hero-button {
      background: linear-gradient(135deg, var(--primary-gold), #E5C158);
      color: #000;
      text-decoration: none;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 600;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    }

    .hero-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
      color: #000;
      text-decoration: none;
    }

    @media (max-width: 768px) {
      .blacksmith-subtitle {
        gap: 0.75rem;
      }

      .hero-button {
        padding: 0.6rem 1.25rem;
        font-size: 0.9rem;
      }
    }
  </style>
</head>
<body>
  <div id="navbar-container"></div>

  <main>
    <!-- Page Title -->
    <div class="page-header">
      <h1 class="page-title" data-theme="blacksmith">⚒️ WC Arena Blacksmith</h1>
      <div class="blacksmith-subtitle">
        <p class="subtitle-text">Supported by heroes</p>
        <a href="/views/hero.html" class="hero-button" onclick="window.location.href='/views/hero.html'; return false;">
          <i class="fas fa-shield-alt"></i>
          Become a Hero
        </a>
      </div>
    </div>

    <!-- WC Arena Blacksmith Section -->
    <section class="glass-section fade-in-up">
      
      <!-- Tabs Navigation -->
      <div class="blacksmith-tabs">
        <button class="blacksmith-tab active" data-tab="current">
          <i class="fas fa-star"></i>
          A New Dawn - Launched July 2025
        </button>
        <button class="blacksmith-tab" data-tab="future">
          <i class="fas fa-rocket"></i>
          Fortification - 2.0
        </button>
        <button class="blacksmith-tab" data-tab="poll">
          <i class="fas fa-vote-yea"></i>
          Community Poll
        </button>
        <button class="blacksmith-tab" data-tab="feedback">
          <i class="fas fa-bug"></i>
          Feedback & Reports
        </button>
      </div>

      <!-- Tab Content -->
      <div class="blacksmith-content">
        <!-- A New Dawn - 1.0 Tab -->
        <div class="tab-panel active" id="tab-current">
          
          <div class="features-section">
            
            <div class="features-grid">
              <div class="feature-card detailed">
                <i class="fas fa-trophy feature-icon"></i>
                <h4>Arena</h4>
                <p>Ladders/Tournaments for all 3 warcraft RTS games, with ELO Based ranking system for all game modes, including vs AI on all maps. This robust system includes achievements, match history and advanced stats for all game modes and relies on screenshots, which are stored historically with advanced image compression.</p>
              </div>

              <div class="feature-card detailed">
                <i class="fas fa-map feature-icon"></i>
                <h4>Atlas</h4>
                <p>Integrated with the Arena, this advanced map program allows users to upload and download maps and does thorough advanced parsing of uploaded data to display them in a very informative and friendly way.</p>
              </div>

              <div class="feature-card detailed">
                <i class="fas fa-broadcast-tower feature-icon"></i>
                <h4>Watch Tower</h4>
                <p>Content creators can show off their WC-related twitch and youtube channels to provide all users a unified source for our favorite games.</p>
              </div>

              <div class="feature-card detailed">
                <i class="fas fa-crown feature-icon"></i>
                <h4>Hero System</h4>
                <p>A way to show off your support for this fan-driven project with beautiful Heroic Profiles among other features.</p>
              </div>

              <div class="feature-card detailed">
                <i class="fas fa-comments feature-icon"></i>
                <h4>Garrison</h4>
                <p>Voice/Text live chat with other users, providing all of the social features you could want including D&D dice rolls/Role playing features as well as unlockable emojis.</p>
              </div>

              <div class="feature-card detailed">
                <i class="fas fa-scroll feature-icon"></i>
                <h4>Stone Tablet</h4>
                <p>A way to post videos/comments/thoughts about the WC world in general, while also being able to keep up to speed with various WC related subreddits in one place.</p>
              </div>

              <div class="feature-card detailed">
                <i class="fas fa-chess-board feature-icon"></i>
                <h4>War Table</h4>
                <p>Achievement system for all WC-related campaigns so you can track your progress, level up and earn Arena Gold at the same time.</p>
              </div>

              <div class="feature-card detailed">
                <i class="fas fa-university feature-icon"></i>
                <h4>Town Hall</h4>
                <p>Your home, where you can keep track of all of your Arena players, your Atlas maps, your Watch Tower channels, Manage your clan, tell the world about yourself, and redecorate it to make it look personalized, as well as track your achievements.</p>
              </div>

              <div class="feature-card detailed">
                <i class="fas fa-desktop feature-icon"></i>
                <h4>Arena Core</h4>
                <p>Beta version of desktop application - monitors and scans for WC related games on your system, provides a simplified screenshot folder system and provides automated reporting on the Arena system with managed screenshots taken. Also works as a system tray for easy access to the WC Arena.</p>
                <a href="/views/arena-core.html" class="btn btn-primary" style="margin-top: 10px;">
                  <i class="fas fa-external-link-alt"></i> Open Arena Core
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Fortification - 2.0 Tab -->
        <div class="tab-panel" id="tab-future">
          <h3 class="tab-title">Upcoming Features - Fortification 2.0</h3>
          <div class="features-grid">
            <div class="feature-card future">
              <i class="fas fa-robot feature-icon"></i>
              <h4>AI Match Analysis</h4>
              <p>Automated replay analysis and suggestions</p>
            </div>
            <div class="feature-card future">
              <i class="fas fa-mobile-alt feature-icon"></i>
              <h4>Mobile Companion</h4>
              <p>Manage your profile on the go</p>
            </div>
            <div class="feature-card future">
              <i class="fas fa-video feature-icon"></i>
              <h4>Replay System</h4>
              <p>Watch and share match replays</p>
            </div>
            <div class="feature-card future">
              <i class="fas fa-gift feature-icon"></i>
              <h4>Seasonal Rewards</h4>
              <p>Exclusive rewards for active players</p>
            </div>
            <div class="feature-card future">
              <i class="fas fa-globe feature-icon"></i>
              <h4>Regional Rankings</h4>
              <p>Compete on a global scale</p>
            </div>
            <div class="feature-card future">
              <i class="fas fa-calendar feature-icon"></i>
              <h4>Tournament Scheduler</h4>
              <p>Automated tournament organization</p>
            </div>
          </div>
        </div>

        <!-- Community Poll Tab -->
        <div class="tab-panel" id="tab-poll">
          <h3 class="tab-title">Community Voice</h3>
          <div class="poll-container-large">
            <div class="poll-question">Is my wife going to divorce me?</div>
            <div class="poll-options" id="poll-options">
              <div class="poll-option" data-option="yes">
                <div class="poll-option-label">Yes</div>
                <div class="poll-option-bar">
                  <div class="poll-option-progress"></div>
                </div>
                <div class="poll-option-stats">
                  <span class="poll-option-percentage">0%</span>
                  <span class="poll-option-votes">0 votes</span>
                </div>
              </div>
              <div class="poll-option" data-option="no">
                <div class="poll-option-label">No</div>
                <div class="poll-option-bar">
                  <div class="poll-option-progress"></div>
                </div>
                <div class="poll-option-stats">
                  <span class="poll-option-percentage">0%</span>
                  <span class="poll-option-votes">0 votes</span>
                </div>
              </div>
            </div>
            <div class="poll-results hidden" id="poll-results">
              <p class="poll-results-text">
                Thanks for voting! You can click any option to change your vote.
              </p>
              <a href="/views/hero.html" class="save-marriage-btn hidden" id="save-marriage-btn">
                <i class="fas fa-heart"></i>
                Save My Marriage
              </a>
            </div>
          </div>
        </div>

        <!-- Feedback & Reports Tab -->
        <div class="tab-panel" id="tab-feedback">
          <h3 class="tab-title">Feedback & Bug Reports</h3>
          <div class="feedback-container-large">
            <form id="feedback-form" class="feedback-form">
              <div class="form-row">
                <div class="form-group">
                  <label for="feedback-type">Type</label>
                  <select id="feedback-type" name="type" required>
                    <option value="">Select type...</option>
                    <option value="bug">🐛 Bug Report</option>
                    <option value="feedback">💭 General Feedback</option>
                    <option value="suggestion">💡 Suggestion</option>
                    <option value="complaint">😤 Complaint</option>
                    <option value="other">📝 Other</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="feedback-category">Category</label>
                  <select id="feedback-category" name="category">
                    <option value="other">Other</option>
                    <option value="ui">🖥️ User Interface</option>
                    <option value="gameplay">🎮 Gameplay</option>
                    <option value="performance">⚡ Performance</option>
                    <option value="account">👤 Account</option>
                    <option value="ladder">🏆 Ladder</option>
                    <option value="chat">💬 Chat</option>
                    <option value="maps">🗺️ Maps</option>
                    <option value="tournaments">🏅 Tournaments</option>
                  </select>
                </div>
              </div>

              <div class="form-group">
                <label for="feedback-subject">Subject</label>
                <input type="text" id="feedback-subject" name="subject" maxlength="200" required 
                       placeholder="Brief description of your feedback">
              </div>

              <div class="form-group">
                <label for="feedback-description">Description</label>
                <textarea id="feedback-description" name="description" maxlength="2000" required 
                          placeholder="Detailed description of your feedback, bug report, or suggestion..."></textarea>
              </div>

              <div class="form-group">
                <label class="checkbox-label">
                  <input type="checkbox" id="feedback-contact" name="contact">
                  I'd like to be contacted about this feedback
                </label>
              </div>

              <div class="form-group contact-info hidden" id="contact-info">
                <label for="contact-method">Contact Method</label>
                <select id="contact-method" name="contactMethod">
                  <option value="none">Don't contact me</option>
                  <option value="email">Email</option>
                  <option value="discord">Discord</option>
                </select>
                <input type="text" id="contact-details" name="contactDetails" 
                       placeholder="Your email or Discord username" class="contact-input-spacing">
              </div>

              <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-paper-plane"></i> Send Feedback
                </button>
              </div>
            </form>
            
            <div id="feedback-success" class="feedback-message success hidden">
              <i class="fas fa-check-circle"></i>
              <span>Thank you for your feedback! We'll review it shortly.</span>
            </div>
            
            <div id="feedback-error" class="feedback-message error hidden">
              <i class="fas fa-exclamation-triangle"></i>
              <span>Failed to send feedback. Please try again.</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Become a Hero Membership Section -->
    <!-- Removed hero-section for separation -->
  </main>



  <div id="footer-container"></div>

  <!-- Scripts -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
  <script src="/js/api-config.js"></script>
  <script type="module" src="/js/app.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/navbar-debug.js"></script>
  
  <script src="/js/utils.js"></script>

  <script>
    // Blacksmith Tabs functionality - Enhanced
    function initBlacksmithTabs() {
      console.log('Initializing Blacksmith tabs...');
      
      const tabs = document.querySelectorAll('.blacksmith-tab');
      const panels = document.querySelectorAll('.tab-panel');
      
      console.log('Found tabs:', tabs.length);
      console.log('Found panels:', panels.length);
      
      if (tabs.length === 0) {
        console.error('No tabs found!');
        return;
      }
      
      tabs.forEach((tab, index) => {
        console.log(`Setting up tab ${index}:`, tab.getAttribute('data-tab'));
        
        tab.addEventListener('click', (e) => {
          e.preventDefault();
          console.log('Tab clicked:', tab.getAttribute('data-tab'));
          
          const targetTab = tab.getAttribute('data-tab');
          const targetPanel = document.getElementById(`tab-${targetTab}`);
          
          if (!targetPanel) {
            console.error('Target panel not found:', `tab-${targetTab}`);
            return;
          }
          
          // Remove active class from all tabs and panels
          tabs.forEach(t => {
            t.classList.remove('active');
            console.log('Removed active from tab');
          });
          panels.forEach(p => {
            p.classList.remove('active');
            console.log('Removed active from panel');
          });
          
          // Add active class to clicked tab and corresponding panel
          tab.classList.add('active');
          targetPanel.classList.add('active');
          
          console.log('Activated tab and panel:', targetTab);
        });
      });
    }
    
    // Initialize when DOM is fully loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initBlacksmithTabs);
    } else {
      initBlacksmithTabs();
    }
    
    // Backup initialization with setTimeout
    setTimeout(initBlacksmithTabs, 1000);

    function showVersion(version) {
      // Update active button
      document.querySelectorAll('.version-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-version="${version}"]`).classList.add('active');
      
      // Update title
      const title = document.getElementById('version-title');
      if (version === '1.0') {
        title.innerHTML = '🔥 Version 1.0';
      } else {
        title.innerHTML = '🚀 Upcoming v1.1';
      }
      
      // Show correct features
      document.querySelectorAll('.version-features').forEach(features => {
        features.classList.remove('active');
        features.style.display = 'none';
      });
      
      const targetFeatures = document.getElementById(`features-${version}`);
      targetFeatures.classList.add('active');
      targetFeatures.style.display = 'block';
    }

    // Legacy function for other collapsible sections
    function toggleCollapsible(header) {
      const content = header.nextElementSibling;
      const icon = header.querySelector('i');
      
      content.classList.toggle('expanded');
      
      if (content.classList.contains('expanded')) {
        icon.style.transform = 'rotate(180deg)';
      } else {
        icon.style.transform = 'rotate(0deg)';
      }
    }

    // Intersection Observer for animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.animationDelay = `${Math.random() * 0.5}s`;
          entry.target.classList.add('fade-in-up');
        }
      });
    }, observerOptions);

    // Observe all sections
    document.addEventListener('DOMContentLoaded', () => {
      document.querySelectorAll('.glass-section').forEach(section => {
        observer.observe(section);
      });
    });

    /**
     * Update poll display with current data
     */
    function updatePollDisplay(pollData) {
      const pollOptions = document.querySelectorAll('.poll-option');
      if (!pollOptions.length) return;

      const total = pollData.yes + pollData.no;
      if (total > 0) {
        const yesPercent = Math.round((pollData.yes / total) * 100);
        const noPercent = Math.round((pollData.no / total) * 100);

        // Update yes option
        const yesOption = document.querySelector('.poll-option[data-option="yes"]');
        if (yesOption) {
          yesOption.querySelector('.poll-option-progress').style.width = `${yesPercent}%`;
          yesOption.querySelector('.poll-option-percentage').textContent = `${yesPercent}%`;
          yesOption.querySelector('.poll-option-votes').textContent = `${pollData.yes} votes`;
        }

        // Update no option
        const noOption = document.querySelector('.poll-option[data-option="no"]');
        if (noOption) {
          noOption.querySelector('.poll-option-progress').style.width = `${noPercent}%`;
          noOption.querySelector('.poll-option-percentage').textContent = `${noPercent}%`;
          noOption.querySelector('.poll-option-votes').textContent = `${pollData.no} votes`;
        }
      }
    }

    /**
     * Show poll results
     */
    function showResults() {
      const pollResults = document.getElementById('poll-results');
      if (!pollResults) return;
      
      pollResults.classList.remove('hidden');

      // Keep options clickable to allow vote changing
      const pollOptions = document.querySelectorAll('.poll-option');
      pollOptions.forEach(option => {
        option.style.cursor = 'pointer';
        option.classList.add('voted');
        option.style.opacity = '0.9'; // Less transparent to show they're still interactive
        
        // Add hover effect to indicate interactivity
        option.style.transition = 'opacity 0.2s ease';
        option.addEventListener('mouseenter', () => {
          option.style.opacity = '1';
        });
        option.addEventListener('mouseleave', () => {
          option.style.opacity = '0.9';
        });
      });
    }

    /**
     * Highlight the user's current vote
     */
    function highlightUserVote(userVote) {
      const pollOptions = document.querySelectorAll('.poll-option');
      pollOptions.forEach(option => {
        if (option.dataset.option === userVote) {
          option.style.border = '2px solid #ffd700';
          option.style.backgroundColor = 'rgba(255, 215, 0, 0.1)';
          option.style.boxShadow = '0 0 10px rgba(255, 215, 0, 0.3)';
        } else {
          option.style.border = '';
          option.style.backgroundColor = '';
          option.style.boxShadow = '';
        }
      });
    }

    /**
     * Show vote success message
     */
    function showVoteMessage(message) {
      // Create or update message element
      let messageEl = document.getElementById('vote-message');
      if (!messageEl) {
        messageEl = document.createElement('div');
        messageEl.id = 'vote-message';
        messageEl.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: rgba(40, 167, 69, 0.9);
          color: white;
          padding: 12px 20px;
          border-radius: 6px;
          font-weight: 600;
          z-index: 1000;
          transform: translateX(100%);
          transition: transform 0.3s ease;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        `;
        document.body.appendChild(messageEl);
      }
      
      messageEl.textContent = message;
      
      // Show message
      setTimeout(() => {
        messageEl.style.transform = 'translateX(0)';
      }, 100);
      
      // Hide message after 3 seconds
      setTimeout(() => {
        messageEl.style.transform = 'translateX(100%)';
      }, 3000);
    }

    /**
     * Show the "Save My Marriage" button after voting
     */
    function showSaveMarriageButton() {
      console.log('💔 Showing Save My Marriage button...');
      
      const saveBtn = document.getElementById('save-marriage-btn');
      if (saveBtn) {
        saveBtn.classList.remove('hidden');
        console.log('💖 Save Marriage button is now visible and links to hero page');
      } else {
        console.error('❌ Save Marriage button not found in DOM');
      }
    }

    /**
     * Check if user is authenticated
     */
    async function checkAuthState() {
      try {
        const response = await fetch('/api/users/me', {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...(window.getAuthHeaders ? window.getAuthHeaders() : {})
          }
        });
        
        if (response.ok) {
          const user = await response.json();
          console.log('✅ User authenticated:', user.username || 'Anonymous');
          return { authenticated: true, user };
        } else {
          console.log('❌ User not authenticated');
          return { authenticated: false, user: null };
        }
      } catch (err) {
        console.error('🔥 Auth check failed:', err);
        return { authenticated: false, user: null };
      }
    }

    /**
     * Initialize poll functionality with enhanced Electron support
     */
    async function initializePoll() {
      console.log('🗳️ Initializing poll system...');
      
      let pollData = {
        yes: 0,
        no: 0,
        hasVoted: false,
        userVote: null
      };

      // Check authentication first
      const authState = await checkAuthState();
      
      try {
        console.log('📊 Loading poll data...');
        const response = await fetch('/api/poll/divorce', {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            ...(window.getAuthHeaders ? window.getAuthHeaders() : {})
          }
        });

        console.log(`📡 Poll data response status: ${response.status}`);

        if (response.ok) {
          const data = await response.json();
          console.log('📊 Poll data received:', data);
          
          if (data && data.options) {
            pollData = {
              yes: data.options.find(opt => opt.value === 'yes')?.votes || 0,
              no: data.options.find(opt => opt.value === 'no')?.votes || 0,
              hasVoted: data.hasVoted || false,
              userVote: data.userVote || null
            };
          }
        } else {
          console.warn('⚠️ Poll data request failed:', response.statusText);
        }
      } catch (err) {
        console.error('🔥 Error loading poll data:', err);
      }

      // Update the display with current data
      updatePollDisplay(pollData);

      // Show results if user has already voted (but still allow vote changing)
      if (pollData.hasVoted && pollData.userVote) {
        showResults();
        highlightUserVote(pollData.userVote);
        showSaveMarriageButton(); // Show button for users who already voted
      }

      // Add click handlers for voting (allow vote changing)
      const pollOptions = document.querySelectorAll('.poll-option');
      console.log(`🎯 Setting up ${pollOptions.length} poll option handlers`);
      
      pollOptions.forEach(option => {
        option.addEventListener('click', async () => {
          const vote = option.dataset.option;
          console.log(`🗳️ User clicked: ${vote}`);

          // In Electron, try to vote regardless of initial auth check
          // The server will handle authentication properly
          console.log('🚀 Proceeding with vote attempt...');

          try {
            console.log(`📡 Sending vote: ${vote}`);
            
            const votePayload = { 
              poll: 'divorce', 
              vote: vote 
            };
            
            console.log('📤 Vote payload:', votePayload);
            
            const response = await fetch('/api/poll/vote', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...(window.getAuthHeaders ? window.getAuthHeaders() : {})
              },
              body: JSON.stringify(votePayload),
              credentials: 'include'
            });

            console.log(`📡 Vote response status: ${response.status}`);

            if (!response.ok) {
              const errorText = await response.text();
              console.error('❌ Vote failed:', response.status, errorText);
              
              if (response.status === 401 || response.status === 403) {
                showVoteMessage('Please log in to vote');
                console.warn('⚠️ Authentication required for voting');
              } else {
                showVoteMessage(`Vote failed: ${response.status}`);
              }
              
              throw new Error(`Failed to save vote: ${response.status} ${errorText}`);
            }

            const data = await response.json();
            console.log('✅ Vote response:', data);
            
            if (data.poll && data.poll.options) {
              pollData = {
                yes: data.poll.options.find(opt => opt.value === 'yes')?.votes || 0,
                no: data.poll.options.find(opt => opt.value === 'no')?.votes || 0,
                hasVoted: true,
                userVote: vote
              };
              updatePollDisplay(pollData);
              showResults();
              
              // Highlight the user's current vote
              highlightUserVote(vote);
              
              // Show success message
              const message = data.message || 'Vote recorded successfully';
              showVoteMessage(message);
              
              // Show "Save my marriage" button after successful vote
              showSaveMarriageButton();
              
              console.log('✅ Vote processed successfully');
            } else {
              console.error('❌ Invalid vote response data:', data);
              showVoteMessage('Invalid response from server');
            }
          } catch (err) {
            console.error('🔥 Error sending vote to server:', err);
            showVoteMessage('Failed to record vote. Please try again.');
          }
        });
      });
      
      console.log('✅ Poll system initialized');
    }

    /**
     * Initialize tab switching functionality
     */
    function initializeTabs() {
      console.log('🔧 Initializing tab system...');
      
      const tabButtons = document.querySelectorAll('.blacksmith-tab');
      const tabPanels = document.querySelectorAll('.tab-panel');
      
      console.log(`📊 Found ${tabButtons.length} tab buttons and ${tabPanels.length} tab panels`);
      
      if (tabButtons.length === 0 || tabPanels.length === 0) {
        console.error('❌ Tab elements not found');
        return;
      }
      
      // Hide all panels except the first one
      tabPanels.forEach((panel, index) => {
        if (index === 0) {
          panel.classList.add('active');
          panel.style.display = 'block';
        } else {
          panel.classList.remove('active');
          panel.style.display = 'none';
        }
      });
      
      // Set first tab as active
      tabButtons.forEach((button, index) => {
        if (index === 0) {
          button.classList.add('active');
        } else {
          button.classList.remove('active');
        }
      });
      
      // Add click handlers
      tabButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          e.preventDefault();
          const targetTab = button.dataset.tab;
          
          console.log(`🔄 Switching to tab: ${targetTab}`);
          
          // Remove active class from all buttons and panels
          tabButtons.forEach(btn => btn.classList.remove('active'));
          tabPanels.forEach(panel => {
            panel.classList.remove('active');
            panel.style.display = 'none';
          });
          
          // Add active class to clicked button
          button.classList.add('active');
          
          // Show the corresponding panel
          const targetPanel = document.getElementById(`tab-${targetTab}`);
          if (targetPanel) {
            targetPanel.classList.add('active');
            targetPanel.style.display = 'block';
            console.log(`✅ Tab ${targetTab} activated successfully`);
          } else {
            console.error(`❌ Target panel tab-${targetTab} not found`);
          }
        });
      });
      
      console.log('✅ Tab system initialized successfully');
    }

    /**
     * Initialize feedback form functionality
     */
    function initializeFeedbackForm() {
      console.log('📝 Initializing feedback form...');
      
      const feedbackForm = document.getElementById('feedback-form');
      const feedbackSuccess = document.getElementById('feedback-success');
      const feedbackError = document.getElementById('feedback-error');
      const contactCheckbox = document.getElementById('feedback-contact');
      const contactInfo = document.getElementById('contact-info');
      
      if (!feedbackForm) {
        console.warn('⚠️ Feedback form not found');
        return;
      }
      
      // Handle contact preference toggle
      if (contactCheckbox && contactInfo) {
        contactCheckbox.addEventListener('change', () => {
          if (contactCheckbox.checked) {
            contactInfo.classList.remove('hidden');
          } else {
            contactInfo.classList.add('hidden');
          }
        });
      }
      
      // Handle form submission
      feedbackForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        console.log('📤 Submitting feedback form...');
        
        // Hide previous messages
        if (feedbackSuccess) feedbackSuccess.classList.add('hidden');
        if (feedbackError) feedbackError.classList.add('hidden');
        
        // Get form data
        const formData = new FormData(feedbackForm);
        const feedbackData = {
          type: formData.get('type'),
          category: formData.get('category'),
          subject: formData.get('subject'),
          description: formData.get('description'),
          contact: formData.get('contact') === 'on'
        };
        
        // Add contact details if contact is requested
        if (feedbackData.contact) {
          const contactMethod = formData.get('contactMethod');
          const contactDetails = formData.get('contactDetails');
          
          feedbackData.contact = {
            preferredMethod: contactMethod,
            email: contactMethod === 'email' ? contactDetails : '',
            discord: contactMethod === 'discord' ? contactDetails : ''
          };
        }
        
        console.log('📋 Feedback data:', feedbackData);
        
        try {
          // Submit feedback
          const response = await fetch('/api/feedback/submit', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            body: JSON.stringify(feedbackData),
            credentials: 'include'
          });
          
          const result = await response.json();
          console.log('📡 Feedback response:', result);
          
          if (response.ok) {
            // Show detailed success message
            showFeedbackSuccess(feedbackData, result.feedbackId);
            
            // Reset form
            feedbackForm.reset();
            if (contactInfo) contactInfo.classList.add('hidden');
            
            console.log('✅ Feedback submitted successfully');
          } else {
            throw new Error(result.message || 'Failed to submit feedback');
          }
        } catch (error) {
          console.error('❌ Error submitting feedback:', error);
          showFeedbackError(error.message);
        }
      });
      
      console.log('✅ Feedback form initialized');
    }
    
    /**
     * Show detailed feedback success message with summary
     */
    function showFeedbackSuccess(feedbackData, feedbackId) {
      const feedbackSuccess = document.getElementById('feedback-success');
      if (!feedbackSuccess) return;
      
      // Get type emoji and label
      const typeMap = {
        bug: '🐛 Bug Report',
        feedback: '💭 General Feedback',
        suggestion: '💡 Suggestion',
        complaint: '😤 Complaint',
        other: '📝 Other'
      };
      
      // Get category label
      const categoryMap = {
        ui: '🖥️ User Interface',
        gameplay: '🎮 Gameplay',
        performance: '⚡ Performance',
        account: '👤 Account',
        ladder: '🏆 Ladder',
        chat: '💬 Chat',
        maps: '🗺️ Maps',
        tournaments: '🏅 Tournaments',
        other: 'Other'
      };
      
      const typeLabel = typeMap[feedbackData.type] || feedbackData.type;
      const categoryLabel = categoryMap[feedbackData.category] || feedbackData.category;
      const contactText = feedbackData.contact ? 
        ` We'll contact you via ${feedbackData.contactMethod || 'your preferred method'}.` : 
        '';
      
      // Create detailed success message
      const successHTML = `
        <div class="feedback-success-content">
          <div class="feedback-success-header">
            <i class="fas fa-check-circle"></i>
            <h4>Feedback Submitted Successfully!</h4>
          </div>
          <div class="feedback-summary">
            <p><strong>Type:</strong> ${typeLabel}</p>
            <p><strong>Category:</strong> ${categoryLabel}</p>
            <p><strong>Subject:</strong> "${feedbackData.subject}"</p>
            <p><strong>Reference ID:</strong> #${feedbackId?.substring(0, 8) || 'UNKNOWN'}</p>
          </div>
          <div class="feedback-success-footer">
            <p>Thank you for your ${feedbackData.type}! We'll review it shortly and take appropriate action.${contactText}</p>
          </div>
        </div>
      `;
      
      feedbackSuccess.innerHTML = successHTML;
      feedbackSuccess.classList.remove('hidden');
      
      // Scroll to message
      feedbackSuccess.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      
      // Auto-hide after 10 seconds
      setTimeout(() => {
        feedbackSuccess.classList.add('hidden');
      }, 10000);
    }
    
    /**
     * Show feedback error message
     */
    function showFeedbackError(errorMessage) {
      const feedbackError = document.getElementById('feedback-error');
      if (!feedbackError) return;
      
      const errorHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        <span>Failed to submit feedback: ${errorMessage || 'Please try again.'}</span>
      `;
      
      feedbackError.innerHTML = errorHTML;
      feedbackError.classList.remove('hidden');
      
      // Scroll to message
      feedbackError.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      
      // Auto-hide after 8 seconds
      setTimeout(() => {
        feedbackError.classList.add('hidden');
      }, 8000);
    }

    // Wait for DOM to load before initializing components
    window.addEventListener('load', async () => {
      console.log('🔄 Page fully loaded, initializing components...');
      
      // Initialize tabs first
      initializeTabs();
      
      // Initialize all components
      await Promise.all([
        initializePoll(),
        initializeFeedbackForm()
      ]).catch(error => console.error('Error initializing components:', error));
      
      // Load unified navigation and update profile
      console.log('🔄 Initializing navigation on index page...');

      // Load unified navigation
      if (typeof window.loadNavigation === 'function') {
        await window.loadNavigation();
      } else if (typeof window.loadNavbar === 'function') {
        await window.loadNavbar();
      }

      // Update navbar profile with reduced delay
      setTimeout(async () => {
        if (window.updateNavbarProfileUnified) {
          console.log('🔄 Updating navbar profile (unified) on index page');
          await window.updateNavbarProfileUnified();
        } else if (window.updateNavbarProfile) {
          console.log('🔄 Updating navbar profile (legacy) on index page');
          await window.updateNavbarProfile();
        }
      }, 500); // Reduced delay for better performance
    });
  </script>
</body>
</html>
