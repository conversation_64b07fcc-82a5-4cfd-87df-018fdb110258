/**
 * WC1LadderManager.js - Warcraft I vs AI Arena Manager
 * 
 * Specialized manager for WC1 vs AI matches with simplified interface:
 * - Auto-creates player with user's username
 * - Only vs AI matches (no other game types)
 * - Simplified map selection from 21 scenarios
 * - Integrated MMR/records system
 * - Map-specific win percentage charts
 */

export class WC1LadderManager {
  constructor() {
    this.currentUser = null;
    this.wc1Player = null;
    this.scenarios = [];
    this.initialized = false;
    this.isActive = false;
    this.currentMatchType = 'vsai'; // Default to vs AI
    this.searchQuery = ''; // Player search query
    this.currentPage = 1;
    this.totalPages = 1;
    
    // WC1 specific configuration
    this.wc1Config = {
      gameType: 'warcraft1',
      matchType: 'vsai',
      races: ['human', 'orc'],
      categories: ['forest', 'swamp', 'dungeon']
    };

    // Don't listen to gameTabChanged anymore - let direct tab handlers manage this

    this.init();
  }

  /**
   * Initialize the WC1 ladder manager
   */
  async init() {
    console.log('🗡️ Initializing WC1 Ladder Manager...');
    
    try {
      // Load scenarios
      await this.loadScenarios();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Load current user
      await this.loadCurrentUser();
      
      this.initialized = true;
      console.log('✅ WC1 Ladder Manager initialized');
      
    } catch (error) {
      console.error('❌ Failed to initialize WC1 Ladder Manager:', error);
    }
  }

  /**
   * Load WC1 scenarios from API
   */
  async loadScenarios() {
    try {
      const response = await fetch('/api/wc1scenarios');
      if (response.ok) {
        this.scenarios = await response.json();
        console.log(`📋 Loaded ${this.scenarios.length} WC1 scenarios`);
      } else {
        // Fallback to hardcoded scenarios if API fails
        this.loadFallbackScenarios();
      }
    } catch (error) {
      console.warn('⚠️ Failed to load scenarios from API, using fallback:', error);
      this.loadFallbackScenarios();
    }
  }

  /**
   * Load fallback scenarios (hardcoded)
   */
  loadFallbackScenarios() {
    this.scenarios = [
      // Forest scenarios
      { name: 'Forest 1', category: 'forest' },
      { name: 'Forest 2', category: 'forest' },
      { name: 'Forest 3', category: 'forest' },
      { name: 'Forest 4', category: 'forest' },
      { name: 'Forest 5', category: 'forest' },
      { name: 'Forest 6', category: 'forest' },
      { name: 'Forest 7', category: 'forest' },
      
      // Swamp scenarios
      { name: 'Swamp 1', category: 'swamp' },
      { name: 'Swamp 2', category: 'swamp' },
      { name: 'Swamp 3', category: 'swamp' },
      { name: 'Swamp 4', category: 'swamp' },
      { name: 'Swamp 5', category: 'swamp' },
      { name: 'Swamp 6', category: 'swamp' },
      { name: 'Swamp 7', category: 'swamp' },
      
      // Dungeon scenarios
      { name: 'Dungeon 1', category: 'dungeon' },
      { name: 'Dungeon 2', category: 'dungeon' },
      { name: 'Dungeon 3', category: 'dungeon' },
      { name: 'Dungeon 4', category: 'dungeon' },
      { name: 'Dungeon 5', category: 'dungeon' },
      { name: 'Dungeon 6', category: 'dungeon' },
      { name: 'Dungeon 7', category: 'dungeon' }
    ];
    console.log(`📋 Loaded ${this.scenarios.length} fallback WC1 scenarios`);
  }

  /**
   * Load current user information
   */
  async loadCurrentUser() {
    try {
      const response = await fetch('/api/me', { credentials: 'include' });
      if (response.ok) {
        this.currentUser = await response.json();
        console.log(`👤 Current user loaded: ${this.currentUser.username}`);
        
        // Check if user already has a WC1 player
        await this.ensureWC1Player();
      }
    } catch (error) {
      console.error('❌ Failed to load current user:', error);
    }
  }

  /**
   * Ensure user has a WC1 player (auto-create if needed)
   */
  async ensureWC1Player() {
    if (!this.currentUser) return;

    try {
      // Check if user already has a WC1 player
      const response = await fetch(`/api/ladder/my-players?gameType=warcraft1`, {
        credentials: 'include'
      });
      
      if (response.ok) {
        const players = await response.json();
        const wc1Players = players.filter(p => p.gameType === 'warcraft1');
        
        if (wc1Players.length > 0) {
          this.wc1Player = wc1Players[0];
          console.log(`✅ Found existing WC1 player: ${this.wc1Player.playerName}`);
        } else {
          // Auto-create WC1 player with user's username
          await this.createWC1Player();
        }
      }
    } catch (error) {
      console.error('❌ Failed to check WC1 player:', error);
    }
  }

  /**
   * Auto-create WC1 player for the user
   */
  async createWC1Player() {
    if (!this.currentUser) return;

    try {
      const response = await fetch('/api/ladder/players', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          playerName: this.currentUser.username,
          gameType: 'warcraft1',
          preferredRace: 'human', // Default race
          autoCreated: true
        })
      });

      if (response.ok) {
        this.wc1Player = await response.json();
        console.log(`✅ Auto-created WC1 player: ${this.wc1Player.playerName}`);
      } else {
        console.error('❌ Failed to create WC1 player:', await response.text());
      }
    } catch (error) {
      console.error('❌ Error creating WC1 player:', error);
    }
  }

  /**
   * Setup event listeners for WC1 functionality
   */
  setupEventListeners() {
    console.log('🔧 Setting up WC1 event listeners...');
    
    // Don't set up report button listener here - let LadderManager handle it
    // and route to the appropriate manager based on active state
    
    // Set up modal events
    this.setupWC1ModalEvents();
    
    // Set up WC1 search functionality
    this.setupWC1SearchListeners();
    
    // Set up WC1 match type toggle
    this.setupWC1MatchTypeToggle();
    
    console.log('✅ WC1 event listeners set up');
  }

  /**
   * Activate WC1 mode (called when WC1 tab is clicked)
   */
  async activateWC1Mode() {
    console.log('🗡️ Activating WC1 Mode');
    this.isActive = true;
    
    // Show WC1 controls and hide WC2/WC3 controls
    this.showWC1Controls();
    
    // Set default match type to vs AI and initialize button states
    this.updateWC1MatchTypeButtons(this.currentMatchType);
    
    // Set default match type to vs AI
    this.currentMatchType = 'vsai';
    
    // Update active tab visually
    document.querySelectorAll('.game-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    
    const wc1Tab = document.querySelector('.game-tab[data-game-type="war1"]');
    if (wc1Tab) {
      wc1Tab.classList.add('active');
    }
    
    // Show WC1 controls, hide WC2/WC3 controls
    this.toggleControls('war1');
    
    // Update report button text
    this.updateReportButton();
    
    // Update page title
    document.title = 'WC Arena - WC1 Arena';
    
    // Hide WC2-specific statistics that don't apply to WC1
    this.hideWC2Statistics();
    
    // Load/update WC1 specific data
    console.log('🗡️ Calling updateWC1Stats from activateWC1Mode...');
    await this.updateWC1Stats();
    
    console.log('✅ WC1 Mode activated');
  }

  /**
   * Show WC1 controls and hide WC2/WC3 controls with consistent styling
   */
  showWC1Controls() {
    const wc1Controls = document.getElementById('wc1-controls');
    const wc2wc3Controls = document.getElementById('wc2-wc3-controls');
    
    if (wc1Controls) {
      wc1Controls.style.display = 'flex';
      
      // Ensure consistent styling through GameSwitchManager if available
      if (window.gameSwitchManager) {
        window.gameSwitchManager.applyConsistentControlsStyle(wc1Controls);
      }
    }
    
    if (wc2wc3Controls) {
      wc2wc3Controls.style.display = 'none';
    }
    
    console.log('✅ WC1 controls shown with consistent horizontal layout');
  }

  /**
   * Hide WC1 controls and show WC2/WC3 controls with consistent styling
   */
  hideWC1Controls() {
    const wc1Controls = document.getElementById('wc1-controls');
    const wc2wc3Controls = document.getElementById('wc2-wc3-controls');
    
    if (wc1Controls) {
      wc1Controls.style.display = 'none';
    }
    
    if (wc2wc3Controls) {
      wc2wc3Controls.style.display = 'flex';
      
      // Ensure consistent styling through GameSwitchManager if available
      if (window.gameSwitchManager) {
        window.gameSwitchManager.applyConsistentControlsStyle(wc2wc3Controls);
      }
    }
    
    console.log('✅ WC1 controls hidden, WC2/WC3 controls shown with horizontal layout');
  }

  /**
   * Hide WC2-specific statistics that don't apply to WC1 vs AI
   */
  hideWC2Statistics() {
    // Hide match type statistics (1v1, 2v2, etc.) since WC1 is only vs AI
    const matchTypeCard = document.querySelector('.stats-card:has(#match-type-chart)');
    if (matchTypeCard) {
      matchTypeCard.style.display = 'none';
    }
    
    // Hide rank distribution since WC1 is just starting
    const rankCard = document.querySelector('.stats-card:has(#rank-chart)');
    if (rankCard) {
      rankCard.style.display = 'none';
    }
    
    // Hide MMR distribution for now
    const mmrCard = document.querySelector('.stats-card:has(#mmr-chart)');
    if (mmrCard) {
      mmrCard.style.display = 'none';
    }
    
    // Hide activity chart for now
    const activityCard = document.querySelector('.stats-card:has(#activity-chart)');
    if (activityCard) {
      activityCard.style.display = 'none';
    }
    
    // Update race distribution title to be WC1-specific
    const raceCard = document.querySelector('.stats-card:has(#race-chart)');
    if (raceCard) {
      const title = raceCard.querySelector('h3');
      if (title) {
        title.textContent = 'WC1 Race Distribution';
      }
    }
    
    // Update popular maps title to be WC1-specific
    const mapsCard = document.querySelector('.stats-card:has(#maps-stats-list)');
    if (mapsCard) {
      const title = mapsCard.querySelector('h3');
      if (title) {
        title.textContent = 'Popular WC1 Scenarios';
      }
    }
  }

  /**
   * Show WC2-specific statistics when switching away from WC1
   */
  showWC2Statistics() {
    // Show all hidden statistics cards
    const hiddenCards = [
      '.stats-card:has(#match-type-chart)',
      '.stats-card:has(#rank-chart)', 
      '.stats-card:has(#mmr-chart)',
      '.stats-card:has(#activity-chart)'
    ];
    
    hiddenCards.forEach(selector => {
      const card = document.querySelector(selector);
      if (card) {
        card.style.display = '';
      }
    });
    
    // Reset race distribution title
    const raceCard = document.querySelector('.stats-card:has(#race-chart)');
    if (raceCard) {
      const title = raceCard.querySelector('h3');
      if (title) {
        title.textContent = 'Race Distribution';
      }
    }
    
    // Reset popular maps title
    const mapsCard = document.querySelector('.stats-card:has(#maps-stats-list)');
    if (mapsCard) {
      const title = mapsCard.querySelector('h3');
      if (title) {
        title.textContent = 'Popular Maps';
      }
    }
  }

  /**
   * Called when deactivating WC1 mode (switching to WC2/WC3)
   */
  onDeactivate() {
    console.log('🗡️ Deactivating WC1 Mode');
    this.isActive = false;
    
    // Hide WC1 controls and show WC2/WC3 controls
    this.hideWC1Controls();
    
    // Reset report button text for WC2/WC3
    this.resetReportButton();
    
    // Show WC2-specific statistics again
    this.showWC2Statistics();
    
    console.log('✅ WC1 Mode deactivated');
  }

  /**
   * Set up WC1 modal event listeners
   */
  setupWC1ModalEvents() {
    // Use more specific event handling to avoid conflicts
    
    // Handle WC1 modal form changes only within the modal
    const modal = document.getElementById('wc1-report-match-modal');
    if (modal) {
      // Handle race and match type changes within WC1 modal only
      modal.addEventListener('change', (e) => {
        if (e.target.name === 'race') {
          this.handleRaceSelection(e.target.value);
        }
        
        if (e.target.name === 'matchType') {
          this.handleMatchTypeSelection(e.target.value);
        }
        
        if (e.target.classList.contains('unit-slider')) {
          this.handleUnitSliderChange(e.target);
        }
      });
      
      // Handle form submission within WC1 modal only
      modal.addEventListener('submit', async (e) => {
        if (e.target.id === 'wc1-report-match-form') {
          e.preventDefault();
          await this.handleWC1MatchSubmission(e.target);
        }
      });
      
      // Handle WC1 modal close events
      modal.addEventListener('click', (e) => {
        // Close on background click
        if (e.target === modal) {
          e.preventDefault();
          e.stopPropagation();
          this.closeWC1ReportModal();
        }
        
        // Close on close button click
        if (e.target.classList.contains('close-wc1-modal') || 
            e.target.closest('.close-wc1-modal')) {
          e.preventDefault();
          e.stopPropagation();
          this.closeWC1ReportModal();
        }
      });
    }
    
    // Handle escape key only for WC1 modal
    document.addEventListener('keydown', (e) => {
      const wc1Modal = document.getElementById('wc1-report-match-modal');
      if (wc1Modal && wc1Modal.classList.contains('show') && e.key === 'Escape') {
        e.preventDefault();
        e.stopPropagation();
        this.closeWC1ReportModal();
      }
    });
    
    console.log('✅ WC1 modal events set up with specific handlers');
  }

  /**
   * Setup WC1 search listeners
   */
  setupWC1SearchListeners() {
    // Player search button
    const searchBtn = document.getElementById('wc1-search-btn');
    if (searchBtn) {
      searchBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.searchWC1Players();
      });
    }
    
    // Search input functionality
    const searchInput = document.getElementById('wc1-player-search');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        const value = e.target.value.trim();
        // Auto-search when input changes
        if (value === '') {
          this.searchQuery = '';
          this.currentPage = 1;
          this.loadWC1Leaderboard();
        }
      });
      
      searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          e.stopPropagation();
          this.searchWC1Players();
        }
      });
    }
    
    console.log('✅ WC1 search listeners set up');
  }

  /**
   * Setup WC1 match type toggle
   */
  setupWC1MatchTypeToggle() {
    // vs AI button
    const vsAIBtn = document.getElementById('wc1-vsai-btn');
    if (vsAIBtn) {
      vsAIBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.switchWC1MatchType('vsai');
      });
    }
    
    // 1v1 button
    const oneVOneBtn = document.getElementById('wc1-1v1-btn');
    if (oneVOneBtn) {
      oneVOneBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.switchWC1MatchType('1v1');
      });
    }
    
    console.log('✅ WC1 match type toggle set up');
  }

  /**
   * Search WC1 players
   */
  searchWC1Players() {
    const searchInput = document.getElementById('wc1-player-search');
    if (searchInput) {
      this.searchQuery = searchInput.value.trim();
      this.currentPage = 1; // Reset to first page
      this.loadWC1Leaderboard();
      console.log(`🔍 Searching WC1 players: "${this.searchQuery}"`);
    }
  }



  /**
   * Handle race selection and show/hide appropriate unit containers
   */
  handleRaceSelection(race) {
    console.log('🏰 Race selected:', race);
    
    const humanUnits = document.getElementById('human-units');
    const orcUnits = document.getElementById('orc-units');
    const unitValidation = document.getElementById('unit-validation');
    
    if (race === 'human') {
      if (humanUnits) humanUnits.style.display = 'block';
      if (orcUnits) orcUnits.style.display = 'none';
    } else if (race === 'orc') {
      if (humanUnits) humanUnits.style.display = 'none';
      if (orcUnits) orcUnits.style.display = 'block';
    }
    
    // Reset all unit sliders and hide validation
    this.resetAllUnitSliders();
    if (unitValidation) unitValidation.style.display = 'none';
  }

  /**
   * Handle match type selection (vs AI or 1v1)
   */
  handleMatchTypeSelection(matchType) {
    console.log('🎯 Match type selected:', matchType);
    
    // Update form title and labels based on match type
    const modalTitle = document.querySelector('#wc1-report-match-modal .modal-header h3');
    const formTitle = document.querySelector('#wc1-report-match-modal .form-section h4');
    const submitButton = document.querySelector('#wc1-report-match-modal button[type="submit"]');
    
    if (matchType === 'vsai') {
      if (modalTitle) modalTitle.textContent = 'Report WC1 Battle';
      if (formTitle) formTitle.textContent = 'Battle vs AI Details';
      if (submitButton) {
        submitButton.innerHTML = '<i class="fas fa-robot"></i> Submit Battle Report';
      }
    } else if (matchType === '1v1') {
      if (modalTitle) modalTitle.textContent = 'Report WC1 Match';
      if (formTitle) formTitle.textContent = '1v1 Match Details';
      if (submitButton) {
        submitButton.innerHTML = '<i class="fas fa-user"></i> Submit 1v1 Match';
      }
    }
    
    // Store current match type for form submission
    this.currentMatchType = matchType;
  }

  /**
   * Handle unit slider value changes
   */
  handleUnitSliderChange(slider) {
    const value = parseInt(slider.value);
    const unitCountDisplay = slider.parentNode.querySelector('.unit-count');
    
    if (unitCountDisplay) {
      unitCountDisplay.textContent = value === 0 ? 'Off' : value;
      unitCountDisplay.setAttribute('data-value', value);
      
      // Add pulse animation
      unitCountDisplay.classList.add('changed');
      setTimeout(() => {
        unitCountDisplay.classList.remove('changed');
      }, 300);
    }
    
    // Validate unit requirements
    this.validateUnitRequirements();
  }

  /**
   * Reset all unit sliders - updated for new minimum values
   */
  resetAllUnitSliders() {
    // Reset peasants and peons to 1 (minimum value)
    const peasantsSlider = document.getElementById('peasants');
    const peonsSlider = document.getElementById('peons');
    
    if (peasantsSlider) {
      peasantsSlider.value = 1;
      const unitCountDisplay = peasantsSlider.parentNode.querySelector('.unit-count');
      if (unitCountDisplay) {
        unitCountDisplay.textContent = '1';
        unitCountDisplay.setAttribute('data-value', '1');
      }
    }
    
    if (peonsSlider) {
      peonsSlider.value = 1;
      const unitCountDisplay = peonsSlider.parentNode.querySelector('.unit-count');
      if (unitCountDisplay) {
        unitCountDisplay.textContent = '1';
        unitCountDisplay.setAttribute('data-value', '1');
      }
    }
    
    // Reset all other units to 0
    const otherSliders = document.querySelectorAll('.unit-slider:not(#peasants):not(#peons)');
    otherSliders.forEach(slider => {
      slider.value = 0;
      const unitCountDisplay = slider.parentNode.querySelector('.unit-count');
      if (unitCountDisplay) {
        unitCountDisplay.textContent = 'Off';
        unitCountDisplay.setAttribute('data-value', '0');
      }
    });
  }

  /**
   * Validate unit requirements (at least 1 worker + 1 additional unit)
   */
  validateUnitRequirements() {
    const raceInputs = document.querySelectorAll('input[name="race"]');
    const selectedRace = Array.from(raceInputs).find(input => input.checked)?.value;
    
    if (!selectedRace) return true; // No race selected yet
    
    let workerCount = 0;
    let totalUnits = 0;
    
    if (selectedRace === 'human') {
      const peasantsSlider = document.getElementById('peasants');
      workerCount = peasantsSlider ? parseInt(peasantsSlider.value) : 1; // Default to 1
      
      // Count all human units
      const humanSliders = ['peasants', 'footmen', 'archers', 'knights', 'human-catapults', 'clerics'];
      totalUnits = humanSliders.reduce((sum, unitId) => {
        const slider = document.getElementById(unitId);
        return sum + (slider ? parseInt(slider.value) : 0);
      }, 0);
      
    } else if (selectedRace === 'orc') {
      const peonsSlider = document.getElementById('peons');
      workerCount = peonsSlider ? parseInt(peonsSlider.value) : 1; // Default to 1
      
      // Count all orc units
      const orcSliders = ['peons', 'grunts', 'spearmen', 'raiders', 'orc-catapults', 'necrolytes'];
      totalUnits = orcSliders.reduce((sum, unitId) => {
        const slider = document.getElementById(unitId);
        return sum + (slider ? parseInt(slider.value) : 0);
      }, 0);
    }
    
    // Since workers now start at 1, we need at least 1 additional unit (total >= 2)
    const isValid = workerCount >= 1 && totalUnits >= 2;
    const unitValidation = document.getElementById('unit-validation');
    
    if (unitValidation) {
      unitValidation.style.display = isValid ? 'none' : 'block';
    }
    
    return isValid;
  }

  /**
   * Get unit composition data from form
   */
  getUnitComposition() {
    const raceInputs = document.querySelectorAll('input[name="race"]');
    const selectedRace = Array.from(raceInputs).find(input => input.checked)?.value;
    
    if (!selectedRace) return null;
    
    const units = {};
    
    if (selectedRace === 'human') {
      const humanUnits = ['peasants', 'footmen', 'archers', 'knights', 'human-catapults', 'clerics'];
      humanUnits.forEach(unitId => {
        const slider = document.getElementById(unitId);
        if (slider) {
          const unitName = unitId === 'human-catapults' ? 'catapults' : unitId;
          units[unitName] = parseInt(slider.value);
        }
      });
    } else if (selectedRace === 'orc') {
      const orcUnits = ['peons', 'grunts', 'spearmen', 'raiders', 'orc-catapults', 'necrolytes'];
      orcUnits.forEach(unitId => {
        const slider = document.getElementById(unitId);
        if (slider) {
          const unitName = unitId === 'orc-catapults' ? 'catapults' : unitId;
          units[unitName] = parseInt(slider.value);
        }
      });
    }
    
    return units;
  }

  /**
   * Open the WC1 report match modal
   */
  openWC1ReportModal() {
    console.log('🚀 Opening WC1 report modal...');
    console.log('👤 Current user:', this.currentUser);
    
    if (!this.currentUser) {
      console.log('❌ No current user, showing alert');
      alert('Please log in to report matches.');
      return;
    }

    const modal = document.getElementById('wc1-report-match-modal');
    if (!modal) {
      console.error('❌ WC1 modal not found!');
      return;
    }

    // Reset form
    const form = document.getElementById('wc1-report-match-form');
    if (form) {
      form.reset();
    }

    // Reset unit interface
    this.resetUnitInterface();

    // Show modal
    modal.classList.add('show');
    
    console.log('✅ WC1 modal opened');
  }

  /**
   * Reset unit interface to default state
   */
  resetUnitInterface() {
    // Hide all unit containers initially
    const humanUnits = document.getElementById('human-units');
    const orcUnits = document.getElementById('orc-units');
    if (humanUnits) humanUnits.style.display = 'none';
    if (orcUnits) orcUnits.style.display = 'none';
    
    // Reset all unit sliders
    this.resetAllUnitSliders();
    
    // Hide unit validation message
    const unitValidation = document.getElementById('unit-validation');
    if (unitValidation) unitValidation.style.display = 'none';
    
    // Set default match type to vs AI
    const vsAIRadio = document.getElementById('wc1-match-type-vsai');
    if (vsAIRadio) {
      vsAIRadio.checked = true;
      this.handleMatchTypeSelection('vsai');
    }
  }

  /**
   * Close the WC1 report match modal
   */
  closeWC1ReportModal() {
    console.log('🚪 Closing WC1 report modal...');
    
    const modal = document.getElementById('wc1-report-match-modal');
    if (modal) {
      // Use CSS class removal
      modal.classList.remove('show');
      modal.style.display = 'none';
      
      // Restore body scrolling
      document.body.style.overflow = '';
      
      // Reset form if it exists
      const form = document.getElementById('wc1-report-match-form');
      if (form) {
        form.reset();
      }
      
      console.log('✅ WC1 modal closed safely');
    }
  }

  /**
   * Handle WC1 match form submission
   */
  async handleWC1MatchSubmission(form) {
    console.log('📝 Processing WC1 match submission...');
    
    try {
      // Extract form data
      const formData = new FormData(form);
      const matchType = formData.get('matchType') || 'vsai';
      const matchData = {
        map: formData.get('map'),
        race: formData.get('race'),
        result: formData.get('result'),
        duration: formData.get('duration') || null,
        notes: formData.get('notes') || '',
        gameType: 'warcraft1',
        matchType: matchType,
        timestamp: new Date().toISOString()
      };
      
      console.log('📊 Match data:', matchData);
      
      // Validate required fields
      if (!matchData.map || !matchData.race || !matchData.result) {
        alert('Please fill in all required fields (Scenario, Race, and Result).');
        return;
      }

      // Validate unit requirements for vs AI matches
      if (matchType === 'vsai') {
        if (!this.validateUnitRequirements()) {
          alert('Invalid army composition! You must have at least one worker (Peasant/Peon) and one additional unit.');
          return;
        }

        // Add unit composition data for vs AI matches
        const unitComposition = this.getUnitComposition();
        if (!unitComposition) {
          alert('Please select your race and set up your army composition.');
          return;
        }
        matchData.units = unitComposition;
        console.log('🏰 Unit composition:', unitComposition);
      }
      
      // Disable submit button
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
      
      // Submit to backend
      const response = await fetch('/api/ladder/matches/wc1', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(matchData)
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Match submitted successfully:', result);
        
        // Show success message
        const successMessage = matchType === 'vsai' 
          ? 'Match reported successfully! Your battle against the AI has been recorded.'
          : 'Match reported successfully! Your 1v1 match has been recorded.';
        alert(successMessage);
        
        // Close modal and refresh data
        this.closeWC1ReportModal();
        await this.updateWC1Stats();
        
      } else {
        const error = await response.json();
        console.error('❌ Match submission failed:', error);
        alert(`Failed to submit match: ${error.message || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.error('❌ Error submitting match:', error);
      alert('An error occurred while submitting the match. Please try again.');
    } finally {
      // Re-enable submit button
      const submitBtn = form.querySelector('button[type="submit"]');
      if (submitBtn) {
        submitBtn.disabled = false;
        const matchType = new FormData(form).get('matchType') || 'vsai';
        const buttonText = matchType === 'vsai' 
          ? '<i class="fas fa-robot"></i> Submit Battle Report'
          : '<i class="fas fa-user"></i> Submit 1v1 Match';
        submitBtn.innerHTML = buttonText;
      }
    }
  }

  /**
   * Toggle controls based on game type
   */
  toggleControls(gameType) {
    const wc2wc3Controls = document.getElementById('wc2-wc3-controls');

    if (gameType === 'war1') {
      if (wc2wc3Controls) wc2wc3Controls.style.display = 'none';
    } else {
      if (wc2wc3Controls) wc2wc3Controls.style.display = 'flex';
    }
  }

  /**
   * Update report button for WC1
   */
  updateReportButton() {
    const reportBtn = document.getElementById('report-match-btn');
    if (reportBtn) {
      const textSpan = reportBtn.querySelector('#report-match-text');
      if (textSpan) {
        textSpan.textContent = 'REPORT WC1 MATCH';
      }
    }
  }

  /**
   * Reset report button to default
   */
  resetReportButton() {
    const reportBtn = document.getElementById('report-match-btn');
    if (reportBtn) {
      const textSpan = reportBtn.querySelector('#report-match-text');
      if (textSpan) {
        textSpan.textContent = 'REPORT MATCH';
      }
    }
  }

  /**
   * Get scenarios by category
   */
  getScenariosByCategory(category) {
    if (category === 'all') {
      return this.scenarios;
    }
    return this.scenarios.filter(s => s.category === category);
  }

  /**
   * Update statistics for WC1 (map win percentages)
   */
  async updateWC1Stats() {
    console.log('🗡️ Updating WC1 stats...');
    try {
      // Update leaderboard title for WC1 (supports both vs AI and 1v1)
      const leaderboardTitle = document.getElementById('leaderboard-title');
      if (leaderboardTitle) {
        leaderboardTitle.textContent = 'WC1 Leaderboard';
      }

      // Load WC1 leaderboard data
      console.log('🗡️ Calling loadWC1Leaderboard...');
      await this.loadWC1Leaderboard();
      
      // Load WC1 recent matches
      await this.loadWC1RecentMatches();
      
      // Load ranks data for sidebar
      await this.loadWC1Ranks();
      
      // Load WC1 statistics - use 'war1' to match backend mapping
      const response = await fetch('/api/ladder/stats?gameType=war1', {
        credentials: 'include'
      });
      
      if (response.ok) {
        const stats = await response.json();
        this.renderWC1MapStats(stats);
      }
    } catch (error) {
      console.error('❌ Failed to load WC1 stats:', error);
    }
  }

  /**
   * Load and render WC1 leaderboard
   */
  async loadWC1Leaderboard(matchType = null) {
    console.log('🗡️ Loading WC1 leaderboard...');
    try {
      // Use provided match type or default to current match type
      const currentMatchType = matchType || this.currentMatchType || 'vsai';
      
      // Build query parameters - use 'war1' to match backend mapping
      const params = new URLSearchParams({
        gameType: 'war1',
        matchType: currentMatchType,
        limit: 50,
        page: this.currentPage
      });
      
      // Add search query if present
      if (this.searchQuery) {
        params.append('search', this.searchQuery);
      }
      
      console.log('🗡️ Fetching WC1 leaderboard from:', `/api/ladder/rankings?${params}`);
      const response = await fetch(`/api/ladder/rankings?${params}`, {
        credentials: 'include'
      });
      
      console.log('🗡️ WC1 leaderboard response status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('🗡️ WC1 leaderboard data:', data);
        let players, totalPages;
        
        if (Array.isArray(data)) {
          players = data;
          totalPages = 1;
        } else {
          players = data.players || data.rankings || [];
          totalPages = data.pagination?.pages || 1;
        }
        
        console.log('🗡️ WC1 players to render:', players.length);
        this.totalPages = totalPages;
        this.renderWC1Leaderboard(players, currentMatchType);
        this.updateWC1Pagination();
      } else {
        console.error('🗡️ WC1 leaderboard API error:', response.status, response.statusText);
        // Show empty leaderboard with message
        this.renderWC1Leaderboard([], currentMatchType);
        this.updateWC1Pagination();
      }
    } catch (error) {
      console.error('❌ Failed to load WC1 leaderboard:', error);
      this.renderWC1Leaderboard([], matchType || 'vsai');
      this.updateWC1Pagination();
    }
  }

  /**
   * Render WC1 leaderboard table
   */
  renderWC1Leaderboard(rankings, matchType = 'vsai') {
    const tbody = document.getElementById('leaderboard-body');
    if (!tbody) return;

    if (rankings.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="6" class="empty-state">
            <div class="wc1-empty-state">
              <i class="fas fa-sword"></i>
              <h3>No WC1 matches yet!</h3>
              <p>Be the first to battle in classic Warcraft scenarios - challenge AI opponents or face other players in 1v1 combat!</p>
              <button onclick="wc1LadderManager.openWC1ReportModal()" class="btn btn-primary">
                <i class="fas fa-trophy"></i> Report Your First Match
              </button>
            </div>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = rankings.map((player, index) => {
      const wins = player.stats?.wins || 0;
      const losses = player.stats?.losses || 0;
      const winRate = player.stats?.winRate || 0;
      
      return `
        <tr class="player-row" data-player-id="${player._id}">
          <td class="rank-cell">
            <div class="rank-info">
              <span class="rank-number">${index + 1}</span>
              ${player.rank ? `
                <div class="rank-badge">
                  <img src="/assets/img/ranks/${player.rank.image}" alt="${player.rank.name}" class="rank-image">
                  <span class="rank-name">${player.rank.name}</span>
                </div>
              ` : ''}
            </div>
          </td>
          <td class="player-cell">
            <div class="player-info" onclick="wc1LadderManager.showWC1PlayerStats('${player.name}')">
              <div class="player-main">
                <span class="player-name">${player.name}</span>
              </div>
              <div class="player-details">
                <span class="player-race ${player.preferredRace || 'human'}">${(player.preferredRace || 'human').charAt(0).toUpperCase() + (player.preferredRace || 'human').slice(1)}</span>
              </div>
            </div>
          </td>
          <td class="mmr-cell">
            <span class="mmr-value">${player.mmr || 1200}</span>
          </td>
          <td class="wins-cell">${wins}</td>
          <td class="losses-cell">${losses}</td>
          <td class="ratio-cell">
            <span class="win-rate ${this.getWinRateClass(winRate)}">${winRate.toFixed(1)}%</span>
            <span class="games-count">(${wins + losses} games)</span>
          </td>
        </tr>
      `;
    }).join('');
  }

  /**
   * Update WC1 pagination controls
   */
  updateWC1Pagination() {
    const pageInfo = document.getElementById('page-info');
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    
    if (pageInfo) {
      pageInfo.textContent = `Page ${this.currentPage} of ${this.totalPages}`;
    }
    
    if (prevBtn) {
      prevBtn.disabled = this.currentPage <= 1;
      prevBtn.onclick = () => this.previousWC1Page();
    }
    
    if (nextBtn) {
      nextBtn.disabled = this.currentPage >= this.totalPages;
      nextBtn.onclick = () => this.nextWC1Page();
    }
  }

  /**
   * Go to previous page
   */
  previousWC1Page() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.loadWC1Leaderboard();
    }
  }

  /**
   * Go to next page
   */
  nextWC1Page() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.loadWC1Leaderboard();
    }
  }

  /**
   * Load and render WC1 recent matches
   */
  async loadWC1RecentMatches() {
    try {
      const response = await fetch('/api/ladder/recent-matches?gameType=war1&limit=10', {
        credentials: 'include'
      });
      
      if (response.ok) {
        const matches = await response.json();
        this.renderWC1RecentMatches(matches);
      } else {
        this.renderWC1RecentMatches([]);
      }
    } catch (error) {
      console.error('❌ Failed to load WC1 recent matches:', error);
      this.renderWC1RecentMatches([]);
    }
  }

  /**
   * Render WC1 recent matches
   */
  renderWC1RecentMatches(matches) {
    const container = document.getElementById('recent-matches-container');
    if (!container) return;

    if (matches.length === 0) {
      container.innerHTML = `
        <div class="empty-matches">
          <i class="fas fa-sword"></i>
          <p>No recent WC1 matches</p>
        </div>
      `;
      return;
    }

    container.innerHTML = matches.map(match => {
      const player = match.players?.find(p => !p.isAI);
      const unitComposition = player?.units || {};
      const hasUnits = Object.keys(unitComposition).length > 0;
      
      return `
        <div class="match-item">
          <div class="match-header">
            <span class="match-type">vs AI</span>
            <span class="match-map">${match.map?.name || match.map || 'Unknown Map'}</span>
          </div>
          <div class="match-players">
            <span class="match-player ${player?.result === 'win' || (!player?.result && match.winner?.toString() === player?.playerId?.toString()) ? 'win' : 'loss'}">
              ${player?.name || player?.playerName || 'Player'} (${player?.race || 'Unknown'})
            </span>
            vs
            <span class="match-player ai">AI</span>
          </div>
          ${hasUnits ? `
            <div class="match-units">
              <span class="units-label">Army:</span>
              <div class="units-list">
                ${Object.entries(unitComposition)
                  .filter(([unit, count]) => count > 0)
                  .map(([unit, count]) => `
                    <span class="unit-item">
                      <i class="fas ${this.getUnitIcon(unit)}"></i>
                      ${count} ${this.formatUnitName(unit)}
                    </span>
                  `).join('')}
              </div>
            </div>
          ` : ''}
          <div class="match-time">${this.formatMatchTime(match.createdAt || match.date)}</div>
        </div>
      `;
    }).join('');
  }

  /**
   * Get icon for unit type
   */
  getUnitIcon(unitType) {
    const iconMap = {
      'peasants': 'fa-hammer',
      'footmen': 'fa-sword',
      'archers': 'fa-bow-arrow',
      'knights': 'fa-chess-knight',
      'catapults': 'fa-dot-circle',
      'clerics': 'fa-cross',
      'peons': 'fa-hammer',
      'grunts': 'fa-fist-raised',
      'spearmen': 'fa-spear',
      'raiders': 'fa-horse',
      'necrolytes': 'fa-skull'
    };
    return iconMap[unitType] || 'fa-question';
  }

  /**
   * Format unit name for display
   */
  formatUnitName(unitType) {
    const nameMap = {
      'peasants': 'Peasants',
      'footmen': 'Footmen',
      'archers': 'Archers',
      'knights': 'Knights',
      'catapults': 'Catapults',
      'clerics': 'Clerics',
      'peons': 'Peons',
      'grunts': 'Grunts',
      'spearmen': 'Spearmen',
      'raiders': 'Raiders',
      'necrolytes': 'Necrolytes'
    };
    return nameMap[unitType] || unitType;
  }

  /**
   * Show WC1 player statistics using the full player modal system
   */
  showWC1PlayerStats(playerName) {
    console.log(`🗡️ WC1LadderManager.showWC1PlayerStats called for: ${playerName}`);
    
    // Use the same player modal system as WC2/WC3 for consistency
    if (window.showPlayerDetails) {
      console.log('🎯 Calling window.showPlayerDetails for WC1 player...');
      window.showPlayerDetails(playerName);
    } else if (window.openPlayerDetailsModal) {
      console.log('🎯 Calling window.openPlayerDetailsModal for WC1 player...');
      window.openPlayerDetailsModal(playerName);
    } else {
      console.error('❌ No player details modal functions available');
      // Fallback to simple alert if modal system is not available
      alert(`WC1 Player Stats for ${playerName}\n\nDetailed vs AI statistics coming soon!`);
    }
  }

  /**
   * Get CSS class for win rate display
   */
  getWinRateClass(winRate) {
    if (winRate >= 70) return 'excellent';
    if (winRate >= 60) return 'good';
    if (winRate >= 50) return 'average';
    return 'below-average';
  }

  /**
   * Format match time for display
   */
  formatMatchTime(timestamp) {
    if (!timestamp) return 'Unknown';
    
    const now = new Date();
    const matchTime = new Date(timestamp);
    const diffMs = now - matchTime;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) return `${diffDays}d ago`;
    if (diffHours > 0) return `${diffHours}h ago`;
    if (diffMins > 0) return `${diffMins}m ago`;
    return 'Just now';
  }

  /**
   * Render WC1 map-specific statistics
   */
  renderWC1MapStats(stats) {
    // This would integrate with the charts section to show
    // map win percentages instead of generic stats
    console.log('📊 WC1 Map Stats:', stats);
    
    // TODO: Update charts to show map-specific win rates
    // This would replace the generic race/match type charts
    // with WC1-specific map performance charts
  }

  /**
   * Switch WC1 match type (vs AI or 1v1)
   */
  async switchWC1MatchType(matchType) {
    if (matchType === this.currentMatchType) {
      console.log(`🔄 WC1 match type already set to: ${matchType}`);
      return;
    }
    
    console.log(`🔄 Switching WC1 match type from ${this.currentMatchType} to: ${matchType}`);
    console.log(`🗡️ WC1 is active: ${this.isActive}`);
    
    this.currentMatchType = matchType;
    this.currentPage = 1; // Reset to first page
    
    // Update button states
    this.updateWC1MatchTypeButtons(matchType);
    
    // Update leaderboard and stats
    await this.updateWC1Stats();
  }

  /**
   * Update WC1 match type button states
   */
  updateWC1MatchTypeButtons(activeMatchType) {
    const vsAIBtn = document.getElementById('wc1-vsai-btn');
    const oneVOneBtn = document.getElementById('wc1-1v1-btn');
    
    if (vsAIBtn && oneVOneBtn) {
      // Remove active class from both
      vsAIBtn.classList.remove('active');
      oneVOneBtn.classList.remove('active');
      
      // Add active class to the selected one
      if (activeMatchType === 'vsai') {
        vsAIBtn.classList.add('active');
      } else if (activeMatchType === '1v1') {
        oneVOneBtn.classList.add('active');
      }
      
      console.log(`✅ Updated WC1 match type buttons: ${activeMatchType} is now active`);
    }
  }

  /**
   * Public API - called when switching to WC1 tab
   */
  async onActivate() {
    await this.activateWC1Mode();
  }

  /**
   * Load and render WC1 ranks
   */
  async loadWC1Ranks() {
    try {
      const response = await fetch('/api/ladder/ranks', {
        credentials: 'include'
      });
      
      if (response.ok) {
        const ranks = await response.json();
        this.renderWC1Ranks(ranks);
      } else {
        this.renderWC1Ranks([]);
      }
    } catch (error) {
      console.error('❌ Failed to load WC1 ranks:', error);
      this.renderWC1Ranks([]);
    }
  }

  /**
   * Render WC1 ranks in sidebar
   */
  renderWC1Ranks(ranks) {
    const container = document.getElementById('ranks-container');
    if (!container) return;

    if (ranks.length === 0) {
      container.innerHTML = `
        <div class="empty-ranks">
          <i class="fas fa-medal"></i>
          <p>Ranks coming soon for WC1!</p>
        </div>
      `;
      return;
    }

    // Show ranks (same format as WC2/WC3 but for WC1)
    const reversedRanks = [...ranks].reverse();
    
    container.innerHTML = reversedRanks.map(rank => {
      const rankImagePath = rank.image.startsWith('/') || rank.image.startsWith('http')
        ? rank.image
        : `/assets/img/ranks/${rank.image}`;
      
      return `
        <div class="rank-item">
          <img src="${rankImagePath}" 
               alt="${rank.name}" 
               class="rank-image" 
               onerror="this.style.display='none'">
          <div class="rank-details">
            <h4 class="rank-name">${rank.name}</h4>
            <p class="rank-threshold">${rank.threshold}+ MMR</p>
          </div>
        </div>
      `;
    }).join('');
  }
}

// Auto-initialize when module loads
const wc1LadderManager = new WC1LadderManager();

// Export for global access
if (typeof window !== 'undefined') {
  window.wc1LadderManager = wc1LadderManager;
}

export default wc1LadderManager; 