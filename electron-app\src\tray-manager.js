const { Tray, Menu, shell, dialog } = require('electron');
const path = require('path');

class TrayManager {
  constructor(app, windowManager, gameDetector, store, screenshotManager) {
    this.app = app;
    this.windowManager = windowManager;
    this.gameDetector = gameDetector;
    this.store = store;
    this.screenshotManager = screenshotManager;
    this.tray = null;
    this.isQuitting = false;
  }

  createTray() {
    // Create tray icon
    const iconPath = this.getTrayIconPath();
    this.tray = new Tray(iconPath);
    
    this.tray.setToolTip('WC Arena Companion - Game Launcher & Screenshot Manager');
    
    // Handle tray click
    this.tray.on('click', () => {
      this.toggleMainWindow();
    });

    // Handle right-click
    this.tray.on('right-click', () => {
      this.tray.popUpContextMenu();
    });

    // Initial menu setup
    this.updateTrayMenu();
    
    return this.tray;
  }

  getTrayIconPath() {
    const iconDir = path.join(__dirname, '..', 'assets');
    const fs = require('fs');
    
    let iconPath;
    switch (process.platform) {
      case 'win32':
        iconPath = path.join(iconDir, 'tray-icon.ico');
        break;
      case 'darwin':
        iconPath = path.join(iconDir, 'tray-iconTemplate.png'); // macOS uses template images
        break;
      default:
        iconPath = path.join(iconDir, 'tray-icon.png');
    }
    
    // Fallback to main icon if tray icon doesn't exist
    if (!fs.existsSync(iconPath)) {
      const fallbackIcon = path.join(iconDir, 'icon.png');
      if (fs.existsSync(fallbackIcon)) {
        return fallbackIcon;
      }
      // Final fallback - create a simple default icon path
      return iconPath;
    }
    
    return iconPath;
  }

  toggleMainWindow() {
    if (this.windowManager.mainWindow) {
      if (this.windowManager.mainWindow.isVisible()) {
        this.windowManager.mainWindow.hide();
      } else {
        this.windowManager.show();
      }
    } else {
      this.windowManager.createMainWindow();
      this.windowManager.loadMainApp();
    }
  }

  async updateTrayMenu() {
    const games = await this.gameDetector.getDetectedGames();
    
    // Build menu template
    const template = [
      {
        label: 'WC Arena Companion',
        type: 'normal',
        click: () => this.toggleMainWindow()
      },
      { type: 'separator' },
      
      // Games section
      this.buildGamesMenu(games),
      
      { type: 'separator' },
      
      // Actions
      {
        label: 'Find Games',
        type: 'normal',
        click: () => this.findGames()
      },
      {
        label: 'Refresh Games',
        type: 'normal',
        click: () => this.refreshGames()
      },
      {
        label: 'Open Screenshots Folder',
        type: 'normal',
        click: () => this.openScreenshotsFolder()
      },
      
      { type: 'separator' },
      
      // Settings and info
      {
        label: 'Settings',
        type: 'normal',
        click: () => this.openSettings()
      },
      {
        label: 'About',
        type: 'normal',
        click: () => this.showAbout()
      },
      
      { type: 'separator' },
      
      // Exit
      {
        label: 'Quit',
        type: 'normal',
        click: () => this.quitApp()
      }
    ];

    const contextMenu = Menu.buildFromTemplate(template);
    this.tray.setContextMenu(contextMenu);
  }

  buildGamesMenu(games) {
    if (games.length === 0) {
      return {
        label: 'No Games Found',
        type: 'normal',
        enabled: false
      };
    }

    // Debug: Log all games
    console.log('Building tray menu for games:', games.map(g => ({
      name: g.name,
      type: g.type,
      path: g.path
    })));

    // Group games by type
    const gamesByType = {
      warcraft1: games.filter(g => g.type === 'warcraft1'),
      warcraft2: games.filter(g => g.type === 'warcraft2'),
      warcraft3: games.filter(g => g.type === 'warcraft3'),
      w3champions: games.filter(g => g.type === 'w3champions'),
      battlenet: games.filter(g => g.type === 'battlenet')
    };

    const submenu = [];

    // Add Warcraft 1 games
    if (gamesByType.warcraft1.length > 0) {
      submenu.push({
        label: 'Warcraft I: Orcs & Humans',
        type: 'submenu',
        submenu: gamesByType.warcraft1.map(game => ({
          label: game.name || `Warcraft I (${game.executable})`,
          type: 'normal',
          click: () => this.launchGame(game.id)
        }))
      });
    }

    // Add Warcraft 2 games
    if (gamesByType.warcraft2.length > 0) {
      // Group Warcraft II games by base name/type
      const wc2Groups = this.groupSimilarGames(gamesByType.warcraft2);
      
      if (wc2Groups.length === 1 && wc2Groups[0].games.length === 1) {
        // Single game, create submenu with launch and maps options
        const game = wc2Groups[0].games[0];
        submenu.push({
          label: game.name || `Warcraft II (${game.executable})`,
          type: 'submenu',
          submenu: this.createGameSubmenu(game)
        });
      } else {
        // Multiple games or groups, create nested submenu structure
        submenu.push({
          label: 'Warcraft II',
          type: 'submenu',
          submenu: wc2Groups.map(group => {
            if (group.games.length === 1) {
              return {
                label: group.games[0].name || `Warcraft II (${group.games[0].executable})`,
                type: 'submenu',
                submenu: this.createGameSubmenu(group.games[0])
              };
            } else {
              return {
                label: group.name,
                type: 'submenu',
                submenu: group.games.map(game => ({
                  label: this.getInstallationLabel(game),
                  type: 'submenu',
                  submenu: this.createGameSubmenu(game)
                }))
              };
            }
          })
        });
      }
    }

    // Add Battle.net games
    if (gamesByType.battlenet.length > 0) {
      gamesByType.battlenet.forEach(game => {
        const runningIndicator = game.isRunning ? '🟢 ' : '';
        const gameLabel = `${runningIndicator}${game.name || 'Battle.net'}`;
        
        submenu.push({
          label: gameLabel,
          type: 'normal',
          enabled: !game.isRunning, // Disable if already running
          click: () => this.launchGame(game.id)
        });
      });
    }

    // Add Warcraft 3 games
    if (gamesByType.warcraft3.length > 0) {
      gamesByType.warcraft3.forEach(game => {
        const runningIndicator = game.isRunning ? '🟢 ' : '';
        const gameLabel = `${runningIndicator}${game.name || 'Warcraft III'}`;
        
        submenu.push({
          label: gameLabel,
          type: 'submenu',
          enabled: !game.isRunning, // Disable if already running
          submenu: this.createGameSubmenu(game)
        });
      });
    }

    // Add W3Champions games
    if (gamesByType.w3champions.length > 0) {
      gamesByType.w3champions.forEach(game => {
        const runningIndicator = game.isRunning ? '🟢 ' : '';
        const gameLabel = `${runningIndicator}${game.name || 'W3Champions'}`;
        
        submenu.push({
          label: gameLabel,
          type: 'submenu',
          enabled: !game.isRunning, // Disable if already running
          submenu: this.createGameSubmenu(game)
        });
      });
    }

    return {
      label: 'Launch Game',
      type: 'submenu',
      submenu: submenu
    };
  }

  groupSimilarGames(games) {
    // Group games by their base name/type
    const groups = {};
    
    games.forEach(game => {
      let groupKey = 'default';
      let groupName = game.name || `Game (${game.executable || 'Unknown'})`;
      
      // Safely check game name
      const gameName = (game.name || '').toLowerCase();
      
      // Determine group based on game characteristics
      if (gameName.includes('war2 combat') || gameName.includes('warcraft 2 combat')) {
        groupKey = 'war2combat';
        groupName = 'Warcraft 2 Combat Edition';
      } else if (gameName.includes('remastered')) {
        groupKey = 'remastered';
        groupName = 'Warcraft II: Remastered';
      } else if (gameName.includes('battle.net edition') || gameName.includes('bne')) {
        groupKey = 'battlenet';
        groupName = 'Warcraft II: Battle.net Edition';
      } else if (gameName.includes('tides of darkness')) {
        groupKey = 'tides';
        groupName = 'Warcraft II: Tides of Darkness';
      } else {
        groupKey = 'other';
        groupName = game.name || 'Warcraft II';
      }
      
      if (!groups[groupKey]) {
        groups[groupKey] = {
          name: groupName,
          games: []
        };
      }
      
      groups[groupKey].games.push(game);
    });
    
    return Object.values(groups);
  }

  getInstallationLabel(game) {
    // Create a label that shows the installation location
    if (!game || !game.directory) {
      return game?.name || `Game (${game?.executable || 'Unknown'})`;
    }
    
    const pathParts = game.directory.split('\\');
    const relevantPart = pathParts[pathParts.length - 1] || pathParts[pathParts.length - 2] || 'Unknown';
    const driveLetter = pathParts[0] || 'C:';
    
    // Try to create a meaningful label
    if (relevantPart.toLowerCase().includes('war2combat')) {
      return `War2Combat (${driveLetter})`;
    } else if (relevantPart.toLowerCase().includes('remastered')) {
      return `Remastered (${driveLetter})`;
    } else if (relevantPart.toLowerCase().includes('bne')) {
      return `Battle.net Edition (${driveLetter})`;
    } else if (relevantPart.toLowerCase().includes('w3champions')) {
      return `W3Champions (${driveLetter})`;
    } else if (relevantPart.includes('Program Files')) {
      return `Standard Installation (${driveLetter})`;
    } else {
      return `${relevantPart} (${driveLetter})`;
    }
  }

  async launchGame(gameId) {
    try {
      console.log(`Tray: Launching game with ID: ${gameId}`);
      const result = await this.gameDetector.launchGame(gameId, async () => {
        // Update tray menu after game launch
        await this.updateTrayMenu();
      });
      
      if (result.success) {
        console.log(`Tray: ${result.message}`);
      }
      
      return result;
    } catch (error) {
      console.error(`Tray: Failed to launch game:`, error);
      throw error;
    }
  }

  async findGames() {
    // Show searching notification
    this.tray.displayBalloon({
      title: 'Searching for Games',
      content: 'Scanning your computer for Warcraft games...',
      icon: this.getTrayIconPath()
    });

    try {
      const games = await this.gameDetector.findAllGames();
      
      // Update menu with new games
      await this.updateTrayMenu();
      
      // Show result notification
      this.tray.displayBalloon({
        title: 'Game Search Complete',
        content: `Found ${games.length} games on your system`,
        icon: this.getTrayIconPath()
      });
      
    } catch (error) {
      console.error('Game search failed:', error);
      this.tray.displayBalloon({
        title: 'Search Failed',
        content: 'Failed to search for games',
        icon: this.getTrayIconPath()
      });
    }
  }

  async refreshGames() {
    await this.findGames();
  }

  openSettings() {
    this.toggleMainWindow();
    // Send message to renderer to open settings
    if (this.windowManager.mainWindow) {
      this.windowManager.sendToRenderer('open-settings');
    }
  }

  showAbout() {
    dialog.showMessageBox(this.windowManager.mainWindow, {
      type: 'info',
      title: 'About WC Arena Core',
      message: 'WC Arena Core - Desktop Application',
      detail: `Version: ${this.app.getVersion()}\n\nA desktop application for launching Warcraft games and accessing WC Arena Core.`,
      buttons: ['OK']
    });
  }

  quitApp() {
    this.isQuitting = true;
    this.app.quit();
  }

  setQuitting(isQuitting) {
    this.isQuitting = isQuitting;
  }

  isAppQuitting() {
    return this.isQuitting;
  }

  destroy() {
    if (this.tray) {
      this.tray.destroy();
      this.tray = null;
    }
  }

  // Handle minimize to tray
  handleWindowMinimize() {
    if (this.store.get('minimizeToTray', true)) {
      this.windowManager.mainWindow.hide();
      
      // Show balloon notification on first minimize
      if (!this.store.get('hasShownTrayNotification', false)) {
        this.tray.displayBalloon({
          title: 'WC Arena Core',
          content: 'App minimized to system tray. Click the tray icon to restore.',
          icon: this.getTrayIconPath()
        });
        this.store.set('hasShownTrayNotification', true);
      }
    }
  }

  // Handle window close
  handleWindowClose(event) {
    if (!this.isQuitting && this.store.get('closeToTray', true)) {
      event.preventDefault();
      this.handleWindowMinimize();
    }
  }

  // Update tray tooltip with game count
  updateTooltip() {
    const games = this.gameDetector.getDetectedGames();
    const tooltip = `WC Arena Core - ${games.length} games detected`;
    this.tray.setToolTip(tooltip);
  }

  openScreenshotsFolder() {
    this.screenshotManager.openScreenshotsFolder();
  }

  getMapsFolder(game) {
    // Get the maps folder path for a specific game
    const fs = require('fs');
    const os = require('os');
    
    try {
      switch (game.type) {
        case 'warcraft2':
          // Try common Warcraft II maps folders
          const wc2GameDir = path.dirname(game.path);
          const wc2Candidates = [
            path.join(wc2GameDir, 'maps'),
            path.join(wc2GameDir, 'Maps'),
            path.join(wc2GameDir, 'MAPS'),
            path.join(wc2GameDir, 'scenarios'),
            path.join(wc2GameDir, 'Scenarios'),
            path.join(wc2GameDir, 'puds'),
            path.join(wc2GameDir, 'Puds'),
            path.join(wc2GameDir, 'PUDS')
          ];
          
          for (const candidate of wc2Candidates) {
            if (fs.existsSync(candidate)) {
              return candidate;
            }
          }
          
          // Fallback: create maps folder in game directory
          const wc2MapsDir = path.join(wc2GameDir, 'maps');
          if (!fs.existsSync(wc2MapsDir)) {
            fs.mkdirSync(wc2MapsDir, { recursive: true });
          }
          return wc2MapsDir;

        case 'warcraft3':
          // Warcraft III maps locations
          const documentsPath = os.homedir();
          const wc3Candidates = [
            path.join(documentsPath, 'Documents', 'Warcraft III', 'Maps'),
            path.join(documentsPath, 'Warcraft III', 'Maps'),
            path.join(documentsPath, 'My Documents', 'Warcraft III', 'Maps'),
            path.join(path.dirname(game.path), 'Maps'),
            path.join(path.dirname(game.path), 'maps'),
            path.join(path.dirname(game.path), '_retail_', 'Maps'),
            path.join(os.homedir(), 'Documents', 'Warcraft III Reforged', 'Maps')
          ];
          
          for (const candidate of wc3Candidates) {
            if (fs.existsSync(candidate)) {
              return candidate;
            }
          }
          
          // Fallback: create in Documents
          const wc3MapsDir = path.join(documentsPath, 'Documents', 'Warcraft III', 'Maps');
          if (!fs.existsSync(wc3MapsDir)) {
            fs.mkdirSync(wc3MapsDir, { recursive: true });
          }
          return wc3MapsDir;

        case 'w3champions':
          // W3Champions typically uses the standard WC3 maps folder
          const w3cDocumentsPath = os.homedir();
          const w3cCandidates = [
            path.join(w3cDocumentsPath, 'Documents', 'Warcraft III', 'Maps'),
            path.join(w3cDocumentsPath, 'Documents', 'W3Champions', 'Maps'),
            path.join(path.dirname(game.path), 'Maps'),
            path.join(path.dirname(game.path), 'maps')
          ];
          
          for (const candidate of w3cCandidates) {
            if (fs.existsSync(candidate)) {
              return candidate;
            }
          }
          
          // Fallback: use standard WC3 location
          const w3cMapsDir = path.join(w3cDocumentsPath, 'Documents', 'Warcraft III', 'Maps');
          if (!fs.existsSync(w3cMapsDir)) {
            fs.mkdirSync(w3cMapsDir, { recursive: true });
          }
          return w3cMapsDir;

        default:
          return null;
      }
    } catch (error) {
      console.error(`Error finding maps folder for ${game.name}:`, error);
      return null;
    }
  }

  openMapsFolder(game) {
    const mapsPath = this.getMapsFolder(game);
    if (mapsPath) {
      console.log(`Opening maps folder for ${game.name}: ${mapsPath}`);
      shell.openPath(mapsPath);
    } else {
      console.error(`Could not find maps folder for ${game.name}`);
      dialog.showMessageBox({
        type: 'warning',
        title: 'Maps Folder Not Found',
        message: `Could not find the maps folder for ${game.name}`,
        detail: 'The maps folder may not exist or may be in a non-standard location.',
        buttons: ['OK']
      });
    }
  }

  createGameSubmenu(game) {
    // Create a submenu for a single game with launch and maps folder options
    const submenu = [{
      label: `🚀 Launch ${game.name}`,
      type: 'normal',
      click: () => this.launchGame(game.id)
    }];

    // Add maps folder option for all games except Warcraft I and Battle.net
    if (game.type !== 'warcraft1' && game.type !== 'battlenet') {
      submenu.push({
        label: `📁 Open Maps Folder`,
        type: 'normal',
        click: () => this.openMapsFolder(game)
      });
    }

    return submenu;
  }
}

module.exports = { TrayManager }; 