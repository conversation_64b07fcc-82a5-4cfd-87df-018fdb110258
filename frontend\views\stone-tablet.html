<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Stone Tablet - WC Arena</title>
  <link rel="stylesheet" href="/css/warcraft-app-modern.css">
  <link rel="stylesheet" href="/css/black-theme.css">
  <link rel="stylesheet" href="/css/clan-stone-tablet.css">
  <link rel="stylesheet" href="/css/navbar-universal.css">
  <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-left">
        <a href="/views/index.html" class="nav-logo">
          <img src="/images/emblem.png" alt="WC Arena" class="nav-emblem">
          <span>WC Arena</span>
        </a>
      </div>
      <div class="nav-center">
        <div class="nav-links">
          <a href="/views/index.html">Home</a>
          <a href="/views/ladder.html">Ladder</a>
          <a href="/views/tournaments.html">Tournaments</a>
          <a href="/views/maps.html">Maps</a>
          <a href="/views/forum.html">Forum</a>
          <a href="/views/live.html">Live</a>
        </div>
      </div>
      <div class="nav-right">
        <div class="nav-user-section">
          <!-- User info will be populated by navbar script -->
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="main-container">
    <div class="content-wrapper">
      <!-- Page Header -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <i class="fas fa-scroll"></i>
            <span id="stone-tablet-title">Stone Tablet</span>
          </h1>
          <p class="page-subtitle">Clan chronicles and member discussions</p>
        </div>
        
        <!-- Filter Controls -->
        <div class="stone-tablet-filters">
          <!-- Clan Toggle -->
          <div class="clan-filter">
            <button class="clan-toggle-btn active" id="clan-toggle-btn">
              <i class="fas fa-scroll"></i>
              <span id="clan-toggle-text">Clan Posts Only</span>
            </button>
          </div>

          <!-- Game Type Tabs -->
          <div class="game-tabs" id="game-tabs">
            <button class="game-tab active" data-game="wc12">
              <i class="fas fa-shield"></i>
              WC I/II
            </button>
            <button class="game-tab" data-game="wc3">
              <i class="fas fa-crown"></i>
              WC III
            </button>
            <button class="game-tab" data-game="all">
              <i class="fas fa-globe"></i>
              All Games
            </button>
          </div>
        </div>
      </div>

      <!-- Stone Tablet Content -->
      <div class="stone-tablet-container">
        <!-- Left Sidebar -->
        <div class="stone-tablet-sidebar">
          <div class="sidebar-section">
            <h3><i class="fas fa-info-circle"></i> Clan Info</h3>
            <div id="clan-info-widget">
              <!-- Clan info will be loaded here -->
            </div>
          </div>

          <div class="sidebar-section">
            <h3><i class="fas fa-trophy"></i> Achievements</h3>
            <div id="clan-achievements-widget">
              <!-- Clan achievements will be loaded here -->
            </div>
          </div>

          <div class="sidebar-section">
            <h3><i class="fas fa-users"></i> Recent Members</h3>
            <div id="recent-members-widget">
              <!-- Recent members will be loaded here -->
            </div>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="stone-tablet-main">
          <!-- Post Composer -->
          <div class="post-composer" id="post-composer">
            <div class="composer-header">
              <h3><i class="fas fa-feather-alt"></i> Share with your clan</h3>
              <div class="composer-actions">
                <select id="post-game-type" class="game-select">
                  <option value="wc12">WC I/II</option>
                  <option value="wc3">WC III</option>
                  <option value="general">General</option>
                </select>
              </div>
            </div>
            <div class="composer-content">
              <input type="text" id="post-title" placeholder="Post title..." class="post-title-input">
              <textarea id="post-content" placeholder="Share your thoughts, strategies, or clan news..." class="post-content-input"></textarea>
              <div class="composer-footer">
                <button class="btn btn-primary" id="publish-post-btn">
                  <i class="fas fa-paper-plane"></i>
                  Share with Clan
                </button>
              </div>
            </div>
          </div>

          <!-- Posts Feed -->
          <div class="stone-tablet-posts" id="stone-tablet-posts">
            <!-- Posts will be loaded here -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" id="loading-overlay" style="display: none;">
    <div class="loading-content">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p>Loading stone tablet...</p>
    </div>
  </div>

  <!-- Error Modal -->
  <div class="modal" id="error-modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3><i class="fas fa-exclamation-triangle"></i> Error</h3>
        <button class="modal-close" onclick="closeErrorModal()">&times;</button>
      </div>
      <div class="modal-body">
        <p id="error-message">An error occurred.</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="closeErrorModal()">Close</button>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script type="module" src="/js/modules/ApiClient.js"></script>
  <script type="module" src="/js/modules/AuthManager.js"></script>
  <script type="module" src="/js/modules/ClanManager.js"></script>
  <script src="/js/navbar-modern.js"></script>
  <script src="/js/main.js"></script>

  <script>
    // Stone Tablet Page Manager
    class StoneTabletManager {
      constructor() {
        this.currentGame = 'wc12';
        this.clanOnly = true; // Default to showing clan posts only
        this.clanManager = null;
        this.userClan = null;
        this.init();
      }

      async init() {
        try {
          console.log('🏰 Initializing Stone Tablet...');
          
          // Import and initialize ClanManager
          const { ClanManager } = await import('/js/modules/ClanManager.js');
          this.clanManager = new ClanManager();
          await this.clanManager.init();
          
          // Get user's clan data
          this.userClan = this.clanManager.currentClan;
          
          if (!this.userClan) {
            this.showNoClanMessage();
            return;
          }

          // Update page title with clan name
          this.updatePageTitle();
          
          // Setup event listeners
          this.setupEventListeners();
          
          // Load initial content
          await this.loadStoneTabletContent();
          
          console.log('✅ Stone Tablet initialized');
        } catch (error) {
          console.error('❌ Failed to initialize Stone Tablet:', error);
          this.showError('Failed to load stone tablet. Please try again.');
        }
      }

      updatePageTitle() {
        const titleElement = document.getElementById('stone-tablet-title');
        if (titleElement && this.userClan) {
          titleElement.textContent = `[${this.userClan.tag}] ${this.userClan.name} Stone Tablet`;
        }
      }

      setupEventListeners() {
        // Clan toggle button
        const clanToggleBtn = document.getElementById('clan-toggle-btn');
        if (clanToggleBtn) {
          clanToggleBtn.addEventListener('click', () => this.toggleClanFilter());
        }

        // Game tab switching
        document.querySelectorAll('.game-tab').forEach(tab => {
          tab.addEventListener('click', (e) => {
            this.switchGame(e.target.dataset.game);
          });
        });

        // Publish post button
        const publishBtn = document.getElementById('publish-post-btn');
        if (publishBtn) {
          publishBtn.addEventListener('click', () => this.publishPost());
        }
      }

      toggleClanFilter() {
        this.clanOnly = !this.clanOnly;

        const toggleBtn = document.getElementById('clan-toggle-btn');
        const toggleText = document.getElementById('clan-toggle-text');

        if (this.clanOnly) {
          toggleBtn.classList.add('active');
          toggleText.textContent = 'Clan Posts Only';
        } else {
          toggleBtn.classList.remove('active');
          toggleText.textContent = 'All Posts';
        }

        // Reload content with new filter
        this.loadStoneTabletContent();
      }

      async switchGame(gameType) {
        if (this.currentGame === gameType) return;

        // Update active tab
        document.querySelectorAll('.game-tab').forEach(tab => {
          tab.classList.remove('active');
        });
        document.querySelector(`[data-game="${gameType}"]`).classList.add('active');

        this.currentGame = gameType;
        await this.loadStoneTabletContent();
      }

      async loadStoneTabletContent() {
        this.showLoading(true);

        try {
          let posts = [];

          if (this.clanOnly && this.userClan && this.clanManager) {
            // Load clan-specific posts
            console.log('🏰 Loading clan posts for game:', this.currentGame);
            const response = await this.clanManager.api.get(`/api/forum/clan/${this.userClan._id}/topics?gameType=${this.currentGame}`);

            if (response instanceof Response) {
              posts = await response.json();
            } else {
              posts = response.data || response;
            }
          } else {
            // Load general forum posts
            console.log('🌐 Loading general forum posts for game:', this.currentGame);
            const response = await this.clanManager.api.get(`/api/forum/topics?gameType=${this.currentGame}&limit=20`);

            if (response instanceof Response) {
              posts = await response.json();
            } else {
              posts = response.data || response;
            }
          }

          console.log('📝 Loaded posts:', posts);

          // Render posts
          this.renderPosts(posts);

          // Load sidebar content if user has a clan
          if (this.userClan) {
            this.loadSidebarContent();
          }

        } catch (error) {
          console.error('Error loading stone tablet content:', error);
          this.showError('Failed to load stone tablet content.');
        } finally {
          this.showLoading(false);
        }
      }

      renderPosts(posts) {
        const postsContainer = document.getElementById('stone-tablet-posts');
        if (!postsContainer) return;

        if (!posts || posts.length === 0) {
          postsContainer.innerHTML = `
            <div class="empty-posts">
              <i class="fas fa-scroll"></i>
              <h3>No Posts Found</h3>
              <p>${this.clanOnly ? 'Your clan hasn\'t shared any posts yet.' : 'No posts available for this game type.'}</p>
              ${this.clanOnly && this.userClan ? '<p>Be the first to share something with your clan!</p>' : ''}
            </div>
          `;
          return;
        }

        postsContainer.innerHTML = posts.map(post => this.createPostCard(post)).join('');
      }

      createPostCard(post) {
        const timeAgo = this.formatTimeAgo(new Date(post.createdAt));
        const gameTypeLabel = this.formatGameType(post.gameType);
        const authorName = post.author?.username || 'Unknown';
        const clanTag = post.clan ? `[${post.clan.tag}] ` : '';

        return `
          <div class="post-card" data-post-id="${post._id}">
            <div class="post-header">
              <div class="post-author">
                <div class="author-avatar">
                  <i class="fas fa-user"></i>
                </div>
                <div class="author-info">
                  <h4 class="author-name">${clanTag}${authorName}</h4>
                  <p class="post-meta">
                    <span class="game-type">${gameTypeLabel}</span>
                    <span class="post-time">${timeAgo}</span>
                  </p>
                </div>
              </div>
              ${post.isPinned ? '<div class="post-pinned"><i class="fas fa-thumbtack"></i></div>' : ''}
            </div>
            <div class="post-content">
              <h3 class="post-title">${post.title}</h3>
              <div class="post-body">${post.content}</div>
            </div>
            <div class="post-actions">
              <button class="post-action-btn like-btn" onclick="stoneTabletManager.handleReaction('${post._id}', 'like')">
                <i class="fas fa-heart"></i>
                <span>${post.reactions?.like || 0}</span>
              </button>
              <button class="post-action-btn comment-btn" onclick="stoneTabletManager.handleComment('${post._id}')">
                <i class="fas fa-comment"></i>
                <span>${post.replyCount || 0}</span>
              </button>
              <button class="post-action-btn share-btn" onclick="stoneTabletManager.handleShare('${post._id}')">
                <i class="fas fa-share"></i>
              </button>
            </div>
          </div>
        `;
      }

      loadSidebarContent() {
        if (!this.userClan) {
          // Hide sidebar if no clan
          const sidebar = document.querySelector('.stone-tablet-sidebar');
          if (sidebar) {
            sidebar.style.display = 'none';
          }
          return;
        }

        // Show sidebar
        const sidebar = document.querySelector('.stone-tablet-sidebar');
        if (sidebar) {
          sidebar.style.display = 'flex';
        }

        // Load clan info widget
        const clanInfoWidget = document.getElementById('clan-info-widget');
        if (clanInfoWidget) {
          clanInfoWidget.innerHTML = `
            <div class="clan-info-card">
              <h4>[${this.userClan.tag}] ${this.userClan.name}</h4>
              <p class="clan-game">${this.formatGameType(this.userClan.gameType)}</p>
              <div class="clan-stats">
                <div class="stat">
                  <span class="label">Members:</span>
                  <span class="value">${this.clanManager.clanMembers?.length || 0}</span>
                </div>
                <div class="stat">
                  <span class="label">Rating:</span>
                  <span class="value">${this.userClan.rating || 1500}</span>
                </div>
              </div>
            </div>
          `;
        }

        // Load achievements widget
        const achievementsWidget = document.getElementById('clan-achievements-widget');
        if (achievementsWidget) {
          achievementsWidget.innerHTML = `
            <div class="achievement-list">
              <div class="achievement-item">
                <i class="fas fa-star"></i>
                <span>Rating: ${this.userClan.rating || 1500}</span>
              </div>
              <div class="achievement-item">
                <i class="fas fa-users"></i>
                <span>Members: ${this.clanManager.clanMembers?.length || 0}</span>
              </div>
            </div>
          `;
        }

        // Load recent members widget
        const recentMembersWidget = document.getElementById('recent-members-widget');
        if (recentMembersWidget && this.clanManager.clanMembers) {
          const recentMembers = this.clanManager.clanMembers.slice(0, 5);
          recentMembersWidget.innerHTML = `
            <div class="member-list">
              ${recentMembers.map(member => `
                <div class="member-item">
                  <i class="fas fa-user"></i>
                  <span>${member.player?.name || member.user?.username || 'Unknown'}</span>
                </div>
              `).join('')}
            </div>
          `;
        }
      }

      async publishPost() {
        const title = document.getElementById('post-title').value.trim();
        const content = document.getElementById('post-content').value.trim();
        const gameType = document.getElementById('post-game-type').value;

        if (!title || !content) {
          this.showError('Please enter both a title and content for your post.');
          return;
        }

        try {
          // Use ClanManager to publish the post
          await this.clanManager.publishStoneTabletPost();
          
          // Clear the form
          document.getElementById('post-title').value = '';
          document.getElementById('post-content').value = '';
          
          // Reload content
          await this.loadStoneTabletContent();
          
        } catch (error) {
          console.error('Error publishing post:', error);
          this.showError('Failed to publish post. Please try again.');
        }
      }

      showNoClanMessage() {
        const mainContainer = document.querySelector('.stone-tablet-container');
        mainContainer.innerHTML = `
          <div class="no-clan-message">
            <i class="fas fa-shield-alt"></i>
            <h2>No Clan</h2>
            <p>You must be a member of a clan to access the stone tablet.</p>
            <p>Join or create a clan to share posts and discussions with your clan members.</p>
            <div class="clan-actions">
              <a href="/views/myprofile.html" class="btn btn-primary">
                <i class="fas fa-users"></i>
                Manage Clans
              </a>
            </div>
          </div>
        `;
      }

      formatGameType(gameType) {
        switch (gameType) {
          case 'warcraft1': return 'Warcraft I';
          case 'warcraft2': return 'Warcraft II';
          case 'warcraft3': return 'Warcraft III';
          case 'wc12': return 'WC I/II';
          case 'wc3': return 'WC III';
          case 'multi': return 'Multi-Game';
          default: return gameType || 'Unknown';
        }
      }

      showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        overlay.style.display = show ? 'flex' : 'none';
      }

      showError(message) {
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-modal').style.display = 'flex';
      }

      formatTimeAgo(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        if (diffDays < 7) return `${diffDays}d ago`;
        return date.toLocaleDateString();
      }

      handleReaction(postId, reactionType) {
        console.log('🏰 Handling reaction:', postId, reactionType);
        // TODO: Implement reaction handling
      }

      handleComment(postId) {
        console.log('🏰 Handling comment:', postId);
        // TODO: Implement comment handling
      }

      handleShare(postId) {
        console.log('🏰 Handling share:', postId);
        // TODO: Implement share handling
      }
    }

    // Global functions
    function closeErrorModal() {
      document.getElementById('error-modal').style.display = 'none';
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
      window.stoneTabletManager = new StoneTabletManager();
    });
  </script>
</body>
</html>
