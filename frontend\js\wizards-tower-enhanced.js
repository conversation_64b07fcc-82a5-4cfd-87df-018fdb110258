/**
 * Enhanced Wizard's Tower - Wiki Management with Images
 * Handles game tab switching, content display, and image loading for the Warcraft wiki
 */

class EnhancedWizardsTowerManager {
  constructor() {
    this.currentGame = 'wc1';
    this.currentRace = 'human';
    this.currentCategory = 'units';
    this.imageManifest = null;
    this.gameTabs = document.querySelectorAll('.game-tab');
    this.subTabs = document.querySelectorAll('.sub-tab');
    this.wikiContents = document.querySelectorAll('.wiki-content');
    
    this.init();
  }

  async init() {
    console.log('🏰 Initializing Enhanced Wizard\'s Tower...');
    
    // Load image manifest
    await this.loadImageManifest();
    
    // Set up event listeners
    this.setupGameTabs();
    this.setupSubTabs();
    
    // Initialize with WC1 content
    this.switchGame('wc1');
    
    console.log('✅ Enhanced Wizard\'s Tower initialized');
  }

  async loadImageManifest() {
    try {
      const response = await fetch('/assets/img/wizards-tower/image-manifest.json');
      this.imageManifest = await response.json();
      console.log('📋 Image manifest loaded');
    } catch (error) {
      console.warn('⚠️ Could not load image manifest, using fallback mode');
      this.imageManifest = {};
    }
  }

  setupGameTabs() {
    this.gameTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        
        const gameType = tab.getAttribute('data-game');
        console.log(`🎮 Switching to ${gameType} content`);
        
        this.switchGame(gameType);
      });
    });
  }

  setupSubTabs() {
    this.subTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        
        const race = tab.getAttribute('data-subtab');
        console.log(`🏛️ Switching to ${race} content`);
        
        this.switchRace(race);
      });
    });
  }

  switchGame(gameType) {
    // Update active tab
    this.gameTabs.forEach(tab => {
      tab.classList.remove('active');
      if (tab.getAttribute('data-game') === gameType) {
        tab.classList.add('active');
      }
    });

    // Update active sub-tabs
    this.subTabs.forEach(tab => {
      const subTabsContainer = tab.closest('.sub-tabs');
      if (subTabsContainer.getAttribute('data-game') === gameType) {
        subTabsContainer.classList.add('active');
      } else {
        subTabsContainer.classList.remove('active');
      }
    });

    this.currentGame = gameType;
    
    // Load content for the new game
    this.loadGameContent(gameType);
    
    // Add animation effect
    this.animateContentSwitch();
    
    // Update URL hash for bookmarking
    window.location.hash = `game=${gameType}`;
  }

  switchRace(race) {
    // Update active sub-tab
    const currentSubTabs = document.querySelector(`.sub-tabs[data-game="${this.currentGame}"].active`);
    if (currentSubTabs) {
      currentSubTabs.querySelectorAll('.sub-tab').forEach(tab => {
        tab.classList.remove('active');
        if (tab.getAttribute('data-subtab') === race) {
          tab.classList.add('active');
        }
      });
    }

    // Update active sub-content
    const currentGameContent = document.querySelector(`.game-content[data-game="${this.currentGame}"]`);
    if (currentGameContent) {
      currentGameContent.querySelectorAll('.sub-content').forEach(content => {
        content.classList.remove('active');
        if (content.getAttribute('data-subtab') === race) {
          content.classList.add('active');
        }
      });
    }

    this.currentRace = race;
    
    // Load content for the new race
    this.loadRaceContent(race);
  }

  async loadGameContent(gameType) {
    console.log(`📚 Loading content for ${gameType}...`);
    
    // Show loading indicator
    this.showLoadingIndicator();
    
    // Load units and buildings for the current race
    await this.loadRaceContent(this.currentRace);
    
    // Hide loading indicator
    this.hideLoadingIndicator();
  }

  async loadRaceContent(race) {
    console.log(`🏛️ Loading ${race} content for ${this.currentGame}...`);
    
    // Load units
    await this.loadCategoryContent('units', race);
    
    // Load buildings
    await this.loadCategoryContent('buildings', race);
  }

  async loadCategoryContent(category, race) {
    const containerId = `${this.currentGame}-${race}-${category}`;
    const container = document.getElementById(containerId);
    
    if (!container) {
      console.warn(`Container not found: ${containerId}`);
      return;
    }

    // Get data for this category
    const items = this.getCategoryData(category, race);
    
    // Clear container
    container.innerHTML = '';
    
    // Create cards for each item
    for (const item of items) {
      const card = this.createWikiCard(item, category, race);
      container.appendChild(card);
    }
  }

  getCategoryData(category, race) {
    // Define the data for each game, race, and category
    const gameData = {
      'wc1': {
        'human': {
          'units': [
            { name: 'Peasant', description: 'Basic worker unit. Can gather resources and build structures.', cost: '60 Gold', attack: 'None', armor: '0', hp: '30' },
            { name: 'Footman', description: 'Basic melee infantry unit. Good for early game defense.', cost: '135 Gold', attack: '6', armor: '2', hp: '60' },
            { name: 'Archer', description: 'Ranged unit. Effective against flying units.', cost: '135 Gold', attack: '4', armor: '0', hp: '30' },
            { name: 'Knight', description: 'Heavy cavalry unit. High armor and health.', cost: '270 Gold', attack: '10', armor: '4', hp: '100' },
            { name: 'Paladin', description: 'Holy warrior with healing abilities.', cost: '270 Gold', attack: '8', armor: '3', hp: '80' },
            { name: 'Mage', description: 'Spellcaster with powerful magic abilities.', cost: '270 Gold', attack: '3', armor: '0', hp: '40' },
            { name: 'Conjurer', description: 'Advanced spellcaster with summoning abilities.', cost: '270 Gold', attack: '2', armor: '0', hp: '30' }
          ],
          'buildings': [
            { name: 'Town Hall', description: 'Main building. Required for all other structures.', cost: '400 Gold', hp: '1200' },
            { name: 'Farm', description: 'Provides food for your army.', cost: '75 Gold', hp: '200' },
            { name: 'Barracks', description: 'Trains military units.', cost: '200 Gold', hp: '800' },
            { name: 'Church', description: 'Trains spellcasters and provides upgrades.', cost: '200 Gold', hp: '600' },
            { name: 'Tower', description: 'Defensive structure with ranged attack.', cost: '150 Gold', hp: '400' },
            { name: 'Stable', description: 'Trains cavalry units.', cost: '200 Gold', hp: '800' },
            { name: 'Mage Tower', description: 'Advanced spellcaster training facility.', cost: '200 Gold', hp: '600' }
          ]
        },
        'orc': {
          'units': [
            { name: 'Peon', description: 'Basic worker unit. Can gather resources and build structures.', cost: '60 Gold', attack: 'None', armor: '0', hp: '30' },
            { name: 'Grunt', description: 'Basic melee infantry unit. Stronger than Footman.', cost: '135 Gold', attack: '7', armor: '1', hp: '70' },
            { name: 'Axethrower', description: 'Ranged unit. Effective against flying units.', cost: '135 Gold', attack: '5', armor: '0', hp: '35' },
            { name: 'Raider', description: 'Fast cavalry unit. Good for hit-and-run attacks.', cost: '270 Gold', attack: '8', armor: '2', hp: '80' },
            { name: 'Ogre', description: 'Heavy infantry unit. High health and damage.', cost: '270 Gold', attack: '12', armor: '3', hp: '120' },
            { name: 'Death Knight', description: 'Dark warrior with death magic abilities.', cost: '270 Gold', attack: '9', armor: '2', hp: '90' },
            { name: 'Warlock', description: 'Dark spellcaster with destructive magic.', cost: '270 Gold', attack: '4', armor: '0', hp: '45' }
          ],
          'buildings': [
            { name: 'Great Hall', description: 'Main building. Required for all other structures.', cost: '400 Gold', hp: '1200' },
            { name: 'Pig Farm', description: 'Provides food for your army.', cost: '75 Gold', hp: '200' },
            { name: 'Barracks', description: 'Trains military units.', cost: '200 Gold', hp: '800' },
            { name: 'Temple', description: 'Trains spellcasters and provides upgrades.', cost: '200 Gold', hp: '600' },
            { name: 'Watch Tower', description: 'Defensive structure with ranged attack.', cost: '150 Gold', hp: '400' },
            { name: 'Ogre Mound', description: 'Trains heavy infantry units.', cost: '200 Gold', hp: '800' },
            { name: 'Warlock Tower', description: 'Advanced spellcaster training facility.', cost: '200 Gold', hp: '600' }
          ]
        }
      },
      'wc2': {
        'human': {
          'units': [
            { name: 'Peasant', description: 'Basic worker unit. Can gather resources and build structures.', cost: '60 Gold', attack: 'None', armor: '0', hp: '30' },
            { name: 'Footman', description: 'Basic melee infantry unit. Good for early game defense.', cost: '135 Gold', attack: '6', armor: '2', hp: '60' },
            { name: 'Archer', description: 'Ranged unit. Effective against flying units.', cost: '135 Gold', attack: '4', armor: '0', hp: '30' },
            { name: 'Knight', description: 'Heavy cavalry unit. High armor and health.', cost: '270 Gold', attack: '10', armor: '4', hp: '100' },
            { name: 'Paladin', description: 'Holy warrior with healing abilities.', cost: '270 Gold', attack: '8', armor: '3', hp: '80' },
            { name: 'Mage', description: 'Spellcaster with powerful magic abilities.', cost: '270 Gold', attack: '3', armor: '0', hp: '40' },
            { name: 'Conjurer', description: 'Advanced spellcaster with summoning abilities.', cost: '270 Gold', attack: '2', armor: '0', hp: '30' },
            { name: 'Ballista', description: 'Siege weapon. Effective against buildings.', cost: '400 Gold', attack: '15', armor: '0', hp: '80' },
            { name: 'Gryphon Rider', description: 'Flying unit. Good for scouting and harassment.', cost: '400 Gold', attack: '12', armor: '1', hp: '90' }
          ],
          'buildings': [
            { name: 'Town Hall', description: 'Main building. Required for all other structures.', cost: '400 Gold', hp: '1200' },
            { name: 'Farm', description: 'Provides food for your army.', cost: '75 Gold', hp: '200' },
            { name: 'Barracks', description: 'Trains military units.', cost: '200 Gold', hp: '800' },
            { name: 'Church', description: 'Trains spellcasters and provides upgrades.', cost: '200 Gold', hp: '600' },
            { name: 'Tower', description: 'Defensive structure with ranged attack.', cost: '150 Gold', hp: '400' },
            { name: 'Stable', description: 'Trains cavalry units.', cost: '200 Gold', hp: '800' },
            { name: 'Mage Tower', description: 'Advanced spellcaster training facility.', cost: '200 Gold', hp: '600' },
            { name: 'Blacksmith', description: 'Provides weapon and armor upgrades.', cost: '150 Gold', hp: '500' },
            { name: 'Shipyard', description: 'Trains naval units.', cost: '300 Gold', hp: '1000' }
          ]
        },
        'orc': {
          'units': [
            { name: 'Peon', description: 'Basic worker unit. Can gather resources and build structures.', cost: '60 Gold', attack: 'None', armor: '0', hp: '30' },
            { name: 'Grunt', description: 'Basic melee infantry unit. Stronger than Footman.', cost: '135 Gold', attack: '7', armor: '1', hp: '70' },
            { name: 'Axethrower', description: 'Ranged unit. Effective against flying units.', cost: '135 Gold', attack: '5', armor: '0', hp: '35' },
            { name: 'Raider', description: 'Fast cavalry unit. Good for hit-and-run attacks.', cost: '270 Gold', attack: '8', armor: '2', hp: '80' },
            { name: 'Ogre', description: 'Heavy infantry unit. High health and damage.', cost: '270 Gold', attack: '12', armor: '3', hp: '120' },
            { name: 'Death Knight', description: 'Dark warrior with death magic abilities.', cost: '270 Gold', attack: '9', armor: '2', hp: '90' },
            { name: 'Warlock', description: 'Dark spellcaster with destructive magic.', cost: '270 Gold', attack: '4', armor: '0', hp: '45' },
            { name: 'Catapult', description: 'Siege weapon. Effective against buildings.', cost: '400 Gold', attack: '18', armor: '0', hp: '100' },
            { name: 'Dragon', description: 'Flying unit. Powerful but expensive.', cost: '500 Gold', attack: '20', armor: '2', hp: '150' }
          ],
          'buildings': [
            { name: 'Great Hall', description: 'Main building. Required for all other structures.', cost: '400 Gold', hp: '1200' },
            { name: 'Pig Farm', description: 'Provides food for your army.', cost: '75 Gold', hp: '200' },
            { name: 'Barracks', description: 'Trains military units.', cost: '200 Gold', hp: '800' },
            { name: 'Temple', description: 'Trains spellcasters and provides upgrades.', cost: '200 Gold', hp: '600' },
            { name: 'Watch Tower', description: 'Defensive structure with ranged attack.', cost: '150 Gold', hp: '400' },
            { name: 'Ogre Mound', description: 'Trains heavy infantry units.', cost: '200 Gold', hp: '800' },
            { name: 'Warlock Tower', description: 'Advanced spellcaster training facility.', cost: '200 Gold', hp: '600' },
            { name: 'Blacksmith', description: 'Provides weapon and armor upgrades.', cost: '150 Gold', hp: '500' },
            { name: 'Shipyard', description: 'Trains naval units.', cost: '300 Gold', hp: '1000' }
          ]
        }
      },
      'wc3': {
        'human': {
          'units': [
            { name: 'Peasant', description: 'Basic worker unit. Can gather resources and build structures.', cost: '75 Gold', attack: 'None', armor: '0', hp: '220' },
            { name: 'Footman', description: 'Basic melee infantry unit. Good for early game defense.', cost: '135 Gold', attack: '12-13', armor: '2', hp: '420' },
            { name: 'Knight', description: 'Heavy cavalry unit. High armor and health.', cost: '245 Gold', attack: '25-26', armor: '5', hp: '835' },
            { name: 'Paladin', description: 'Holy warrior with healing abilities.', cost: '425 Gold', attack: '24-25', armor: '4', hp: '1000' },
            { name: 'Priest', description: 'Support unit with healing abilities.', cost: '135 Gold', attack: 'None', armor: '0', hp: '340' },
            { name: 'Sorceress', description: 'Spellcaster with polymorph and slow abilities.', cost: '155 Gold', attack: 'None', armor: '0', hp: '305' },
            { name: 'Mage', description: 'Spellcaster with water elemental summoning.', cost: '155 Gold', attack: 'None', armor: '0', hp: '305' },
            { name: 'Gryphon Rider', description: 'Flying unit. Good for scouting and harassment.', cost: '280 Gold', attack: '45-55', armor: '2', hp: '825' },
            { name: 'Mortar Team', description: 'Siege weapon. Effective against buildings.', cost: '180 Gold', attack: '62-68', armor: '0', hp: '360' },
            { name: 'Siege Engine', description: 'Heavy siege weapon. Very effective against buildings.', cost: '195 Gold', attack: '75-83', armor: '2', hp: '700' }
          ],
          'buildings': [
            { name: 'Town Hall', description: 'Main building. Required for all other structures.', cost: '400 Gold', hp: '1500' },
            { name: 'Farm', description: 'Provides food for your army.', cost: '80 Gold', hp: '500' },
            { name: 'Barracks', description: 'Trains military units.', cost: '205 Gold', hp: '1200' },
            { name: 'Blacksmith', description: 'Provides weapon and armor upgrades.', cost: '150 Gold', hp: '1000' },
            { name: 'Workshop', description: 'Trains siege weapons.', cost: '185 Gold', hp: '1200' },
            { name: 'Lumber Mill', description: 'Provides lumber upgrades.', cost: '150 Gold', hp: '1000' },
            { name: 'Keep', description: 'Upgraded Town Hall. Provides additional upgrades.', cost: 'N/A', hp: '2000' },
            { name: 'Castle', description: 'Fully upgraded Town Hall. Maximum upgrades available.', cost: 'N/A', hp: '2500' },
            { name: 'Tower', description: 'Basic defensive structure.', cost: '150 Gold', hp: '600' },
            { name: 'Guard Tower', description: 'Upgraded tower with better attack.', cost: 'N/A', hp: '800' },
            { name: 'Cannon Tower', description: 'Advanced tower with siege damage.', cost: 'N/A', hp: '1000' }
          ]
        },
        'orc': {
          'units': [
            { name: 'Peon', description: 'Basic worker unit. Can gather resources and build structures.', cost: '75 Gold', attack: 'None', armor: '0', hp: '220' },
            { name: 'Grunt', description: 'Basic melee infantry unit. Stronger than Footman.', cost: '135 Gold', attack: '14-15', armor: '1', hp: '700' },
            { name: 'Raider', description: 'Fast cavalry unit. Good for hit-and-run attacks.', cost: '245 Gold', attack: '25-26', armor: '3', hp: '770' },
            { name: 'Troll Headhunter', description: 'Ranged unit. Effective against flying units.', cost: '135 Gold', attack: '16-19', armor: '0', hp: '400' },
            { name: 'Kodo Beast', description: 'Heavy support unit with war drums ability.', cost: '255 Gold', attack: '25-35', armor: '3', hp: '1000' },
            { name: 'Tauren', description: 'Heavy infantry unit. High health and damage.', cost: '280 Gold', attack: '33-43', armor: '5', hp: '1000' },
            { name: 'Shaman', description: 'Support unit with bloodlust and purge abilities.', cost: '155 Gold', attack: 'None', armor: '0', hp: '305' },
            { name: 'Witch Doctor', description: 'Support unit with healing ward and stasis trap.', cost: '155 Gold', attack: 'None', armor: '0', hp: '305' },
            { name: 'Spirit Walker', description: 'Support unit with spirit link and ancestral spirit.', cost: '155 Gold', attack: 'None', armor: '0', hp: '305' },
            { name: 'Wind Rider', description: 'Flying unit. Good for scouting and harassment.', cost: '280 Gold', attack: '45-55', armor: '2', hp: '825' },
            { name: 'Catapult', description: 'Siege weapon. Effective against buildings.', cost: '180 Gold', attack: '62-68', armor: '0', hp: '360' }
          ],
          'buildings': [
            { name: 'Great Hall', description: 'Main building. Required for all other structures.', cost: '400 Gold', hp: '1500' },
            { name: 'Pig Farm', description: 'Provides food for your army.', cost: '80 Gold', hp: '500' },
            { name: 'Barracks', description: 'Trains military units.', cost: '205 Gold', hp: '1200' },
            { name: 'War Mill', description: 'Provides weapon and armor upgrades.', cost: '150 Gold', hp: '1000' },
            { name: 'Tauren Totem', description: 'Trains heavy infantry units.', cost: '185 Gold', hp: '1200' },
            { name: 'Stronghold', description: 'Upgraded Great Hall. Provides additional upgrades.', cost: 'N/A', hp: '2000' },
            { name: 'Fortress', description: 'Fully upgraded Great Hall. Maximum upgrades available.', cost: 'N/A', hp: '2500' },
            { name: 'Tower', description: 'Basic defensive structure.', cost: '150 Gold', hp: '600' },
            { name: 'Watch Tower', description: 'Upgraded tower with better attack.', cost: 'N/A', hp: '800' },
            { name: 'Cannon Tower', description: 'Advanced tower with siege damage.', cost: 'N/A', hp: '1000' }
          ]
        },
        'undead': {
          'units': [
            { name: 'Acolyte', description: 'Basic worker unit. Can gather resources and build structures.', cost: '75 Gold', attack: 'None', armor: '0', hp: '220' },
            { name: 'Ghoul', description: 'Basic melee infantry unit. Can harvest lumber.', cost: '120 Gold', attack: '13-14', armor: '0', hp: '340' },
            { name: 'Crypt Fiend', description: 'Ranged unit. Can web flying units.', cost: '215 Gold', attack: '16-19', armor: '0', hp: '550' },
            { name: 'Gargoyle', description: 'Flying unit. Good for scouting and harassment.', cost: '185 Gold', attack: '25-30', armor: '2', hp: '410' },
            { name: 'Abomination', description: 'Heavy infantry unit. High health and damage.', cost: '240 Gold', attack: '28-38', armor: '3', hp: '1175' },
            { name: 'Necromancer', description: 'Spellcaster with raise dead and cripple abilities.', cost: '155 Gold', attack: 'None', armor: '0', hp: '305' },
            { name: 'Banshee', description: 'Spellcaster with curse and possession abilities.', cost: '155 Gold', attack: 'None', armor: '0', hp: '305' },
            { name: 'Meat Wagon', description: 'Siege weapon. Can carry corpses.', cost: '180 Gold', attack: '62-68', armor: '0', hp: '360' },
            { name: 'Frost Wyrm', description: 'Flying unit. Powerful but expensive.', cost: '385 Gold', attack: '45-55', armor: '2', hp: '825' }
          ],
          'buildings': [
            { name: 'Necropolis', description: 'Main building. Required for all other structures.', cost: '400 Gold', hp: '1500' },
            { name: 'Ziggurat', description: 'Provides food for your army.', cost: '80 Gold', hp: '500' },
            { name: 'Crypt', description: 'Trains military units.', cost: '205 Gold', hp: '1200' },
            { name: 'Graveyard', description: 'Provides upgrades and corpse storage.', cost: '150 Gold', hp: '1000' },
            { name: 'Temple of the Damned', description: 'Trains spellcasters.', cost: '185 Gold', hp: '1200' },
            { name: 'Slaughterhouse', description: 'Trains heavy infantry units.', cost: '185 Gold', hp: '1200' },
            { name: 'Tomb of Relics', description: 'Provides upgrades.', cost: '150 Gold', hp: '1000' },
            { name: 'Halls of the Dead', description: 'Upgraded Necropolis. Provides additional upgrades.', cost: 'N/A', hp: '2000' },
            { name: 'Black Citadel', description: 'Fully upgraded Necropolis. Maximum upgrades available.', cost: 'N/A', hp: '2500' },
            { name: 'Tower', description: 'Basic defensive structure.', cost: '150 Gold', hp: '600' },
            { name: 'Nerubian Tower', description: 'Upgraded tower with better attack.', cost: 'N/A', hp: '800' },
            { name: 'Frost Tower', description: 'Advanced tower with slow effect.', cost: 'N/A', hp: '1000' }
          ]
        },
        'nightelf': {
          'units': [
            { name: 'Wisp', description: 'Basic worker unit. Can gather resources and build structures.', cost: '75 Gold', attack: 'None', armor: '0', hp: '220' },
            { name: 'Archer', description: 'Ranged unit. Effective against flying units.', cost: '130 Gold', attack: '16-19', armor: '0', hp: '310' },
            { name: 'Huntress', description: 'Fast melee unit. Good for scouting and harassment.', cost: '195 Gold', attack: '18-22', armor: '3', hp: '600' },
            { name: 'Dryad', description: 'Support unit with abolish magic and slow poison.', cost: '155 Gold', attack: 'None', armor: '0', hp: '305' },
            { name: 'Druid of the Claw', description: 'Shapeshifting unit. Can be bear or crow form.', cost: '255 Gold', attack: '25-35', armor: '3', hp: '1000' },
            { name: 'Druid of the Talon', description: 'Spellcaster with cyclone and faerie fire abilities.', cost: '155 Gold', attack: 'None', armor: '0', hp: '305' },
            { name: 'Hippogryph', description: 'Flying unit. Good for scouting and harassment.', cost: '280 Gold', attack: '45-55', armor: '2', hp: '825' },
            { name: 'Chimaera', description: 'Flying unit. Powerful but expensive.', cost: '385 Gold', attack: '45-55', armor: '2', hp: '825' },
            { name: 'Mountain Giant', description: 'Heavy infantry unit. High health and damage.', cost: '425 Gold', attack: '33-43', armor: '5', hp: '1000' }
          ],
          'buildings': [
            { name: 'Tree of Life', description: 'Main building. Required for all other structures.', cost: '400 Gold', hp: '1500' },
            { name: 'Moon Well', description: 'Provides food and healing for your army.', cost: '80 Gold', hp: '500' },
            { name: 'Ancient of War', description: 'Trains military units.', cost: '205 Gold', hp: '1200' },
            { name: 'Hunter\'s Hall', description: 'Trains ranged units.', cost: '185 Gold', hp: '1200' },
            { name: 'Ancient of Lore', description: 'Trains spellcasters.', cost: '185 Gold', hp: '1200' },
            { name: 'Ancient of Wind', description: 'Trains flying units.', cost: '185 Gold', hp: '1200' },
            { name: 'Chimaera Roost', description: 'Trains flying units.', cost: '185 Gold', hp: '1200' },
            { name: 'Ancient Protector', description: 'Defensive structure. Can uproot and move.', cost: '150 Gold', hp: '600' }
          ]
        },
        'neutral': {
          'units': [
            { name: 'Goblin Sapper', description: 'Explosive unit. Can destroy buildings.', cost: '185 Gold', attack: 'None', armor: '0', hp: '220' },
            { name: 'Goblin Tinker', description: 'Support unit with pocket factory and clockwerk goblins.', cost: '425 Gold', attack: 'None', armor: '0', hp: '305' },
            { name: 'Goblin Shredder', description: 'Siege weapon. Can harvest lumber.', cost: '195 Gold', attack: '75-83', armor: '2', hp: '700' },
            { name: 'Goblin Zeppelin', description: 'Transport unit. Can carry units and resources.', cost: '180 Gold', attack: 'None', armor: '0', hp: '360' },
            { name: 'Pandaren Brewmaster', description: 'Hero unit with drunken haze and breath of fire.', cost: '425 Gold', attack: '24-25', armor: '4', hp: '1000' },
            { name: 'Firelord', description: 'Hero unit with soul burn and summon lava spawn.', cost: '425 Gold', attack: '24-25', armor: '4', hp: '1000' },
            { name: 'Dark Ranger', description: 'Hero unit with silence and black arrow.', cost: '425 Gold', attack: '24-25', armor: '4', hp: '1000' },
            { name: 'Beastmaster', description: 'Hero unit with summon bear and hawk.', cost: '425 Gold', attack: '24-25', armor: '4', hp: '1000' },
            { name: 'Pit Lord', description: 'Hero unit with howl of terror and doom.', cost: '425 Gold', attack: '24-25', armor: '4', hp: '1000' },
            { name: 'Naga Sea Witch', description: 'Hero unit with forked lightning and mana shield.', cost: '425 Gold', attack: '24-25', armor: '4', hp: '1000' },
            { name: 'Tinker', description: 'Hero unit with pocket factory and clockwerk goblins.', cost: '425 Gold', attack: '24-25', armor: '4', hp: '1000' },
            { name: 'Alchemist', description: 'Hero unit with healing spray and acid bomb.', cost: '425 Gold', attack: '24-25', armor: '4', hp: '1000' }
          ],
          'buildings': [
            { name: 'Mercenary Camp', description: 'Trains neutral units.', cost: '185 Gold', hp: '1200' },
            { name: 'Goblin Laboratory', description: 'Trains goblin units.', cost: '185 Gold', hp: '1200' },
            { name: 'Marketplace', description: 'Provides upgrades and items.', cost: '150 Gold', hp: '1000' },
            { name: 'Tavern', description: 'Hires neutral heroes.', cost: '150 Gold', hp: '1000' },
            { name: 'Dragon Roost', description: 'Trains flying units.', cost: '185 Gold', hp: '1200' },
            { name: 'Fountain of Life', description: 'Provides healing and mana regeneration.', cost: '150 Gold', hp: '1000' },
            { name: 'Goblin Merchant', description: 'Sells items and upgrades.', cost: '150 Gold', hp: '1000' }
          ]
        }
      }
    };

    return gameData[this.currentGame]?.[race]?.[category] || [];
  }

  createWikiCard(item, category, race) {
    const card = document.createElement('div');
    card.className = 'wiki-card';
    
    // Get image path
    const imagePath = this.getImagePath(item.name, category, race);
    
    card.innerHTML = `
      <div class="wiki-card-image">
        <img src="${imagePath}" alt="${item.name}" onerror="this.src='/assets/img/default-unit.png'">
      </div>
      <div class="wiki-card-content">
        <h4 class="wiki-card-title">${item.name}</h4>
        <p class="wiki-card-description">${item.description}</p>
        <div class="wiki-card-stats">
          ${item.cost ? `<div class="stat"><span class="stat-label">Cost:</span> <span class="stat-value">${item.cost}</span></div>` : ''}
          ${item.attack ? `<div class="stat"><span class="stat-label">Attack:</span> <span class="stat-value">${item.attack}</span></div>` : ''}
          ${item.armor ? `<div class="stat"><span class="stat-label">Armor:</span> <span class="stat-value">${item.armor}</span></div>` : ''}
          ${item.hp ? `<div class="stat"><span class="stat-label">HP:</span> <span class="stat-value">${item.hp}</span></div>` : ''}
        </div>
      </div>
    `;
    
    return card;
  }

  getImagePath(itemName, category, race) {
    // Try to get image from manifest
    if (this.imageManifest && 
        this.imageManifest[this.currentGame] && 
        this.imageManifest[this.currentGame][category] && 
        this.imageManifest[this.currentGame][category][race]) {
      
      const items = this.imageManifest[this.currentGame][category][race];
      const item = items.find(i => i.name.toLowerCase() === itemName.toLowerCase());
      
      if (item) {
        return `/assets/img/wizards-tower/${item.path}`;
      }
    }
    
    // Fallback to default image
    return `/assets/img/default-${category.slice(0, -1)}.png`;
  }

  showLoadingIndicator() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.style.display = 'flex';
    }
  }

  hideLoadingIndicator() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.style.display = 'none';
    }
  }

  animateContentSwitch() {
    const activeContent = document.querySelector(`.wiki-content[data-game="${this.currentGame}"]`);
    
    if (activeContent) {
      // Add fade-in animation
      activeContent.style.opacity = '0';
      activeContent.style.transform = 'translateY(20px)';
      
      setTimeout(() => {
        activeContent.style.transition = 'all 0.3s ease-in-out';
        activeContent.style.opacity = '1';
        activeContent.style.transform = 'translateY(0)';
      }, 50);
    }
  }

  // Method to search wiki content
  searchWiki(query) {
    console.log(`🔍 Searching for: ${query}`);
    
    const searchResults = [];
    const wikiCards = document.querySelectorAll('.wiki-card');
    
    wikiCards.forEach(card => {
      const title = card.querySelector('.wiki-card-title').textContent.toLowerCase();
      const description = card.querySelector('.wiki-card-description').textContent.toLowerCase();
      const searchTerm = query.toLowerCase();
      
      if (title.includes(searchTerm) || description.includes(searchTerm)) {
        searchResults.push(card);
      }
    });
    
    return searchResults;
  }

  // Method to highlight search results
  highlightSearchResults(results) {
    // Remove previous highlights
    document.querySelectorAll('.wiki-card').forEach(card => {
      card.style.borderColor = '';
      card.style.backgroundColor = '';
    });
    
    // Highlight search results
    results.forEach(card => {
      card.style.borderColor = 'var(--primary-gold)';
      card.style.backgroundColor = 'rgba(212, 175, 55, 0.1)';
    });
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Check if we're on the Wizard's Tower page
  if (document.querySelector('.wizards-tower-container')) {
    window.enhancedWizardsTowerManager = new EnhancedWizardsTowerManager();
    
    // Handle URL hash for direct navigation
    if (window.location.hash) {
      const hashParams = new URLSearchParams(window.location.hash.substring(1));
      const gameType = hashParams.get('game');
      
      if (gameType && ['wc1', 'wc2', 'wc3'].includes(gameType)) {
        window.enhancedWizardsTowerManager.switchGame(gameType);
      }
    }
  }
});

// Export for module usage
// EnhancedWizardsTowerManager is available globally as window.enhancedWizardsTowerManager 