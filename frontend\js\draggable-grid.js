/**
 * DRAGGABLE PROFILE GRID SYSTEM
 * Handles drag & drop, minimize/maximize, and section positioning
 * Extracted from myprofile.html for better maintainability
 */

class DraggableProfileGrid {
  constructor() {
    this.container = null;
    this.draggedElement = null;
    this.init();
  }

  init() {
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.initialize());
    } else {
      this.initialize();
    }
  }

  initialize() {
    this.container = document.getElementById('profile-grid-container');
    if (!this.container) {
      console.warn('Profile grid container not found');
      return;
    }

    // Initialize grid system
    this.initializeGridPositions();
    this.setupSectionControls();
    this.setupLayoutControls();
    this.setupDragAndDrop();
    this.setupMainLayoutButtons(); // Setup main minimize/maximize buttons

    console.log('✅ Draggable grid system initialized');
  }

  /**
   * Initialize grid positions for all sections
   */
  initializeGridPositions() {
    const sections = this.container.querySelectorAll('.draggable-section');
    
    console.log('🏗️ Initializing grid positions for', sections.length, 'sections');
    
    // Use compactGrid for initial positioning to ensure optimal layout
    this.compactGrid();
  }

  /**
   * Setup section control buttons (minimize/maximize)
   */
  setupSectionControls() {
    this.container.addEventListener('click', (e) => {
      if (e.target.closest('.section-control-btn')) {
        const btn = e.target.closest('.section-control-btn');
        const section = e.target.closest('.draggable-section');
        
        if (btn.classList.contains('minimize-toggle')) {
          this.toggleMinimize(section, btn);
        }
      }
    });
  }

  /**
   * Setup layout control buttons
   */
  setupLayoutControls() {
    // Find or create layout controls container
    let controlsContainer = document.querySelector('.profile-layout-controls');
    
    if (!controlsContainer) {
      controlsContainer = document.createElement('div');
      controlsContainer.className = 'profile-layout-controls';
      controlsContainer.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        display: flex;
        gap: 0.5rem;
        z-index: 1000;
        opacity: 0.7;
        transition: opacity 0.2s ease;
      `;
      
      // Add hover effect
      controlsContainer.addEventListener('mouseenter', () => {
        controlsContainer.style.opacity = '1';
      });
      controlsContainer.addEventListener('mouseleave', () => {
        controlsContainer.style.opacity = '0.7';
      });
      
      document.body.appendChild(controlsContainer);
    }

    // Compact button
    const compactBtn = document.createElement('button');
    compactBtn.className = 'layout-control-btn compact-btn';
    compactBtn.innerHTML = '<i class="fas fa-compress-arrows-alt"></i>';
    compactBtn.title = 'Compact Layout - Fill Empty Spaces';
    compactBtn.style.cssText = `
      background: rgba(212, 175, 55, 0.9);
      border: none;
      color: #1a1a1a;
      padding: 0.75rem;
      border-radius: 50%;
      cursor: pointer;
      font-size: 1rem;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
      transition: all 0.2s ease;
    `;
    
    compactBtn.addEventListener('mouseenter', () => {
      compactBtn.style.transform = 'scale(1.1)';
      compactBtn.style.background = 'rgba(212, 175, 55, 1)';
    });
    
    compactBtn.addEventListener('mouseleave', () => {
      compactBtn.style.transform = 'scale(1)';
      compactBtn.style.background = 'rgba(212, 175, 55, 0.9)';
    });

    compactBtn.addEventListener('click', () => {
      console.log('🎯 Manual compact triggered');
      this.compactGrid();
      
      // Visual feedback
      compactBtn.style.transform = 'scale(0.9)';
      setTimeout(() => {
        compactBtn.style.transform = 'scale(1)';
      }, 150);
    });

    controlsContainer.appendChild(compactBtn);
  }

  /**
   * Setup main layout buttons (minimize all, maximize all)
   */
  setupMainLayoutButtons() {
    console.log('🔧 Setting up main layout buttons...');
    
    const minimizeAllBtn = document.getElementById('minimize-all');
    const maximizeAllBtn = document.getElementById('maximize-all');
    
    if (minimizeAllBtn) {
      console.log('✅ Found minimize all button, setting up direct listener');
      minimizeAllBtn.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('🔽 Minimize All button clicked (direct)');
        this.minimizeAll();
      });
    } else {
      console.warn('⚠️ Minimize all button not found in DOM');
    }
    
    if (maximizeAllBtn) {
      console.log('✅ Found maximize all button, setting up direct listener');
      maximizeAllBtn.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('🔼 Maximize All button clicked (direct)');
        this.maximizeAll();
      });
    } else {
      console.warn('⚠️ Maximize all button not found in DOM');
    }
  }

  /**
   * Setup drag and drop functionality
   */
  setupDragAndDrop() {
    const sections = this.container.querySelectorAll('.draggable-section');
    sections.forEach(section => {
      section.draggable = true;
      
      section.addEventListener('dragstart', (e) => {
        this.draggedElement = section;
        section.classList.add('dragging');
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', section.outerHTML);
        console.log('Drag started for section:', section.id);
      });

      section.addEventListener('dragend', (e) => {
        section.classList.remove('dragging');
        // Remove any drag target highlights
        this.container.querySelectorAll('.drag-target').forEach(el => {
          el.classList.remove('drag-target');
        });
        this.draggedElement = null;
        console.log('Drag ended for section:', section.id);
      });

      // Handle dragging over other sections for visual feedback
      section.addEventListener('dragenter', (e) => {
        if (this.draggedElement && section !== this.draggedElement) {
          section.classList.add('drag-target');
        }
      });

      section.addEventListener('dragleave', (e) => {
        // Only remove highlight if we're actually leaving the section
        if (!section.contains(e.relatedTarget)) {
          section.classList.remove('drag-target');
        }
      });

      section.addEventListener('dragover', (e) => {
        if (this.draggedElement && section !== this.draggedElement) {
          e.preventDefault();
          e.dataTransfer.dropEffect = 'move';
        }
      });

      section.addEventListener('drop', (e) => {
        e.preventDefault();
        if (this.draggedElement && section !== this.draggedElement) {
          section.classList.remove('drag-target');
          this.swapSections(this.draggedElement, section);
          console.log('Swapped sections:', this.draggedElement.id, 'with', section.id);
        }
      });
    });

    // Handle drops on empty areas of the container
    this.container.addEventListener('dragover', (e) => {
      // Only allow drop if we're not over a section
      if (!e.target.closest('.draggable-section') && this.draggedElement) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
      }
    });

    this.container.addEventListener('drop', (e) => {
      // Only handle drops on empty areas
      if (!e.target.closest('.draggable-section') && this.draggedElement) {
        e.preventDefault();
        
        // Calculate drop position based on mouse coordinates
        const rect = this.container.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        console.log('Container drop - Mouse position:', { x, y });
        console.log('Container rect:', rect);
        
        // Find the best position to drop the section
        const targetPosition = this.calculateDropPosition(x, y);
        console.log('Calculated target position:', targetPosition);
        
        if (targetPosition) {
          // Set the target position temporarily
          this.draggedElement.dataset.position = (targetPosition.row * 2) + targetPosition.col + 1;
          console.log('Moved section to target position, now compacting...');
          
          // Compact the grid to ensure optimal layout
          this.compactGrid();
        } else {
          console.log('No valid target position found, compacting current layout...');
          this.compactGrid();
        }
      }
    });
  }

  /**
   * Toggle minimize/restore state of a section and synchronize its row partner
   * 
   * This function implements synchronized row behavior where both sections in 
   * the same row (left and right columns) will have matching minimize states.
   * 
   * Row Layout:
   * - Positions 1,2 are in row 1 (left/right)
   * - Positions 3,4 are in row 2 (left/right)  
   * - Positions 5,6 are in row 3 (left/right)
   * - etc.
   * 
   * When you minimize/restore one section, its row partner will automatically
   * be synchronized to the same state for a cleaner, more organized layout.
   * 
   * @param {HTMLElement} section - The section element to toggle
   * @param {HTMLElement} btn - The minimize/restore button that was clicked
   */
  toggleMinimize(section, btn) {
    const content = section.querySelector('.section-content');
    const btnText = btn.querySelector('.btn-text');
    const indicator = section.querySelector('.grid-position-indicator');
    
    const isMinimizing = !section.classList.contains('minimized');
    
    if (section.classList.contains('minimized')) {
      // Restore section (show content)
      section.classList.remove('minimized');
      if (content) content.style.display = 'block';
      btnText.textContent = 'Minimize';
      btn.title = 'Hide section content';
      if (indicator) {
        indicator.textContent = indicator.textContent.replace(' (Min)', '');
      }
      console.log('🔼 Section restored:', section.id);
    } else {
      // Minimize section (hide content)
      section.classList.add('minimized');
      if (content) content.style.display = 'none';
      btnText.textContent = 'Restore';
      btn.title = 'Show section content';
      if (indicator) {
        indicator.textContent = indicator.textContent.replace(' (Min)', '') + ' (Min)';
      }
      console.log('🔽 Section minimized:', section.id);
    }
    
    // Find and synchronize the row partner (adjacent section in same row)
    this.synchronizeRowPartner(section, isMinimizing);
    
    // Immediately reflow all sections to fill gaps
    this.reflowSections();
    
    // Notify layout system of change
    if (window.profileCore) {
      window.profileCore.updateSectionPosition(section);
    }
  }

  /**
   * Synchronize the minimize/restore state with the row partner
   */
  synchronizeRowPartner(section, shouldMinimize) {
    const currentPosition = parseInt(section.dataset.position) || 1;
    
    // Calculate row partner position
    // Positions 1,2 are in row 1; 3,4 are in row 2; etc.
    let partnerPosition;
    if (currentPosition % 2 === 1) {
      // Odd position (left column) - partner is +1 (right column)
      partnerPosition = currentPosition + 1;
    } else {
      // Even position (right column) - partner is -1 (left column)
      partnerPosition = currentPosition - 1;
    }
    
    // Find the partner section
    const partnerSection = this.container.querySelector(`[data-position="${partnerPosition}"]`);
    
    if (!partnerSection) {
      console.log(`📍 No row partner found for position ${currentPosition} (looking for position ${partnerPosition})`);
      return;
    }
    
    // Check if partner is already in the correct state
    const partnerIsMinimized = partnerSection.classList.contains('minimized');
    if ((shouldMinimize && partnerIsMinimized) || (!shouldMinimize && !partnerIsMinimized)) {
      console.log(`📍 Row partner ${partnerSection.id} already in correct state`);
      return;
    }
    
    // Synchronize partner section state
    const partnerContent = partnerSection.querySelector('.section-content');
    const partnerBtn = partnerSection.querySelector('.minimize-toggle');
    const partnerBtnText = partnerBtn?.querySelector('.btn-text');
    const partnerIndicator = partnerSection.querySelector('.grid-position-indicator');
    
    if (shouldMinimize) {
      // Minimize partner
      partnerSection.classList.add('minimized');
      if (partnerContent) partnerContent.style.display = 'none';
      if (partnerBtnText) partnerBtnText.textContent = 'Restore';
      if (partnerBtn) partnerBtn.title = 'Show section content';
      if (partnerIndicator) {
        partnerIndicator.textContent = partnerIndicator.textContent.replace(' (Min)', '') + ' (Min)';
      }
      console.log(`🔽 Synchronized minimize for row partner: ${partnerSection.id}`);
    } else {
      // Restore partner
      partnerSection.classList.remove('minimized');
      if (partnerContent) partnerContent.style.display = 'block';
      if (partnerBtnText) partnerBtnText.textContent = 'Minimize';
      if (partnerBtn) partnerBtn.title = 'Hide section content';
      if (partnerIndicator) {
        partnerIndicator.textContent = partnerIndicator.textContent.replace(' (Min)', '');
      }
      console.log(`🔼 Synchronized restore for row partner: ${partnerSection.id}`);
    }
  }



  /**
   * Move section to specific grid position
   */
  moveToGridPosition(section, row, col) {
    console.log(`📦 Moving ${section.id} to row ${row + 1}, col ${col + 1}`);
    
    // Set CSS Grid positions - all sections are normal width
    section.style.gridRow = row + 1;
    section.style.gridColumn = col + 1;
    
    // Update position data
    const newPosition = (row * 2) + col + 1;
    section.dataset.position = newPosition;
    
    // Update position indicator
    const indicator = section.querySelector('.grid-position-indicator');
    if (indicator) {
      const baseText = `Position ${newPosition}`;
      let statusText = '';
      
      if (section.classList.contains('minimized')) statusText += ' (Min)';
      
      indicator.textContent = baseText + statusText;
    }
    
    console.log(`✅ Section ${section.id} moved to position ${newPosition}`);
  }

  /**
   * Swap positions of two sections
   */
  swapSections(section1, section2) {
    console.log(`🔄 Swapping sections: ${section1.id} ↔ ${section2.id}`);
    
    // Simply swap the position data attributes
    const pos1 = parseInt(section1.dataset.position) || 1;
    const pos2 = parseInt(section2.dataset.position) || 1;
    
    section1.dataset.position = pos2;
    section2.dataset.position = pos1;
    
    console.log(`📊 Position data swapped: ${section1.id}=${pos2}, ${section2.id}=${pos1}`);
    
    // Compact the grid to ensure optimal layout
    this.compactGrid();
  }

  /**
   * Update position indicator text
   */
  updatePositionIndicator(section) {
    const indicator = section.querySelector('.grid-position-indicator');
    if (indicator) {
      const position = section.dataset.position;
      let text = `Position ${position}`;
      
      if (section.classList.contains('minimized')) text += ' (Min)';
      
      indicator.textContent = text;
    }
  }

  /**
   * Calculate drop position based on mouse coordinates
   */
  calculateDropPosition(x, y) {
    const containerRect = this.container.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;
    
    // Determine column (left or right half)
    const col = x < (containerWidth / 2) ? 0 : 1;
    
    // Estimate row based on section height (assuming ~200px per row)
    const estimatedSectionHeight = 200;
    const row = Math.floor(y / estimatedSectionHeight);
    
    return { row: Math.max(0, row), col };
  }

  /**
   * Reorganize the entire grid to ensure proper layout
   * This ensures all sections are properly positioned in a 2-column grid
   */
  reorganizeGrid() {
    console.log('🔄 Reorganizing and compacting grid layout...');
    this.compactGrid();
  }

    /**
   * Simple reflow - just use the existing compact grid method
   */
  reflowSections() {
    console.log('🌊 Using simple compact grid...');
    
    // Clear any custom grid positioning
    const sections = this.container.querySelectorAll('.draggable-section');
    sections.forEach(section => {
      section.style.gridRow = '';
      section.style.gridColumn = '';
    });
    
    // Reset container grid
    this.container.style.gridTemplateRows = '';
    
    // Use the existing compact grid method
    this.compactGrid();
  }

  /**
   * Compact grid by filling all empty spaces from top to bottom
   * This creates a "tetris-like" effect where sections automatically fill gaps
   */
  compactGrid() {
    console.log('📦 Compacting grid - filling empty spaces...');
    
    const sections = Array.from(this.container.querySelectorAll('.draggable-section'));
    
    if (sections.length === 0) {
      console.log('⚠️ No sections found to compact');
      return;
    }
    
    // Sort by current position to maintain relative order
    sections.sort((a, b) => {
      const posA = parseInt(a.dataset.position) || 0;
      const posB = parseInt(b.dataset.position) || 0;
      return posA - posB;
    });
    
    let currentRow = 0;
    let currentCol = 0;
    
    sections.forEach((section, index) => {
      // Place section in next available position
      section.style.gridRow = currentRow + 1;
      section.style.gridColumn = currentCol + 1;
      
      // Update position data
      const newPosition = (currentRow * 2) + currentCol + 1;
      section.dataset.position = newPosition;
      
      console.log(`📏 Compacted section ${section.id} to row ${currentRow + 1}, col ${currentCol + 1} (pos ${newPosition})`);
      
      // Move to next position in 2-column grid
      currentCol++;
      if (currentCol >= 2) {
        currentRow++;
        currentCol = 0;
      }
      
      // Update position indicator
      this.updatePositionIndicator(section);
    });
    
    console.log(`✅ Grid compacted - ${sections.length} sections optimally positioned`);
  }

  /**
   * Find next available grid position
   */
  findNextAvailablePosition(allSections, avoidRow = -1) {
    const occupiedPositions = new Set();
    
    console.log(`🔍 Finding available position, avoiding row ${avoidRow + 1}`);
    
    // Map all currently occupied positions
    allSections.forEach(section => {
      const row = parseInt(section.style.gridRow) || 1;
      const col = parseInt(section.style.gridColumn) || 1;
      
      const rowIndex = row - 1; // Convert to 0-indexed
      const colIndex = col - 1; // Convert to 0-indexed
      
      occupiedPositions.add(`${rowIndex}-${colIndex}`);
      console.log(`📍 Position ${row}-${col} occupied by section ${section.id}`);
    });
    
    // Find first available position
    for (let row = 0; row < 20; row++) { // Check up to 20 rows
      if (row === avoidRow) {
        console.log(`⏭️ Skipping row ${row + 1} (avoided)`);
        continue; // Skip the row we're trying to avoid
      }
      
      // Check if positions in this row are available
      const leftOccupied = occupiedPositions.has(`${row}-0`);
      const rightOccupied = occupiedPositions.has(`${row}-1`);
      
      if (!leftOccupied && !rightOccupied) {
        // Entire row is free - return left column
        console.log(`✅ Found free row ${row + 1}, returning left column`);
        return { row, col: 0 };
      } else if (!leftOccupied) {
        // Left column is free
        console.log(`✅ Found free left column in row ${row + 1}`);
        return { row, col: 0 };
      } else if (!rightOccupied) {
        // Right column is free
        console.log(`✅ Found free right column in row ${row + 1}`);
        return { row, col: 1 };
      }
    }
    
    // Fallback: find the first completely empty row after all used rows
    const maxRow = Math.max(0, ...Array.from(occupiedPositions).map(pos => parseInt(pos.split('-')[0])));
    const fallbackRow = maxRow + 1;
    console.log(`⚠️ Using fallback position: row ${fallbackRow + 1}, left column`);
    return { row: fallbackRow, col: 0 };
  }

  /**
   * Minimize all sections
   */
  minimizeAll() {
    console.log('🔽 Minimizing all sections...');
    const sections = this.container.querySelectorAll('.draggable-section');
    let changed = false;
    
    sections.forEach(section => {
      if (!section.classList.contains('minimized')) {
        const content = section.querySelector('.section-content');
        const btn = section.querySelector('.minimize-toggle');
        const btnText = btn?.querySelector('.btn-text');
        const indicator = section.querySelector('.grid-position-indicator');
        
        // Minimize section without triggering individual reflow
        section.classList.add('minimized');
        if (content) content.style.display = 'none';
        if (btnText) btnText.textContent = 'Restore';
        if (btn) btn.title = 'Show section content';
        if (indicator) {
          indicator.textContent = indicator.textContent.replace(' (Min)', '') + ' (Min)';
        }
        
        changed = true;
        console.log('🔽 Section minimized:', section.id);
      }
    });
    
    // Reflow all sections once after all changes
    if (changed) {
      this.reflowSections();
    }
  }

  /**
   * Maximize all sections (restore from minimized)
   */
  maximizeAll() {
    console.log('🔼 Restoring all sections...');
    const sections = this.container.querySelectorAll('.draggable-section');
    let changed = false;
    
    sections.forEach(section => {
      if (section.classList.contains('minimized')) {
        const content = section.querySelector('.section-content');
        const btn = section.querySelector('.minimize-toggle');
        const btnText = btn?.querySelector('.btn-text');
        const indicator = section.querySelector('.grid-position-indicator');
        
        // Restore section without triggering individual reflow
        section.classList.remove('minimized');
        if (content) content.style.display = 'block';
        if (btnText) btnText.textContent = 'Minimize';
        if (btn) btn.title = 'Hide section content';
        if (indicator) {
          indicator.textContent = indicator.textContent.replace(' (Min)', '');
        }
        
        changed = true;
        console.log('🔼 Section restored:', section.id);
      }
    });
    
    // Reflow all sections once after all changes
    if (changed) {
      this.reflowSections();
    }
  }
}

// Global instance
window.DraggableProfileGrid = DraggableProfileGrid;

// Auto-initialize
const draggableGrid = new DraggableProfileGrid();

// Export the instance globally so other modules can access it
window.draggableGrid = draggableGrid;

// Export functions for backward compatibility
window.toggleMinimize = (section, btn) => draggableGrid.toggleMinimize(section, btn);
window.initDraggableGrid = () => draggableGrid.initialize();

console.log('🎯 Draggable Grid System loaded successfully');