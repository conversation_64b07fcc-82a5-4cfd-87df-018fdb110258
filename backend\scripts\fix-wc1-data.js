/**
 * Fix WC1 Data - Remove fake test data and restore real WC1 players
 * 
 * This script:
 * 1. Removes fake test data (players with WC1_, DOS_, Retro_ prefixes)
 * 2. Restores real WC1 players for existing users
 * 
 * Run with: node backend/scripts/fix-wc1-data.js
 */

const mongoose = require('mongoose');
const User = require('../models/User');
const Player = require('../models/Player');
const Match = require('../models/Match');

// MongoDB connection string - adjust if needed
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/newsite';

async function fixWC1Data() {
  console.log('🚀 Starting WC1 data fix...');
  
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('📦 Connected to MongoDB');

    // Step 1: Remove fake test data
    console.log('\n🧹 Removing fake test data...');
    
    // Remove players with fake prefixes
    const fakePlayerResult = await Player.deleteMany({
      name: { 
        $regex: /^(WC1_|DOS_|Retro_|WC2_|BNE_|Combat_|WC3_|Reforged_|Champion_)/ 
      }
    });
    console.log(`✅ Removed ${fakePlayerResult.deletedCount} fake players`);

    // Remove matches associated with fake players
    const fakeMatchResult = await Match.deleteMany({
      'players.name': { 
        $regex: /^(WC1_|DOS_|Retro_|WC2_|BNE_|Combat_|WC3_|Reforged_|Champion_)/ 
      }
    });
    console.log(`✅ Removed ${fakeMatchResult.deletedCount} fake matches`);

    // Step 2: Restore real WC1 players for existing users
    console.log('\n👥 Restoring real WC1 players...');
    
    // Find all users who have defined usernames
    const users = await User.find({
      isUsernameDefined: true,
      username: { $exists: true, $ne: null }
    });

    console.log(`📋 Found ${users.length} users with defined usernames`);

    let created = 0;
    let skipped = 0;
    let errors = 0;

    for (const user of users) {
      try {
        // Check if WC1 player already exists
        const existingWC1Player = await Player.findOne({
          user: user._id,
          gameType: 'warcraft1'
        });

        if (existingWC1Player) {
          console.log(`✅ ${user.username} already has WC1 player`);
          skipped++;
          continue;
        }

        // Create WC1 player
        const wc1Player = new Player({
          name: user.username,
          user: user._id,
          gameType: 'warcraft1',
          mmr: 1200, // Default MMR for WC1
          wins: 0,
          losses: 0,
          isActive: true,
          autoCreated: true,
          createdAt: new Date()
        });

        await wc1Player.save();
        console.log(`✅ Created WC1 player for ${user.username}`);
        created++;

      } catch (error) {
        console.error(`❌ Error creating WC1 player for ${user.username}:`, error.message);
        errors++;
      }
    }

    // Step 3: Show summary
    console.log('\n📊 Fix Summary:');
    console.log(`✅ Removed: ${fakePlayerResult.deletedCount} fake players`);
    console.log(`✅ Removed: ${fakeMatchResult.deletedCount} fake matches`);
    console.log(`✅ Created: ${created} real WC1 players`);
    console.log(`⏭️ Skipped: ${skipped} (already had WC1 players)`);
    console.log(`❌ Errors: ${errors}`);
    console.log(`📈 Total users processed: ${users.length}`);

    // Step 4: Show current WC1 player count
    const wc1PlayerCount = await Player.countDocuments({ gameType: 'warcraft1' });
    console.log(`\n📊 Current WC1 players in database: ${wc1PlayerCount}`);

  } catch (error) {
    console.error('💥 Fatal error during WC1 data fix:', error);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  fixWC1Data()
    .then(() => {
      console.log('🎉 WC1 data fix completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fix failed:', error);
      process.exit(1);
    });
}

module.exports = { fixWC1Data }; 