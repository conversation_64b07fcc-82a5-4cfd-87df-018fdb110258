/* ==========================================================================
   WARCRAFT ARENA - UNIFIED CSS IMPORT SYSTEM
   Complete styling system with no duplicates - Warcraft themed
   ========================================================================== */

/* =============================================================================
   IMPORT ORDER (Critical - DO NOT REORDER)
   ============================================================================= */

/* 1. DESIGN SYSTEM FOUNDATION */
@import url('./design-system.css');

/* 2. GOOGLE FONTS */
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

/* 3. PAGE-SPECIFIC MODULES (smaller, focused files) */
@import url('./components/navbar.css');
@import url('./components/notifications.css');
@import url('./components/tables.css');
@import url('./components/charts.css');
@import url('./components/animations.css');
@import url('./components/modals.css');

/* =============================================================================
   LEGACY COMPATIBILITY OVERRIDES
   Only the minimal necessary overrides for backwards compatibility
   ============================================================================= */

/* ===== LEGACY LADDER OVERRIDES ===== */
.leaderboard-table {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(20px);
}

.leaderboard-table th {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  color: var(--warcraft-gold);
  border-bottom: 2px solid var(--warcraft-gold);
}

.leaderboard-table td {
  border-bottom: 1px solid var(--glass-border);
  color: var(--neutral-100);
}

.leaderboard-table tr:hover {
  background: var(--glass-border);
}

/* ===== LEGACY PROFILE OVERRIDES ===== */
.profile-container {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
}

.profile-header {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  border-bottom: 2px solid var(--warcraft-gold);
}

/* ===== LEGACY FORM OVERRIDES ===== */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
textarea,
select {
  background: var(--bg-card);
  border: 1px solid var(--glass-border);
  color: var(--neutral-100);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  transition: all var(--transition-normal);
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--warcraft-gold);
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
}

/* ===== LEGACY BUTTON OVERRIDES ===== */
button:not(.btn),
.button:not(.btn),
input[type="submit"]:not(.btn),
input[type="button"]:not(.btn) {
  @extend .btn;
  @extend .btn-secondary;
}

.button-primary:not(.btn-primary) {
  @extend .btn;
  @extend .btn-primary;
}

.button-danger:not(.btn-danger) {
  @extend .btn;
  @extend .btn-danger;
}

/* ===== FORCE HIGH SPECIFICITY FOR CRITICAL COMPONENTS ===== */
body .modal {
  position: fixed;
  z-index: var(--z-modal);
}

body .modal.show {
  display: flex;
}

body .modal-content {
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.95), rgba(40, 40, 40, 0.95));
  border: 2px solid var(--warcraft-gold);
  border-radius: var(--radius-xl);
}

/* ===== DARK MODE / THEME OVERRIDES ===== */
@media (prefers-color-scheme: dark) {
  :root {
    /* Already optimized for dark mode */
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .modal-content {
    margin: var(--space-4);
    padding: var(--space-4);
    max-width: calc(100vw - 2rem);
  }
  
  .btn-lg {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-base);
  }
  
  .card {
    padding: var(--space-4);
    border-radius: var(--radius-lg);
  }
}

@media (max-width: 480px) {
  .modal-content {
    margin: var(--space-2);
    padding: var(--space-3);
    border-radius: var(--radius-lg);
  }
  
  .modal-title {
    font-size: var(--text-xl);
  }
  
  .btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
  }
}

/* =============================================================================
   ELECTRON APP STYLES
   Handle styling when website is embedded in Electron app
   ============================================================================= */
body.electron-app {
  /* Add padding to avoid overlap with Electron controls */
  padding-top: 50px;
}

/* Adjust specific elements that might conflict with Electron controls */
body.electron-app .navbar,
body.electron-app .header-bar,
body.electron-app .top-menu {
  /* Ensure website navigation doesn't overlap with Electron controls */
  margin-top: 0;
  position: relative;
}

/* Hide elements that might conflict with Electron interface */
body.electron-app .app-download-banner,
body.electron-app .desktop-app-prompt {
  display: none;
}

/* Adjust dropdowns and tooltips in Electron */
body.electron-app .dropdown-menu {
  z-index: 998; /* Lower than Electron controls but higher than regular content */
}

body.electron-app .tooltip {
  z-index: 997;
}

/* Responsive adjustments for Electron */
@media (max-width: 1024px) {
  body.electron-app {
    padding-top: 60px; /* More space on smaller screens */
  }
}

/* =============================================================================
   PRINT STYLES
   ============================================================================= */
@media print {
  .modal,
  .navbar,
  .notification,
  button {
    display: none;
  }
  
  body {
    background: white;
    color: black;
  }
  
  .card {
    border: 1px solid #ccc;
    box-shadow: none;
  }
}
