/**
 * Lightweight Game Detector
 * 
 * Core game detection without heavy dependencies
 * Focuses on process monitoring and basic result detection
 */
const { EventEmitter } = require('events');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
class LightweightGameDetector extends EventEmitter {
  constructor() {
    super();
    this.activeGames = new Map();
    this.gameConfigs = this.initializeGameConfigs();
    this.isMonitoring = false;
    this.processMonitorInterval = null;
  }
  /**
   * Initialize game configurations
   */
  initializeGameConfigs() {
    return {
      'warcraft1': {
        name: 'Warcraft: Orcs & Humans',
        processNames: ['Warcraft.exe', 'War1.exe', 'WARCRAFT.EXE'],
        windowTitles: ['Warcraft', 'Warcraft: Orcs & Humans'],
        logPaths: [
          path.join(process.env.USERPROFILE || '', 'Documents', 'Warcraft', 'Logs'),
          path.join(process.env.PROGRAMFILES || '', 'Warcraft', 'Logs')
        ],
        resultPatterns: {
          victory: ['Victory!', 'You have won!', 'Victor<PERSON>'],
          defeat: ['Defeat!', 'You have been defeated!', 'Defeated']
        }
      },
      'warcraft2': {
        name: 'Warcraft II: Tides of Darkness',
        processNames: ['Warcraft II BNE.exe', 'War2.exe', 'WARCRAFT2.EXE'],
        windowTitles: ['Warcraft II', 'Warcraft II BNE'],
        logPaths: [
          path.join(process.env.USERPROFILE || '', 'Documents', 'Warcraft II BNE', 'Logs'),
          path.join(process.env.PROGRAMFILES || '', 'Warcraft II BNE', 'Logs')
        ],
        resultPatterns: {
          victory: ['You have won!', 'Victory', 'Victorious'],
          defeat: ['You have been defeated!', 'Defeat', 'Defeated']
        }
      },
      'warcraft3': {
        name: 'Warcraft III',
        processNames: ['Warcraft III.exe', 'war3.exe', 'Frozen Throne.exe'],
        windowTitles: ['Warcraft III', 'Warcraft III: The Frozen Throne'],
        logPaths: [
          path.join(process.env.USERPROFILE || '', 'Documents', 'Warcraft III', 'Logs'),
          path.join(process.env.PROGRAMFILES || '', 'Warcraft III', 'Logs')
        ],
        resultPatterns: {
          victory: ['Victory!', 'has won the game!'],
          defeat: ['Defeat!', 'has left the game']
        }
      }
    };
  }
  /**
   * Start monitoring for games
   */
  async startMonitoring() {
    if (this.isMonitoring) return;
    this.isMonitoring = true;
    // Start process monitoring
    this.startProcessMonitoring();
  }
  /**
   * Stop monitoring
   */
  async stopMonitoring() {
    if (!this.isMonitoring) return;
    this.isMonitoring = false;
    if (this.processMonitorInterval) {
      clearInterval(this.processMonitorInterval);
      this.processMonitorInterval = null;
    }
    // Clean up active games
    for (const [sessionId, gameSession] of this.activeGames.entries()) {
      if (gameSession.logWatcher) {
        gameSession.logWatcher.close();
      }
      if (gameSession.logCheckInterval) {
        clearInterval(gameSession.logCheckInterval);
      }
      if (gameSession.periodicCheck) {
        clearInterval(gameSession.periodicCheck);
      }
    }
    this.activeGames.clear();
  }
  /**
   * Start process monitoring
   */
  startProcessMonitoring() {
    this.processMonitorInterval = setInterval(async () => {
      await this.scanForGameProcesses();
    }, 3000); // Check every 3 seconds
  }
  /**
   * Scan for running game processes
   */
  async scanForGameProcesses() {
    try {
      const processes = await this.getRunningProcesses();
      const currentGameProcesses = new Set();
      for (const [gameType, config] of Object.entries(this.gameConfigs)) {
        for (const processName of config.processNames) {
          const process = processes.find(p => 
            p.name.toLowerCase() === processName.toLowerCase()
          );
          if (process) {
            const processKey = `${gameType}-${process.pid}`;
            currentGameProcesses.add(processKey);
            // New game detected
            if (!this.activeGames.has(processKey)) {
              const gameSession = this.createGameSession(gameType, process, config);
              this.activeGames.set(processKey, gameSession);
              this.emit('gameStarted', gameSession);
              // Start monitoring for this specific game
              this.startGameMonitoring(gameSession);
            }
          }
        }
      }
      // Check for ended games
      for (const [processKey, gameSession] of this.activeGames.entries()) {
        if (!currentGameProcesses.has(processKey)) {
          gameSession.endTime = Date.now();
          // Clean up watchers and intervals
          if (gameSession.logWatcher) {
            gameSession.logWatcher.close();
          }
          if (gameSession.logCheckInterval) {
            clearInterval(gameSession.logCheckInterval);
          }
          this.activeGames.delete(processKey);
          this.emit('gameEnded', gameSession);
          // Perform final analysis
          await this.performFinalAnalysis(gameSession);
        }
      }
    } catch (error) {
    }
  }
  /**
   * Create game session object
   */
  createGameSession(gameType, process, config) {
    const gameSession = {
      id: `${gameType}-${Date.now()}`,
      type: gameType,
      name: config.name,
      processId: process.pid,
      processName: process.name,
      startTime: Date.now(),
      endTime: null,
      version: 'Unknown',
      detectedResults: [],
      finalResult: null,
      logWatcher: null
    };
    console.log(`🎮 New game session: ${gameSession.name} (PID: ${gameSession.processId})`);
    return gameSession;
  }
  /**
   * Start monitoring for a specific game
   */
  startGameMonitoring(gameSession) {
    const config = this.gameConfigs[gameSession.type];
    // Monitor log files for result patterns
    this.startLogFileMonitoring(gameSession, config);
    // Simulate periodic result checking (placeholder for memory reading)
    this.startPeriodicResultCheck(gameSession, config);
  }
  /**
   * Start log file monitoring
   */
  startLogFileMonitoring(gameSession, config) {
    // Check if any log directories exist
    for (const logPath of config.logPaths) {
      if (fs.existsSync(logPath)) {
        // Use basic fs.watch instead of chokidar
        try {
          gameSession.logWatcher = fs.watch(logPath, { recursive: true }, (eventType, filename) => {
            if (eventType === 'change' && filename) {
              const filePath = path.join(logPath, filename);
              this.analyzeLogFile(gameSession, filePath, config);
            }
          });
        } catch (error) {
          // Fallback to periodic checking
          this.startPeriodicLogCheck(gameSession, logPath, config);
        }
        break; // Only watch the first existing directory
      }
    }
  }
  /**
   * Fallback periodic log checking
   */
  startPeriodicLogCheck(gameSession, logPath, config) {
    gameSession.logCheckInterval = setInterval(() => {
      try {
        const files = fs.readdirSync(logPath);
        const logFiles = files.filter(f => f.endsWith('.log') || f.endsWith('.txt'));
        for (const file of logFiles) {
          const filePath = path.join(logPath, file);
          this.analyzeLogFile(gameSession, filePath, config);
        }
      } catch (error) {
        // Directory might not exist or be accessible
      }
    }, 10000); // Check every 10 seconds
  }
  /**
   * Analyze log file for result patterns
   */
  analyzeLogFile(gameSession, filePath, config) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      // Check last 20 lines for result patterns
      const recentLines = lines.slice(-20);
      for (const line of recentLines) {
        // Check victory patterns
        for (const pattern of config.resultPatterns.victory) {
          if (line.includes(pattern)) {
            this.recordResult(gameSession, 'victory', 'logFile', 0.8);
            return;
          }
        }
        // Check defeat patterns
        for (const pattern of config.resultPatterns.defeat) {
          if (line.includes(pattern)) {
            this.recordResult(gameSession, 'defeat', 'logFile', 0.8);
            return;
          }
        }
      }
    } catch (error) {
      // File reading can fail, that's normal
    }
  }
  /**
   * Start periodic result checking (simulates memory reading)
   */
  startPeriodicResultCheck(gameSession, config) {
    gameSession.periodicCheck = setInterval(() => {
      // Simulate random result detection for testing
      // In real implementation, this would read game memory
      if (Math.random() > 0.98) { // 2% chance per check
        const outcome = Math.random() > 0.5 ? 'victory' : 'defeat';
        this.recordResult(gameSession, outcome, 'memory', 0.9);
      }
    }, 5000); // Check every 5 seconds
  }
  /**
   * Record a detected result
   */
  recordResult(gameSession, outcome, method, confidence) {
    gameSession.detectedResults.push({
      outcome: outcome,
      method: method,
      confidence: confidence,
      timestamp: Date.now()
    });
    this.emit('resultDetected', gameSession, outcome, method);
  }
  /**
   * Perform final analysis when game ends
   */
  async performFinalAnalysis(gameSession) {
    // Clean up intervals
    if (gameSession.periodicCheck) {
      clearInterval(gameSession.periodicCheck);
    }
    // Determine final result
    const finalResult = this.determineFinalResult(gameSession);
    if (finalResult) {
      gameSession.finalResult = finalResult;
      console.log(`✅ Final result: ${finalResult.outcome} (confidence: ${finalResult.confidence})`);
      this.emit('gameResult', gameSession);
      // Simulate auto-reporting
      await this.simulateAutoReport(gameSession);
    } else {
    }
  }
  /**
   * Determine final result from detections
   */
  determineFinalResult(gameSession) {
    if (gameSession.detectedResults.length === 0) {
      return null;
    }
    // Use the most recent high-confidence result
    const sortedResults = gameSession.detectedResults
      .filter(r => r.confidence > 0.7)
      .sort((a, b) => b.timestamp - a.timestamp);
    if (sortedResults.length > 0) {
      const bestResult = sortedResults[0];
      return {
        outcome: bestResult.outcome,
        confidence: bestResult.confidence,
        detectionMethod: bestResult.method,
        detectionCount: sortedResults.length
      };
    }
    return null;
  }
  /**
   * Simulate auto-reporting to Arena
   */
  async simulateAutoReport(gameSession) {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      const reportData = {
        gameType: gameSession.type,
        result: gameSession.finalResult.outcome,
        confidence: gameSession.finalResult.confidence,
        duration: gameSession.endTime - gameSession.startTime,
        detectionMethod: gameSession.finalResult.detectionMethod,
        timestamp: gameSession.endTime,
        autoReported: true,
        sessionId: gameSession.id
      };
      // Simulate success/failure
      if (Math.random() > 0.1) { // 90% success rate
        this.emit('autoReported', gameSession, reportData);
      } else {
        throw new Error('Simulated network error');
      }
    } catch (error) {
      this.emit('autoReportFailed', gameSession, error);
    }
  }
  /**
   * Get running processes
   */
  async getRunningProcesses() {
    return new Promise((resolve, reject) => {
      exec('tasklist /fo csv', (error, stdout) => {
        if (error) {
          reject(error);
          return;
        }
        const lines = stdout.split('\n').slice(1);
        const processes = lines.map(line => {
          const parts = line.split('","').map(part => part.replace(/"/g, ''));
          return {
            name: parts[0],
            pid: parts[1],
            sessionName: parts[2],
            sessionNumber: parts[3],
            memUsage: parts[4]
          };
        }).filter(p => p.name && p.pid);
        resolve(processes);
      });
    });
  }
}
module.exports = LightweightGameDetector;
