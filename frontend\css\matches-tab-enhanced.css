/* ==========================================================================
   ENHANCED MATCHES TAB STYLING
   Modern, sleek design with improved UX and visual hierarchy
   ========================================================================== */

/* ===== MATCHES TAB CONTAINER ===== */
.matches-content {
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.95), 
    rgba(30, 41, 59, 0.9));
  border-radius: 16px;
  border: 1px solid rgba(212, 175, 55, 0.15);
  overflow: hidden;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
}

.matches-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(212, 175, 55, 0.6) 50%, 
    transparent 100%);
  z-index: 1;
}

/* ===== MATCHES LIST CONTAINER ===== */
.matches-list {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(212, 175, 55, 0.5) transparent;
}

.matches-list::-webkit-scrollbar {
  width: 6px;
}

.matches-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.matches-list::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.5);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.matches-list::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.7);
}

/* ===== ENHANCED MATCH ITEMS ===== */
/* Match item styling moved to player-modal-enhanced.css to avoid conflicts */

/* ===== ENHANCED MATCH HEADER ===== */
/* Match header styling moved to player-modal-enhanced.css to avoid conflicts */

/* Match expand icon styling moved to player-modal-enhanced.css to avoid conflicts */

/* ===== ENHANCED MATCH DETAILS ===== */
.match-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.match-map {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--neutral-200, #e2e8f0);
  font-weight: 500;
  font-size: 0.9rem;
  padding: 0.4rem 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.match-map i {
  color: #D4AF37;
  font-size: 0.9rem;
}

.match-players-container {
  flex: 1;
  min-width: 0;
}

/* ===== ENHANCED PLAYER DISPLAY ===== */
.match-players {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.match-players.team-match {
  flex-direction: column;
  gap: 0.5rem;
}

.team {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.team::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.05), 
    transparent);
  transition: left 0.6s ease;
}

.team:hover::before {
  left: 100%;
}

.team.winning-team {
  background: rgba(34, 197, 94, 0.08);
  border-color: rgba(34, 197, 94, 0.2);
}

.team.losing-team {
  background: rgba(239, 68, 68, 0.08);
  border-color: rgba(239, 68, 68, 0.2);
}

.team:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.team-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.team-label {
  font-size: 0.7rem;
  color: var(--neutral-400, #94a3b8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.team-result {
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
}

.team-result.winner {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.team-result.loser {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.team-players {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.team-vs-separator {
  color: var(--neutral-400, #94a3b8);
  font-weight: 700;
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.player-link,
.current-player {
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  color: var(--neutral-200, #e2e8f0);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.85rem;
  padding: 0.3rem 0.6rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.player-link::before,
.current-player::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 175, 55, 0.1), 
    transparent);
  transition: left 0.4s ease;
}

.player-link:hover::before,
.current-player:hover::before {
  left: 100%;
}

.player-link:hover,
.current-player:hover {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
  color: #D4AF37;
  transform: translateY(-1px);
}

.player-link.winner,
.current-player.winner {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.player-link.loser,
.current-player.loser {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.current-player {
  background: rgba(212, 175, 55, 0.15);
  border: 1px solid rgba(212, 175, 55, 0.3);
  color: #D4AF37;
  font-weight: 600;
}

.current-player .fa-star {
  color: #FFD700;
  font-size: 0.7rem;
  animation: starPulse 2s ease-in-out infinite;
}

@keyframes starPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

.player-separator {
  color: var(--neutral-500, #64748b);
  font-weight: 600;
  margin: 0 0.2rem;
}

.vs-separator {
  color: var(--neutral-400, #94a3b8);
  font-weight: 700;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.2rem 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.winner-icon {
  color: #22c55e;
  font-size: 0.7rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.loser-icon {
  color: #ef4444;
  font-size: 0.7rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.match-type-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.2), 
    rgba(212, 175, 55, 0.1));
  color: #D4AF37;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.6rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  z-index: 2;
}

/* ===== ENHANCED MMR CHANGE ===== */
/* MMR change styling moved to player-modal-enhanced.css to avoid conflicts */

/* ===== ENHANCED PAGINATION ===== */
.matches-pagination {
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.95), 
    rgba(30, 41, 59, 0.9));
  border-top: 1px solid rgba(212, 175, 55, 0.2);
  padding: 1.5rem;
  position: relative;
}

.matches-pagination::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(212, 175, 55, 0.4) 50%, 
    transparent 100%);
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.matches-range {
  font-weight: 600;
  color: var(--neutral-200, #e2e8f0);
  font-size: 0.9rem;
}

.page-info {
  font-weight: 700;
  color: #D4AF37;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  background: rgba(255, 255, 255, 0.03);
  padding: 0.5rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.page-numbers .btn {
  min-width: 2.2rem;
  height: 2.2rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.8rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.page-numbers .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.1), 
    transparent);
  transition: left 0.4s ease;
}

.page-numbers .btn:hover::before {
  left: 100%;
}

.page-numbers .btn-primary {
  background: linear-gradient(135deg, #D4AF37, #FFD700);
  color: #000;
  border: 1px solid #D4AF37;
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.page-numbers .btn-primary:hover {
  background: linear-gradient(135deg, #FFD700, #FFED4E);
  border-color: #FFD700;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.4);
}

.page-numbers .btn-secondary {
  background: rgba(255, 255, 255, 0.08);
  color: var(--neutral-200, #e2e8f0);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.page-numbers .btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(212, 175, 55, 0.3);
  color: #D4AF37;
  transform: translateY(-1px);
}

.page-numbers .btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.page-ellipsis {
  color: var(--neutral-400, #94a3b8);
  font-weight: 600;
  padding: 0 0.5rem;
  font-size: 0.8rem;
}

/* Previous/Next buttons */
.pagination-controls .btn {
  font-size: 0.8rem;
  padding: 0.5rem 1rem;
  min-height: auto;
  border-radius: 6px;
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
  color: #D4AF37;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.pagination-controls .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 175, 55, 0.2), 
    transparent);
  transition: left 0.4s ease;
}

.pagination-controls .btn:hover::before {
  left: 100%;
}

.pagination-controls .btn:hover:not(:disabled) {
  background: rgba(212, 175, 55, 0.2);
  border-color: rgba(212, 175, 55, 0.4);
  color: #FFD700;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
}

.pagination-controls .btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.pagination-controls .btn i {
  font-size: 0.7rem;
  margin: 0 0.2rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  /* Responsive match styling moved to player-modal-enhanced.css to avoid conflicts */
}

@media (max-width: 480px) {
  /* Responsive match styling moved to player-modal-enhanced.css to avoid conflicts */
}

/* ===== LOADING STATES ===== */
.match-item.loading {
  opacity: 0.7;
  pointer-events: none;
}

.match-item.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-top: 2px solid #D4AF37;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* ===== ACCESSIBILITY ===== */
.match-item[role="button"] {
  cursor: pointer;
}

.match-item[role="button"]:focus {
  outline: 2px solid #D4AF37;
  outline-offset: 2px;
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  .match-item,
  .match-outcome,
  .player-link,
  .current-player,
  .mmr-change,
  .page-numbers .btn,
  .pagination-controls .btn {
    transition: none;
  }
  
  .match-item::before,
  .match-outcome::before,
  .player-link::before,
  .current-player::before,
  .mmr-change::before,
  .page-numbers .btn::before,
  .pagination-controls .btn::before {
    display: none;
  }
  
  .match-item:hover,
  .player-link:hover,
  .current-player:hover,
  .mmr-change:hover,
  .page-numbers .btn:hover,
  .pagination-controls .btn:hover {
    transform: none;
  }
} 