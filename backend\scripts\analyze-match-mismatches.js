const mongoose = require('mongoose');
const Match = require('../models/Match');
const Player = require('../models/Player'); // Add Player model import

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/newsite', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function analyzeMatchMismatches() {
  console.log('🔍 Analyzing match type and player count mismatches...\n');

  try {
    // Get all matches
    const matches = await Match.find({}).populate('players.playerId', 'name');
    
    console.log(`📊 Total matches found: ${matches.length}\n`);

    const mismatches = [];
    const matchTypeStats = {};

    matches.forEach(match => {
      const matchType = match.matchType;
      const playerCount = match.players.length;
      
      // Initialize stats for this match type
      if (!matchTypeStats[matchType]) {
        matchTypeStats[matchType] = {
          total: 0,
          correctCount: 0,
          incorrectCount: 0,
          playerCounts: {}
        };
      }
      
      matchTypeStats[matchType].total++;
      
      // Track player count distribution
      if (!matchTypeStats[matchType].playerCounts[playerCount]) {
        matchTypeStats[matchType].playerCounts[playerCount] = 0;
      }
      matchTypeStats[matchType].playerCounts[playerCount]++;

      // Determine expected player count based on match type
      let expectedPlayerCount;
      let isCorrect = false;
      
      switch (matchType) {
        case '1v1':
          expectedPlayerCount = 2;
          isCorrect = playerCount === 2;
          break;
        case '2v2':
          expectedPlayerCount = 4;
          isCorrect = playerCount === 4;
          break;
        case '3v3':
          expectedPlayerCount = 6;
          isCorrect = playerCount === 6;
          break;
        case '4v4':
          expectedPlayerCount = 8;
          isCorrect = playerCount === 8;
          break;
        case 'ffa':
          expectedPlayerCount = '3+'; // FFA can have 3+ players
          isCorrect = playerCount >= 3;
          break;
        case 'vsai':
          expectedPlayerCount = '2+'; // vsai can have 2+ players (player + AI)
          isCorrect = playerCount >= 2;
          break;
        default:
          expectedPlayerCount = 'unknown';
          isCorrect = false;
      }

      if (isCorrect) {
        matchTypeStats[matchType].correctCount++;
      } else {
        matchTypeStats[matchType].incorrectCount++;
        
        // Add to mismatches list
        mismatches.push({
          matchId: match._id,
          matchType: matchType,
          expectedPlayers: expectedPlayerCount,
          actualPlayers: playerCount,
          players: match.players.map(p => ({
            name: p.playerId?.name || p.name || 'Unknown',
            team: p.team,
            isAI: p.isAI,
            placement: p.placement
          })),
          date: match.date,
          map: match.map?.name || 'Unknown',
          verificationStatus: match.verification?.status || 'unknown'
        });
      }
    });

    // Display overall statistics
    console.log('📈 MATCH TYPE STATISTICS:');
    console.log('========================');
    
    Object.entries(matchTypeStats).forEach(([matchType, stats]) => {
      const accuracy = ((stats.correctCount / stats.total) * 100).toFixed(1);
      console.log(`\n${matchType}:`);
      console.log(`  Total matches: ${stats.total}`);
      console.log(`  Correct player count: ${stats.correctCount} (${accuracy}%)`);
      console.log(`  Incorrect player count: ${stats.incorrectCount}`);
      console.log(`  Player count distribution:`, stats.playerCounts);
    });

    // Display mismatches
    if (mismatches.length > 0) {
      console.log(`\n❌ FOUND ${mismatches.length} MISMATCHES:`);
      console.log('=====================================');
      
      mismatches.forEach((mismatch, index) => {
        console.log(`\n${index + 1}. Match ID: ${mismatch.matchId}`);
        console.log(`   Type: ${mismatch.matchType}`);
        console.log(`   Expected: ${mismatch.expectedPlayers} players`);
        console.log(`   Actual: ${mismatch.actualPlayers} players`);
        console.log(`   Map: ${mismatch.map}`);
        console.log(`   Date: ${mismatch.date}`);
        console.log(`   Verification: ${mismatch.verificationStatus}`);
        console.log(`   Players:`);
        mismatch.players.forEach((player, pIndex) => {
          const aiFlag = player.isAI ? ' (AI)' : '';
          const teamInfo = player.team ? ` (Team ${player.team})` : '';
          const placementInfo = player.placement ? ` (${player.placement}${getOrdinalSuffix(player.placement)})` : '';
          console.log(`     ${pIndex + 1}. ${player.name}${aiFlag}${teamInfo}${placementInfo}`);
        });
      });

      // Group mismatches by type for easier analysis
      console.log(`\n📋 MISMATCH SUMMARY BY TYPE:`);
      console.log('============================');
      
      const mismatchByType = {};
      mismatches.forEach(mismatch => {
        if (!mismatchByType[mismatch.matchType]) {
          mismatchByType[mismatch.matchType] = [];
        }
        mismatchByType[mismatch.matchType].push(mismatch);
      });

      Object.entries(mismatchByType).forEach(([matchType, typeMismatches]) => {
        console.log(`\n${matchType}: ${typeMismatches.length} mismatches`);
        const playerCounts = {};
        typeMismatches.forEach(m => {
          if (!playerCounts[m.actualPlayers]) playerCounts[m.actualPlayers] = 0;
          playerCounts[m.actualPlayers]++;
        });
        console.log(`  Player count distribution:`, playerCounts);
      });

    } else {
      console.log('\n✅ No mismatches found! All matches have the correct number of players for their type.');
    }

    // Additional analysis: Check for potential data quality issues
    console.log('\n🔍 ADDITIONAL DATA QUALITY CHECKS:');
    console.log('==================================');
    
    const dataQualityIssues = [];
    
    matches.forEach(match => {
      // Check for matches with no players
      if (match.players.length === 0) {
        dataQualityIssues.push({
          type: 'No players',
          matchId: match._id,
          matchType: match.matchType,
          date: match.date
        });
      }
      
      // Check for matches with null/undefined player names
      const invalidPlayers = match.players.filter(p => 
        !p.playerId?.name && !p.name && !p.playerName
      );
      if (invalidPlayers.length > 0) {
        dataQualityIssues.push({
          type: 'Invalid player names',
          matchId: match._id,
          matchType: match.matchType,
          count: invalidPlayers.length,
          date: match.date
        });
      }
      
      // Check for FFA matches with placement issues
      if (match.matchType === 'ffa') {
        const playersWithPlacement = match.players.filter(p => p.placement !== undefined && p.placement !== null);
        if (playersWithPlacement.length !== match.players.length) {
          dataQualityIssues.push({
            type: 'FFA missing placements',
            matchId: match._id,
            playersWithPlacement: playersWithPlacement.length,
            totalPlayers: match.players.length,
            date: match.date
          });
        }
      }
    });

    if (dataQualityIssues.length > 0) {
      console.log(`\n⚠️  Found ${dataQualityIssues.length} data quality issues:`);
      dataQualityIssues.forEach((issue, index) => {
        console.log(`\n${index + 1}. ${issue.type}:`);
        console.log(`   Match ID: ${issue.matchId}`);
        console.log(`   Match Type: ${issue.matchType}`);
        console.log(`   Date: ${issue.date}`);
        if (issue.count) console.log(`   Count: ${issue.count}`);
        if (issue.playersWithPlacement) {
          console.log(`   Players with placement: ${issue.playersWithPlacement}/${issue.totalPlayers}`);
        }
      });
    } else {
      console.log('\n✅ No data quality issues found!');
    }

  } catch (error) {
    console.error('❌ Error analyzing matches:', error);
  } finally {
    mongoose.connection.close();
    console.log('\n🔚 Database connection closed.');
  }
}

function getOrdinalSuffix(num) {
  const j = num % 10;
  const k = num % 100;
  if (j === 1 && k !== 11) return 'st';
  if (j === 2 && k !== 12) return 'nd';
  if (j === 3 && k !== 13) return 'rd';
  return 'th';
}

// Run the analysis
analyzeMatchMismatches(); 