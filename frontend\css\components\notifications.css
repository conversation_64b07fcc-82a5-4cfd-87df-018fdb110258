/* =============================================================================
   NOTIFICATIONS COMPONENT STYLES
   ============================================================================= */

.notification-bell-container {
  position: relative;
  display: inline-flex;
}

.notification-bell,
.notification-bell-avatar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  color: var(--neutral-300);
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.notification-bell:hover,
.notification-bell-avatar:hover {
  background: var(--glass-border);
  border-color: var(--warcraft-gold);
  color: var(--warcraft-gold);
  box-shadow: var(--shadow-glow);
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--horde-red);
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  font-size: var(--text-xs);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--bg-primary);
  animation: none;
}

.notification-badge:empty {
  display: none;
}

.notifications-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.95), rgba(40, 40, 40, 0.9));
  border: 2px solid var(--warcraft-gold);
  border-radius: var(--radius-lg);
  min-width: 320px;
  max-width: 400px;
  max-height: 450px;
  overflow-y: auto;
  z-index: var(--z-dropdown);
  box-shadow: var(--glass-shadow), var(--shadow-glow);
  backdrop-filter: blur(15px);
  display: none;
  animation: slideDownFade 0.3s ease-out;
  transform-origin: top right;
}

.notifications-dropdown.show {
  display: block;
}

@keyframes slideDownFade {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.notifications-header h4 {
  color: var(--warcraft-gold);
  margin: 0;
  font-family: var(--font-display);
  font-size: var(--text-lg);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.mark-all-read {
  background: none;
  border: 1px solid rgba(212, 175, 55, 0.5);
  color: var(--warcraft-gold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: var(--text-xs);
  font-weight: 500;
  transition: all var(--transition-fast);
}

.mark-all-read:hover {
  background: rgba(212, 175, 55, 0.2);
  border-color: var(--warcraft-gold);
}

.notifications-list {
  max-height: 350px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.notification-item:hover {
  background: rgba(212, 175, 55, 0.1);
}

.notification-item.unread {
  background: rgba(37, 99, 235, 0.1);
  border-left: 3px solid var(--alliance-blue);
}

.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  flex-shrink: 0;
}

.notification-icon.success {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success);
}

.notification-icon.error {
  background: rgba(239, 68, 68, 0.2);
  color: var(--error);
}

.notification-icon.info {
  background: rgba(37, 99, 235, 0.2);
  color: var(--info);
}

.notification-icon.warning {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning);
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  color: var(--neutral-100);
  font-weight: 600;
  font-size: var(--text-sm);
  margin: 0 0 var(--space-1) 0;
  line-height: 1.4;
}

.notification-message {
  color: var(--neutral-400);
  font-size: var(--text-xs);
  margin: 0 0 var(--space-1) 0;
  line-height: 1.4;
  word-wrap: break-word;
}

.notification-time {
  color: var(--neutral-500);
  font-size: var(--text-xs);
  margin: 0;
}

.notification-actions {
  display: flex;
  gap: var(--space-2);
  margin-top: var(--space-2);
  flex-wrap: wrap;
}

.notification-action-btn {
  padding: var(--space-1) var(--space-3);
  border: none;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.notification-action-btn.primary {
  background: var(--alliance-blue);
  color: white;
}

.notification-action-btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.notification-action-btn.success {
  background: var(--success);
  color: white;
}

.notification-action-btn.success:hover {
  background: #059669;
  transform: translateY(-1px);
}

.notification-action-btn.danger {
  background: var(--error);
  color: white;
}

.notification-action-btn.danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.notification-action-btn.warning {
  background: var(--warning);
  color: white;
}

.notification-action-btn.warning:hover {
  background: #d97706;
  transform: translateY(-1px);
}

.notifications-empty {
  text-align: center;
  padding: var(--space-8) var(--space-4);
  color: var(--neutral-400);
}

.notifications-empty i {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.notifications-empty p {
  margin: 0;
  font-size: var(--text-sm);
}

/* Notification Toasts */
.notification-toast {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  max-width: 400px;
  z-index: var(--z-toast);
  box-shadow: var(--glass-shadow);
  animation: slideInRight 0.3s ease-out;
}

.notification-toast.success {
  border-color: var(--success);
  background: rgba(16, 185, 129, 0.1);
}

.notification-toast.error {
  border-color: var(--error);
  background: rgba(239, 68, 68, 0.1);
}

.notification-toast.info {
  border-color: var(--info);
  background: rgba(37, 99, 235, 0.1);
}

.notification-toast.warning {
  border-color: var(--warning);
  background: rgba(245, 158, 11, 0.1);
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.toast-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.toast-title {
  color: var(--neutral-100);
  font-weight: 600;
  font-size: var(--text-sm);
  margin: 0;
}

.toast-close {
  background: none;
  border: none;
  color: var(--neutral-400);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.toast-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--neutral-100);
}

.toast-body {
  color: var(--neutral-300);
  font-size: var(--text-sm);
  line-height: 1.4;
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .notifications-dropdown {
    right: var(--space-2);
    left: var(--space-2);
    min-width: auto;
    max-width: none;
  }
  
  .notification-toast {
    right: var(--space-2);
    left: var(--space-2);
    max-width: none;
  }
}

/* ===== NEXT-GEN FORUM NOTIFICATIONS ===== */

/* General Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 16px 20px;
  border-radius: 8px;
  color: white;
  z-index: 10000;
  min-width: 300px;
  max-width: 500px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 12px;
  animation: slideInRight 0.3s ease;
  transition: all 0.3s ease;
}

.notification:hover {
  transform: translateX(-5px);
}

.notification.success {
  background: linear-gradient(135deg, rgba(46, 160, 67, 0.9), rgba(34, 139, 34, 0.9));
  border-left: 4px solid #22dd22;
}

.notification.error {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.9), rgba(178, 34, 34, 0.9));
  border-left: 4px solid #dc3545;
}

.notification.info {
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.9), rgba(25, 135, 84, 0.9));
  border-left: 4px solid #17a2b8;
}

.notification.warning {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.9), rgba(255, 152, 0, 0.9));
  border-left: 4px solid #ffc107;
  color: #333;
}

.notification i {
  font-size: 18px;
  flex-shrink: 0;
}

.notification span {
  flex: 1;
  font-weight: 500;
}

.notification button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.notification button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ============================================
   INLINE REPLIES SYSTEM - NEW
   ============================================ */

/* View Replies Button Enhancement */
.view-replies-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.view-replies-btn.active {
  background-color: rgba(var(--accent-color), 0.1);
  color: var(--accent-color);
  transform: scale(1.05);
}

.view-replies-text {
  font-size: 12px;
  font-weight: 500;
  opacity: 0.8;
}

/* Post Replies Section */
.post-replies-section {
  margin-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
  animation: slideDown 0.3s ease;
  /* Make replies section wider for better textarea space */
  width: 110%;
  margin-left: -5%;
  box-sizing: border-box;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 1000px;
  }
}

/* Replies Container */
.replies-container {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Replies Header */
.replies-header {
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 12px;
}

.replies-header h4 {
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.replies-header i {
  color: var(--accent-color);
  font-size: 12px;
}

.replies-header-count {
  color: var(--accent-color);
  font-weight: 700;
}

/* Replies List */
.replies-list {
  margin-bottom: 16px;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.replies-list::-webkit-scrollbar {
  width: 4px;
}

.replies-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.replies-list::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 4px;
}

/* Individual Reply Item */
.reply-item {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-left: 3px solid rgba(212, 175, 55, 0.4);
  border-radius: 8px;
  padding: 12px 16px;
  margin: 8px 0 8px 20px; /* Indent replies to show hierarchy */
  transition: all 0.3s ease;
  position: relative;
}

.reply-item::before {
  content: '';
  position: absolute;
  left: -23px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, 
    rgba(212, 175, 55, 0.3), 
    rgba(212, 175, 55, 0.1));
  border-radius: 1px;
}

.reply-item:hover {
  background: rgba(255, 255, 255, 0.06);
  border-left-color: rgba(212, 175, 55, 0.6);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-item.new-reply {
  background: rgba(212, 175, 55, 0.1);
  border-left-color: #d4af37;
  animation: newReplyPulse 2s ease-out;
  box-shadow: 0 0 15px rgba(212, 175, 55, 0.3);
}

@keyframes newReplyPulse {
  0% { 
    background: rgba(212, 175, 55, 0.2);
    transform: scale(1.02);
  }
  100% { 
    background: rgba(212, 175, 55, 0.1);
    transform: scale(1);
  }
}

/* Reply Header */
.reply-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 8px;
}

.reply-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

/* Reply Actions */
.reply-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.reply-item:hover .reply-actions {
  opacity: 1;
}

.delete-reply-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  font-size: 12px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.delete-reply-btn:hover {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.delete-reply-btn:active {
  transform: scale(0.95);
}

/* Stronger rules for reply avatar sizing - prevent any overrides */
.reply-avatar,
.reply-item .reply-avatar,
img.reply-avatar,
.reply-header .reply-avatar {
  width: 28px;
  height: 28px;
  max-width: 28px;
  max-height: 28px;
  min-width: 28px;
  min-height: 28px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  object-fit: cover;
  flex-shrink: 0;
  transition: border-color 0.3s ease;
  display: block;
}

/* Hover effect for reply avatars */
.reply-item:hover .reply-avatar {
  border-color: rgba(212, 175, 55, 0.4);
}

/* Reply meta information - updated to work with new reply actions layout */

.reply-author {
  color: rgba(212, 175, 55, 0.9);
  font-weight: 600;
  font-size: 13px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.reply-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 11px;
  font-style: italic;
}

/* Reply Content */
.reply-content {
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;
  line-height: 1.6;
  margin-left: 24px; /* Reduced to match composer-body margin */
  word-wrap: break-word;
}

.reply-content p {
  margin: 0 0 8px 0;
}

.reply-content p:last-child {
  margin-bottom: 0;
}

/* YouTube Embed Styling */
.youtube-embed {
  margin: 12px 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.youtube-embed:hover {
  transform: scale(1.02);
}

.youtube-embed iframe {
  width: 100%;
  height: auto;
  min-height: 200px;
  border: none;
  display: block;
}

.youtube-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #ff0000, #cc0000);
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  margin: 8px 0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 0, 0, 0.3);
}

.youtube-link:hover {
  background: linear-gradient(135deg, #cc0000, #990000);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 0, 0, 0.4);
  text-decoration: none;
  color: white;
}

.youtube-link i {
  font-size: 14px;
}

/* Reply Content Links */
.reply-content a {
  color: rgba(212, 175, 55, 0.9);
  text-decoration: none;
  border-bottom: 1px solid rgba(212, 175, 55, 0.3);
  transition: all 0.3s ease;
}

.reply-content a:hover {
  color: #d4af37;
  border-bottom-color: #d4af37;
  text-shadow: 0 0 8px rgba(212, 175, 55, 0.5);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .reply-item {
    margin-left: 12px;
    padding: 10px 12px;
  }
  
  .reply-item::before {
    left: -15px;
  }
  
  .reply-content {
    margin-left: 0;
    font-size: 13px;
  }
  
  .reply-avatar {
    width: 24px;
    height: 24px;
  }
  
  .youtube-embed iframe {
    min-height: 180px;
  }
}

/* ============================================
   LEGACY MODAL SYSTEM CLEANUP
   ============================================ */

/* These styles are now unused but kept for backward compatibility */
.comments-modal,
.comments-loading-modal {
  display: none;
}

/* Offline Indicator */
.offline-indicator {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: linear-gradient(135deg, rgba(108, 117, 125, 0.95), rgba(73, 80, 87, 0.95));
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  z-index: 9999;
  display: none;
  align-items: center;
  gap: 10px;
  animation: slideInLeft 0.3s ease;
}

.offline-indicator.show {
  display: flex;
}

.offline-indicator i {
  color: #ffc107;
  animation: pulse 2s infinite;
}

.offline-indicator .reconnecting {
  font-size: 12px;
  opacity: 0.8;
  margin-left: 8px;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

/* Post title styling */
.post-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0.75rem 0;
  line-height: 1.4;
}

.post-card:hover .post-title {
  color: var(--accent-color);
  transition: color 0.2s ease;
}

/* Prominent post title (larger and more visible than username) */
.post-title-prominent {
  font-family: var(--font-display);
  font-weight: 700;
  font-size: 1.6rem;
  color: #d4af37;
  margin: 0 0 1rem 0;
  line-height: 1.3;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
  letter-spacing: 0.5px;
  border-bottom: 2px solid rgba(212, 175, 55, 0.3);
  padding-bottom: 0.5rem;
}

.post-card:hover .post-title-prominent {
  color: #fff;
  text-shadow: 0 0 8px rgba(212, 175, 55, 0.6);
  transition: all 0.2s ease;
}

/* Post header adjustments for better hierarchy */
.post-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.post-author-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: #b0b0b0;
}

.post-meta {
  font-size: 0.8rem;
  color: #888;
}

/* Delete button styling */
.post-actions-header {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.delete-post-btn {
  width: 32px;
  height: 32px;
  border: 1px solid rgba(239, 68, 68, 0.3);
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.delete-post-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
  color: #fff;
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
  transform: scale(1.05);
}

.delete-post-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.delete-post-btn:disabled:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
  box-shadow: none;
}

/* Reply button styling - consistent with site design */
.reply-btn {
  width: auto;
  min-width: 80px;
  height: 36px;
  padding: 0 16px;
  border: 2px solid #d4af37;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.05));
  color: #d4af37;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.reply-btn:hover {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.15));
  border-color: #f4e58c;
  color: #f4e58c;
  box-shadow: 
    0 0 12px rgba(212, 175, 55, 0.6),
    0 4px 8px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px) scale(1.02);
}

.reply-btn:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.reply-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reply-btn i {
  font-size: 0.85rem;
}

/* Comments Modal Styling */
.comments-modal .modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.comments-modal .modal-content {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.comments-modal .modal-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.05);
}

.comments-modal .modal-header h3 {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.comments-modal .modal-header h3 i {
  color: var(--accent-color);
}

.comments-modal .modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.comments-modal .modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.comments-modal .comments-container {
  padding: 1.5rem 2rem;
  overflow-y: auto;
  flex: 1;
}

/* Original Post Styling */
.comments-modal .original-post {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.comments-modal .original-post .post-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.comments-modal .original-post .post-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid var(--accent-color);
  object-fit: cover;
}

.comments-modal .original-post .post-meta {
  display: flex;
  flex-direction: column;
}

.comments-modal .original-post .post-author {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.95rem;
}

.comments-modal .original-post .post-time {
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.comments-modal .original-post .post-content {
  color: var(--text-primary);
  line-height: 1.6;
  font-size: 1rem;
}

/* Comments Section */
.comments-modal .comments-section h4 {
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.comments-modal .comments-section h4 i {
  color: var(--accent-color);
}

.comments-modal .comments-list {
  margin-bottom: 2rem;
}

/* Individual Comment Styling */
.comments-modal .comment-item {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
}

.comments-modal .comment-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.comments-modal .comment-item.new-comment {
  border-color: var(--accent-color);
  background: rgba(212, 175, 55, 0.1);
}

.comments-modal .comment-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.comments-modal .comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  object-fit: cover;
}

.comments-modal .comment-meta {
  display: flex;
  flex-direction: column;
}

.comments-modal .comment-author {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.9rem;
}

.comments-modal .comment-time {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.comments-modal .comment-content {
  color: var(--text-primary);
  line-height: 1.5;
  font-size: 0.95rem;
}

/* No Comments State */
.comments-modal .no-comments {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
}

.comments-modal .no-comments i {
  font-size: 2rem;
  color: var(--accent-color);
  margin-bottom: 0.5rem;
  opacity: 0.7;
}

.comments-modal .no-comments p {
  margin: 0;
  font-size: 1rem;
}

/* Comment Composer */
.comments-modal .comment-composer {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-top: 1rem;
}

.comments-modal .composer-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.comments-modal .composer-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 2px solid var(--accent-color);
  object-fit: cover;
}

.comments-modal .composer-header h5 {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.comments-modal .comment-input {
  width: 100%;
  min-height: 100px;
  background: var(--bg-card);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: 1rem;
  color: var(--text-primary);
  font-family: inherit;
  font-size: 0.95rem;
  line-height: 1.5;
  resize: vertical;
  transition: all 0.2s ease;
}

.comments-modal .comment-input:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
  outline: none;
}

.comments-modal .comment-input::placeholder {
  color: var(--text-secondary);
}

.comments-modal .composer-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.comments-modal .submit-comment {
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
  color: var(--neutral-900);
  border: none;
  border-radius: var(--radius-lg);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.comments-modal .submit-comment:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.comments-modal .submit-comment:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Auth Prompt */
.comments-modal .auth-prompt {
  text-align: center;
  padding: 2rem;
  margin-top: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
}

.comments-modal .auth-prompt p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 1rem;
}

.comments-modal .auth-prompt a {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
}

.comments-modal .auth-prompt a:hover {
  color: var(--primary-gold-light);
  text-decoration: underline;
}

/* Loading Modal */
.comments-loading-modal .modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comments-loading-modal .loading-content {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: 2rem;
  text-align: center;
  color: var(--text-primary);
}

.comments-loading-modal .loading-spinner {
  font-size: 2rem;
  color: var(--accent-color);
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .comments-modal .modal-overlay {
    padding: 1rem;
  }
  
  .comments-modal .modal-content {
    max-height: 95vh;
  }
  
  .comments-modal .modal-header {
    padding: 1rem 1.5rem;
  }
  
  .comments-modal .comments-container {
    padding: 1rem 1.5rem;
  }
  
  .comments-modal .modal-header h3 {
    font-size: 1.1rem;
  }
  
  .comments-modal .original-post {
    padding: 1rem;
  }
  
  .comments-modal .comment-composer {
    padding: 1rem;
  }
}

/* Inline Reply Composer - Optimized for maximum textarea width */
.inline-reply-composer {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px; /* Reduced padding to save more space for textarea */
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%; /* Take full width */
  max-width: none; /* Remove width restrictions */
  min-width: 100%; /* Force minimum full width */
  box-sizing: border-box; /* Include padding in width calculation */
  transition: all 0.3s ease;
  margin: 0; /* Remove any margins that might constrain width */
}

.inline-reply-composer:focus-within {
  border-color: var(--accent-color);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 2px rgba(var(--accent-color), 0.2);
}

.inline-reply-composer .composer-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
}

.inline-reply-composer .composer-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  object-fit: cover;
}

.composer-label {
  color: #fff;
  font-weight: 500;
  font-size: 13px;
}

.composer-body {
  margin-left: 24px; /* Further reduced to save more width for textarea */
  width: calc(100% - 24px); /* Take full width minus the smaller avatar space */
  max-width: none; /* Remove any width restrictions */
  flex: 1; /* Allow it to expand fully */
}

/* Strong, specific selector to ensure reply textarea is properly sized AND WIDER */
.inline-reply-composer .reply-input,
.post-replies-section .reply-input,
textarea.reply-input {
  width: 100%;
  min-width: 100%; /* Take the full container width */
  max-width: 100%; /* Ensure it doesn't exceed container */
  min-height: 250px; /* Significantly increased for much better visibility */
  height: 250px; /* Force proper height - equivalent to about 12-15 rows */
  max-height: 500px;
  background: rgba(0, 0, 0, 0.4);
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-radius: 12px;
  padding: 12px; /* Reduced from 16px to 12px to save 8px of usable width */
  color: #fff;
  font-size: 1rem;
  line-height: 1.5; /* Standard line height */
  resize: vertical;
  transition: all 0.3s ease;
  font-family: inherit;
  box-sizing: border-box;
  vertical-align: top;
  overflow-y: auto;
  display: block; /* Ensure proper display */
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2);
}

.reply-input:focus {
  outline: none;
  border-color: #d4af37;
  background: rgba(0, 0, 0, 0.5);
  box-shadow: 
    0 0 0 3px rgba(212, 175, 55, 0.3),
    inset 0 2px 4px rgba(0, 0, 0, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.3);
}

.reply-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.composer-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  justify-content: flex-end;
  align-items: center;
}

.composer-actions .btn {
  padding: 12px 24px;
  font-size: 0.95rem;
  border-radius: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  box-shadow: 
    0 4px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.composer-actions .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.composer-actions .btn:hover::before {
  left: 100%;
}

.composer-actions .btn.btn-primary {
  background: linear-gradient(135deg, #d4af37, #b8941f);
  border-color: #d4af37;
  color: #000;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.composer-actions .btn.btn-primary:hover {
  background: linear-gradient(135deg, #f4e58c, #d4af37);
  border-color: #f4e58c;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 
    0 8px 16px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(212, 175, 55, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.composer-actions .btn.btn-primary:active {
  transform: translateY(-1px) scale(1.01);
  box-shadow: 
    0 4px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.composer-actions .btn.btn-secondary {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  border-color: rgba(255, 255, 255, 0.3);
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.composer-actions .btn.btn-secondary:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 
    0 8px 16px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.composer-actions .btn.btn-secondary:active {
  transform: translateY(-1px) scale(1.01);
  box-shadow: 
    0 4px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.composer-actions .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.composer-actions .btn:disabled::before {
  display: none;
}

.composer-actions .btn i {
  margin-right: 8px;
  font-size: 0.9rem;
}

/* Loading States */
.loading-replies {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.loading-replies i {
  color: var(--accent-color);
  font-size: 16px;
}

/* No Replies State */
.no-replies {
  text-align: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.6);
}

.no-replies i {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.4);
  margin-bottom: 8px;
  display: block;
}

.no-replies p {
  margin: 0;
  font-size: 14px;
  font-style: italic;
}

/* Error Loading State */
.error-loading-replies {
  text-align: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(220, 53, 69, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.error-loading-replies i {
  font-size: 20px;
  color: #dc3545;
  margin-bottom: 8px;
  display: block;
}

.error-loading-replies button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-left: 8px;
  transition: all 0.3s ease;
}

.error-loading-replies button:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* Auth Prompt Inline */
.auth-prompt-inline {
  text-align: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px dashed rgba(255, 255, 255, 0.2);
}

.auth-prompt-inline p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.auth-prompt-inline a {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.auth-prompt-inline a:hover {
  color: #fff;
  text-decoration: underline;
}

/* Additional Mobile Responsiveness */
@media (max-width: 768px) {
  .inline-reply-composer .composer-body {
    margin-left: 0;
    margin-top: 8px;
    width: 100%; /* Full width on mobile */
  }
  
  .inline-reply-composer .composer-header {
    margin-bottom: 8px;
  }
  
  .inline-reply-composer .composer-actions {
    flex-direction: column;
  }
  
  .inline-reply-composer .composer-actions .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Composer buttons container */
.composer-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Post composer hidden state */
.post-composer.hidden {
  display: none;
}

.post-composer.show {
  display: block;
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 
