Write-Host "========================================" -ForegroundColor Cyan
Write-Host "WC Arena Core - Complete Startup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "[1/4] Killing existing processes on ports 3000 and 3001..." -ForegroundColor Yellow
# Kill processes on port 3000
$port3000 = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess
if ($port3000) {
    Stop-Process -Id $port3000 -Force -ErrorAction SilentlyContinue
}

# Kill processes on port 3001
$port3001 = Get-NetTCPConnection -LocalPort 3001 -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess
if ($port3001) {
    Stop-Process -Id $port3001 -Force -ErrorAction SilentlyContinue
}
Write-Host "✅ Ports cleared" -ForegroundColor Green

Write-Host ""
Write-Host "[2/4] Starting backend server on port 3000..." -ForegroundColor Yellow
Set-Location backend
Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm start" -WindowStyle Normal
Set-Location ..

Write-Host ""
Write-Host "[3/4] Starting proxy server on port 3001..." -ForegroundColor Yellow
Set-Location electron-app
Start-Process powershell -ArgumentList "-NoExit", "-Command", "node electron-proxy.js" -WindowStyle Normal
Set-Location ..

Write-Host ""
Write-Host "[4/4] Waiting 5 seconds for servers to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "[5/5] Starting Electron app..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm start" -WindowStyle Normal

Write-Host ""
Write-Host "✅ All services started!" -ForegroundColor Green
Write-Host "📡 Backend Server: http://127.0.0.1:3000" -ForegroundColor Cyan
Write-Host "🔄 Proxy Server: http://127.0.0.1:3001" -ForegroundColor Cyan
Write-Host "🖥️  Electron App: WC Arena Core" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to stop all services..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Write-Host ""
Write-Host "🛑 Stopping all services..." -ForegroundColor Red
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "electron" -ErrorAction SilentlyContinue | Stop-Process -Force
Write-Host "✅ All services stopped." -ForegroundColor Green 