/* Enhanced Taskbar Styles for WC Arena Core */

/* Taskbar Icon Animations */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(46, 204, 113, 0.5); }
  50% { box-shadow: 0 0 15px rgba(46, 204, 113, 0.8); }
  100% { box-shadow: 0 0 5px rgba(46, 204, 113, 0.5); }
}

@keyframes notification-blink {
  0% { background-color: #F39C12; }
  50% { background-color: #E67E22; }
  100% { background-color: #F39C12; }
}

/* Status Indicators */
.status-indicator {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid #fff;
  bottom: 0;
  right: 0;
}

.status-online {
  background-color: #2ECC71;
  animation: glow 2s infinite;
}

.status-gaming {
  background-color: #FF6B6B;
  animation: pulse 1s infinite;
}

.status-offline {
  background-color: #95A5A6;
}

.status-notification {
  background-color: #F39C12;
  animation: notification-blink 1s infinite;
}

/* Enhanced Menu Styling */
.enhanced-menu {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border: 1px solid #34495e;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.menu-header {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
  font-weight: bold;
  text-align: center;
  border-bottom: 1px solid #2980b9;
}

.menu-item {
  padding: 8px 16px;
  color: #ecf0f1;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.menu-item:hover {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  transform: translateX(4px);
}

.menu-item.disabled {
  color: #7f8c8d;
  cursor: not-allowed;
}

.menu-separator {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #34495e 50%, transparent 100%);
  margin: 4px 0;
}

/* Garrison Integration Styles */
.garrison-section {
  background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
  border-radius: 4px;
  margin: 4px 8px;
  padding: 8px;
}

.garrison-item {
  color: #ecf0f1;
  padding: 6px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.garrison-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.02);
}

.garrison-badge {
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 10px;
  margin-left: 8px;
  animation: pulse 2s infinite;
}

/* Game Section Styles */
.games-section {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  border-radius: 4px;
  margin: 4px 8px;
  padding: 8px;
}

.game-item {
  color: #ecf0f1;
  padding: 6px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.game-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.02);
}

/* Quick Actions */
.quick-actions {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  border-radius: 4px;
  margin: 4px 8px;
  padding: 8px;
}

.quick-action-item {
  color: #ecf0f1;
  padding: 6px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.quick-action-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.02);
}

/* Status Display */
.status-display {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  color: #ecf0f1;
  padding: 8px 12px;
  border-radius: 4px;
  margin: 4px 8px;
  text-align: center;
  font-size: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-icon {
  margin-right: 8px;
  font-size: 14px;
}

/* Notification Styles */
.notification-item {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  margin: 4px 8px;
  animation: pulse 1s infinite;
}

.notification-count {
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 10px;
  margin-left: 8px;
  animation: notification-blink 1s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-menu {
    min-width: 280px;
  }
  
  .menu-item {
    padding: 12px 16px;
    font-size: 14px;
  }
  
  .status-display {
    font-size: 14px;
  }
}

/* Dark Theme Enhancements */
.dark-theme .enhanced-menu {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-color: #404040;
}

.dark-theme .menu-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border-bottom-color: #34495e;
}

.dark-theme .menu-item {
  color: #bdc3c7;
}

.dark-theme .menu-item:hover {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
}

/* Accessibility */
.menu-item:focus {
  outline: 2px solid #3498db;
  outline-offset: -2px;
}

.menu-item:focus-visible {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .status-indicator {
    border-width: 2px;
  }
  
  .menu-item {
    border-bottom-width: 2px;
  }
  
  .enhanced-menu {
    border-width: 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .status-online,
  .status-gaming,
  .status-notification,
  .garrison-badge,
  .notification-count {
    animation: none;
  }
  
  .menu-item:hover,
  .garrison-item:hover,
  .game-item:hover,
  .quick-action-item:hover {
    transform: none;
  }
} 