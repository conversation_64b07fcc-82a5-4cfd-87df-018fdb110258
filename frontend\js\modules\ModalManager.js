/**
 * ModalManager.js - Universal Modal Management System
 * 
 * Provides a streamlined way to create, show, hide, and manage modals
 * across the entire Warcraft Arena project.
 */

class ModalManager {
  constructor() {
    this.activeModals = new Map();
    this.modalStack = [];
    this.escapeKeyHandler = this.handleEscapeKey.bind(this);
    this.clickOutsideHandler = this.handleClickOutside.bind(this);
    
    // Initialize the modal system
    this.init();
    
    console.log('🎭 ModalManager initialized');
  }

  /**
   * Initialize the modal system
   */
  init() {
    // Bind global escape key handler
    document.addEventListener('keydown', this.escapeKeyHandler);
    
    // Set up existing modals in the DOM
    this.setupExistingModals();
    
    // Add global styles if not already present
    this.injectGlobalStyles();
  }

  /**
   * Setup existing modals in the DOM
   */
  setupExistingModals() {
    const existingModals = document.querySelectorAll('.modal');
    existingModals.forEach(modal => {
      this.setupModalEvents(modal);
    });
  }

  /**
   * Create a new modal
   */
  createModal(options = {}) {
    const {
      id = `modal-${Date.now()}`,
      title = 'Modal',
      content = '',
      size = 'md', // sm, md, lg, xl, fullscreen
      showCloseButton = true,
      backdrop = true,
      keyboard = true,
      footer = null
    } = options;

    // Remove existing modal with same ID
    const existingModal = document.getElementById(id);
    if (existingModal) {
      existingModal.remove();
    }

    // Create modal structure
    const modal = document.createElement('div');
    modal.id = id;
    modal.className = `modal modal-${size}`;
    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-modal', 'true');
    modal.setAttribute('aria-labelledby', `${id}-title`);
    modal.style.display = 'none';

    // Modal content
    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';

    // Modal header
    if (title || showCloseButton) {
      const header = document.createElement('div');
      header.className = 'modal-header';

      if (title) {
        const titleElement = document.createElement('h2');
        titleElement.id = `${id}-title`;
        titleElement.className = 'modal-title';
        titleElement.textContent = title;
        header.appendChild(titleElement);
      }

      if (showCloseButton) {
        const closeButton = document.createElement('button');
        closeButton.className = 'close-modal';
        closeButton.innerHTML = '&times;';
        closeButton.setAttribute('aria-label', 'Close modal');
        header.appendChild(closeButton);
      }

      modalContent.appendChild(header);
    }

    // Modal body
    const body = document.createElement('div');
    body.className = 'modal-body';
    
    if (typeof content === 'string') {
      body.innerHTML = content;
    } else if (content instanceof HTMLElement) {
      body.appendChild(content);
    }
    
    modalContent.appendChild(body);

    // Modal footer
    if (footer) {
      const footerElement = document.createElement('div');
      footerElement.className = 'modal-footer';
      
      if (typeof footer === 'string') {
        footerElement.innerHTML = footer;
      } else if (footer instanceof HTMLElement) {
        footerElement.appendChild(footer);
      }
      
      modalContent.appendChild(footerElement);
    }

    modal.appendChild(modalContent);
    document.body.appendChild(modal);

    // Setup modal events
    this.setupModalEvents(modal, { backdrop, keyboard });

    // Store modal options
    this.activeModals.set(id, {
      element: modal,
      options: { backdrop, keyboard }
    });

    console.log(`🎭 Modal created: ${id}`);
    return modal;
  }

  /**
   * Show a modal
   */
  show(modalId, options = {}) {
    const modal = this.getModal(modalId);
    if (!modal) {
      console.error(`❌ Modal not found: ${modalId}`);
      return false;
    }

    // Hide any currently active modals if not stacking
    if (!options.stack && this.modalStack.length > 0) {
      this.hideAll();
    }

    // Add loading state if specified
    if (options.loading) {
      modal.classList.add('modal-loading');
    }

    // Show modal
    modal.style.display = 'flex';
    modal.classList.add('show');
    modal.setAttribute('data-visible', 'true');
    
    // Add to modal stack
    this.modalStack.push(modalId);
    
    // Prevent body scroll
    document.body.classList.add('modal-open');

    // Focus management
    setTimeout(() => {
      const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
      if (firstFocusable) {
        firstFocusable.focus();
      }
    }, 100);

    console.log(`👁️ Modal shown: ${modalId}`);
    
    // Dispatch custom event
    modal.dispatchEvent(new CustomEvent('modal:show', { 
      detail: { modalId, options } 
    }));

    return true;
  }

  /**
   * Hide a modal
   */
  hide(modalId) {
    const modal = this.getModal(modalId);
    if (!modal) {
      console.error(`❌ Modal not found: ${modalId}`);
      return false;
    }

    // Remove from modal stack
    const stackIndex = this.modalStack.indexOf(modalId);
    if (stackIndex > -1) {
      this.modalStack.splice(stackIndex, 1);
    }

    // Hide modal
    modal.classList.remove('show');
    modal.setAttribute('data-visible', 'false');
    modal.style.display = 'none';
    modal.classList.remove('modal-loading');

    // Remove body scroll lock if no more modals
    if (this.modalStack.length === 0) {
      document.body.classList.remove('modal-open');
    }

    console.log(`🙈 Modal hidden: ${modalId}`);
    
    // Dispatch custom event
    modal.dispatchEvent(new CustomEvent('modal:hide', { 
      detail: { modalId } 
    }));

    return true;
  }

  /**
   * Toggle a modal
   */
  toggle(modalId) {
    const modal = this.getModal(modalId);
    if (!modal) return false;

    if (this.isVisible(modalId)) {
      return this.hide(modalId);
    } else {
      return this.show(modalId);
    }
  }

  /**
   * Hide all modals
   */
  hideAll() {
    [...this.modalStack].forEach(modalId => {
      this.hide(modalId);
    });
  }

  /**
   * Check if a modal is visible
   */
  isVisible(modalId) {
    const modal = this.getModal(modalId);
    return modal && modal.classList.contains('show');
  }

  /**
   * Update modal content
   */
  updateContent(modalId, content) {
    const modal = this.getModal(modalId);
    if (!modal) return false;

    const body = modal.querySelector('.modal-body');
    if (body) {
      if (typeof content === 'string') {
        body.innerHTML = content;
      } else if (content instanceof HTMLElement) {
        body.innerHTML = '';
        body.appendChild(content);
      }
    }

    return true;
  }

  /**
   * Update modal title
   */
  updateTitle(modalId, title) {
    const modal = this.getModal(modalId);
    if (!modal) return false;

    const titleElement = modal.querySelector('.modal-title');
    if (titleElement) {
      titleElement.textContent = title;
    }

    return true;
  }

  /**
   * Set modal loading state
   */
  setLoading(modalId, loading = true) {
    const modal = this.getModal(modalId);
    if (!modal) return false;

    if (loading) {
      modal.classList.add('modal-loading');
    } else {
      modal.classList.remove('modal-loading');
    }

    return true;
  }

  /**
   * Get modal element
   */
  getModal(modalId) {
    const storedModal = this.activeModals.get(modalId);
    if (storedModal) return storedModal.element;
    
    // Fallback to DOM query
    return document.getElementById(modalId);
  }

  /**
   * Setup modal event handlers
   */
  setupModalEvents(modal, options = {}) {
    const { backdrop = true, keyboard = true } = options;

    // Close button handler
    const closeButton = modal.querySelector('.close-modal, .modal-close, .close');
    if (closeButton) {
      closeButton.addEventListener('click', (e) => {
        e.preventDefault();
        this.hide(modal.id);
      });
    }

    // Backdrop click handler
    if (backdrop) {
      modal.addEventListener('click', this.clickOutsideHandler);
    }

    // Prevent modal content clicks from closing modal
    const modalContent = modal.querySelector('.modal-content');
    if (modalContent) {
      modalContent.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }
  }

  /**
   * Handle escape key press
   */
  handleEscapeKey(e) {
    if (e.key === 'Escape' && this.modalStack.length > 0) {
      const topModalId = this.modalStack[this.modalStack.length - 1];
      const modalData = this.activeModals.get(topModalId);
      
      if (modalData && modalData.options.keyboard !== false) {
        this.hide(topModalId);
      }
    }
  }

  /**
   * Handle click outside modal
   */
  handleClickOutside(e) {
    if (e.target.classList.contains('modal')) {
      const modalId = e.target.id;
      this.hide(modalId);
    }
  }

  /**
   * Remove a modal from the DOM
   */
  destroy(modalId) {
    const modal = this.getModal(modalId);
    if (!modal) return false;

    // Hide first if visible
    if (this.isVisible(modalId)) {
      this.hide(modalId);
    }

    // Remove from tracking
    this.activeModals.delete(modalId);

    // Remove from DOM
    modal.remove();

    console.log(`🗑️ Modal destroyed: ${modalId}`);
    return true;
  }

  /**
   * Quick modal creation methods
   */
  alert(message, title = 'Alert') {
    const modalId = `alert-${Date.now()}`;
    
    const modal = this.createModal({
      id: modalId,
      title,
      content: `<p>${message}</p>`,
      size: 'sm',
      footer: '<button class="btn btn-primary" onclick="window.modalManager.hide(\'' + modalId + '\')">OK</button>'
    });

    this.show(modalId);
    return modalId;
  }

  confirm(message, title = 'Confirm') {
    return new Promise((resolve) => {
      const modalId = `confirm-${Date.now()}`;
      
      const footer = `
        <button class="btn btn-secondary" onclick="window.modalManager.hide('${modalId}'); window.modalManager._confirmResolve(false)">Cancel</button>
        <button class="btn btn-primary" onclick="window.modalManager.hide('${modalId}'); window.modalManager._confirmResolve(true)">Confirm</button>
      `;

      this._confirmResolve = resolve;

      const modal = this.createModal({
        id: modalId,
        title,
        content: `<p>${message}</p>`,
        size: 'sm',
        footer,
        backdrop: false,
        keyboard: false
      });

      this.show(modalId);
    });
  }

  /**
   * Inject global modal styles if not present
   */
  injectGlobalStyles() {
    if (document.getElementById('modal-manager-styles')) return;

    const link = document.createElement('link');
    link.id = 'modal-manager-styles';
    link.rel = 'stylesheet';
    link.href = '/css/components/modals.css';
    document.head.appendChild(link);
  }

  /**
   * Cleanup - remove all event listeners
   */
  destroy() {
    document.removeEventListener('keydown', this.escapeKeyHandler);
    this.hideAll();
    this.activeModals.clear();
    this.modalStack = [];
    console.log('🧹 ModalManager destroyed');
  }
}

// Create global instance
window.modalManager = new ModalManager();

// Global instance is available as window.modalManager
// No exports needed for regular script loading 