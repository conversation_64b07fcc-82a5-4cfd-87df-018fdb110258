/* ==========================================
   POLL CREATION MODAL STYLES
   ========================================== */

/* Modal Overlay */
.poll-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  display: none;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.poll-modal.show {
  opacity: 1;
}

.poll-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
}

/* Modal Content */
.poll-modal-content {
  position: relative;
  background: linear-gradient(135deg, 
    rgba(45, 45, 45, 0.98) 0%, 
    rgba(25, 25, 25, 0.99) 100%);
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-radius: 20px;
  padding: 0;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.8),
    0 0 30px rgba(212, 175, 55, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: scale(0.9);
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.poll-modal.show .poll-modal-content {
  transform: scale(1);
}

/* Modal Header */
.poll-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-bottom: 1px solid rgba(212, 175, 55, 0.2);
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.1) 0%, 
    rgba(212, 175, 55, 0.05) 100%);
}

.poll-modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #d4af37;
  display: flex;
  align-items: center;
  gap: 12px;
}

.poll-modal-header h3 i {
  font-size: 1.3rem;
  opacity: 0.9;
}

.poll-modal-close {
  background: none;
  border: none;
  color: #888;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.poll-modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  transform: scale(1.1);
}

/* Modal Body */
.poll-modal-body {
  padding: 32px;
  max-height: 60vh;
  overflow-y: auto;
}

.poll-form-group {
  margin-bottom: 32px;
}

.poll-form-group:last-child {
  margin-bottom: 0;
}

.poll-form-group label {
  display: block;
  font-size: 1rem;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 12px;
}

/* Input Styles */
.poll-form-group input,
.poll-form-group select {
  width: 100%;
  padding: 16px;
  background: rgba(0, 0, 0, 0.4);
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-radius: 12px;
  color: #fff;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.poll-form-group input:focus,
.poll-form-group select:focus {
  outline: none;
  border-color: #d4af37;
  background: rgba(0, 0, 0, 0.5);
  box-shadow: 
    0 0 0 3px rgba(212, 175, 55, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.3);
}

.poll-form-group input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Character Count */
.poll-char-count {
  text-align: right;
  font-size: 0.875rem;
  color: #888;
  margin-top: 8px;
}

/* Poll Options */
.poll-option-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.poll-option-input {
  flex: 1;
  margin-bottom: 0;
}

.poll-option-remove {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  border: none;
  color: white;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  height: 52px;
}

.poll-option-remove:hover:not(:disabled) {
  background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
  transform: scale(1.05);
}

.poll-option-remove:disabled {
  background: #555;
  cursor: not-allowed;
  opacity: 0.5;
}

.poll-option-remove i {
  font-size: 0.9rem;
}

/* Add Option Button */
.poll-add-option {
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.2) 0%, 
    rgba(212, 175, 55, 0.1) 100%);
  border: 2px dashed rgba(212, 175, 55, 0.5);
  color: #d4af37;
  padding: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 600;
  width: 100%;
  margin-bottom: 16px;
}

.poll-add-option:hover {
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.3) 0%, 
    rgba(212, 175, 55, 0.2) 100%);
  border-color: #d4af37;
  transform: translateY(-2px);
}

.poll-add-option i {
  font-size: 0.9rem;
}

/* Poll Options Info */
.poll-options-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #888;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 4px solid rgba(212, 175, 55, 0.5);
}

.poll-options-info i {
  font-size: 0.8rem;
  opacity: 0.7;
}

/* Modal Footer */
.poll-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 24px 32px;
  border-top: 1px solid rgba(212, 175, 55, 0.2);
  background: linear-gradient(135deg, 
    rgba(0, 0, 0, 0.3) 0%, 
    rgba(0, 0, 0, 0.1) 100%);
}

/* Button Styles */
.poll-modal .btn-primary,
.poll-modal .btn-secondary {
  padding: 14px 28px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  min-width: 120px;
  justify-content: center;
}

.poll-modal .btn-primary {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  box-shadow: 
    0 4px 12px rgba(40, 167, 69, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.poll-modal .btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #218838 0%, #1dd1a1 100%);
  transform: translateY(-2px);
  box-shadow: 
    0 6px 16px rgba(40, 167, 69, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.poll-modal .btn-primary:disabled {
  background: #555;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.poll-modal .btn-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  box-shadow: 
    0 4px 12px rgba(108, 117, 125, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.poll-modal .btn-secondary:hover {
  background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
  transform: translateY(-2px);
  box-shadow: 
    0 6px 16px rgba(108, 117, 125, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
  .poll-modal-content {
    width: 95%;
    max-height: 95vh;
  }
  
  .poll-modal-header,
  .poll-modal-footer {
    padding: 20px 24px;
  }
  
  .poll-modal-body {
    padding: 24px;
  }
  
  .poll-modal-header h3 {
    font-size: 1.3rem;
  }
  
  .poll-option-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .poll-option-remove {
    align-self: flex-end;
    min-width: 40px;
    height: 40px;
    padding: 8px;
  }
  
  .poll-modal-footer {
    flex-direction: column;
  }
  
  .poll-modal .btn-primary,
  .poll-modal .btn-secondary {
    width: 100%;
  }
}

/* Scrollbar Styling for Modal Body */
.poll-modal-body::-webkit-scrollbar {
  width: 8px;
}

.poll-modal-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.poll-modal-body::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.5);
  border-radius: 4px;
}

.poll-modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.7);
}

/* Animation for options */
@keyframes slideInOption {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.poll-option-item {
  animation: slideInOption 0.3s ease-out;
}

/* ==========================================
   POLL VOTE BUTTON STYLES
   ========================================== */

.poll-vote-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.poll-vote-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #0056b3 0%, #003d82 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.4);
}

.poll-vote-btn:disabled {
  background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.poll-vote-btn i {
  font-size: 0.8rem;
}

.poll-vote-btn span {
  font-size: 0.85rem;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* ==========================================
   ENHANCED POLL OPTION STYLES
   ========================================== */

.poll-option {
  background: rgba(45, 45, 45, 0.8);
  border: 2px solid rgba(212, 175, 55, 0.2);
  border-radius: 12px;
  margin-bottom: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.poll-option.clickable {
  cursor: pointer;
}

.poll-option.clickable:hover {
  background: rgba(45, 45, 45, 0.9);
  border-color: rgba(212, 175, 55, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.poll-option.clickable:hover .vote-status.votable {
  background: linear-gradient(135deg, #0056b3 0%, #003d82 100%);
  transform: scale(1.05);
}

.poll-option.clickable:hover .vote-status.voted {
  background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
}

.poll-option.user-vote {
  background: rgba(0, 123, 255, 0.15);
  border-color: rgba(0, 123, 255, 0.5);
  box-shadow: 0 0 20px rgba(0, 123, 255, 0.2);
}

.poll-option.user-vote:hover {
  background: rgba(0, 123, 255, 0.2);
  border-color: rgba(0, 123, 255, 0.6);
}

.poll-option.expired {
  opacity: 0.7;
  cursor: not-allowed;
}

.poll-option-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.poll-option-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #fff;
  flex: 1;
  margin-right: 16px;
}

.poll-option-status {
  display: flex;
  align-items: center;
}

.vote-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.vote-status.votable {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.vote-status.voted {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.poll-option-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.poll-option-votes {
  font-size: 0.9rem;
  color: #ccc;
  font-weight: 500;
}

.poll-option-bar {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  height: 6px;
  overflow: hidden;
  position: relative;
}

.poll-option-fill {
  background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
  height: 100%;
  border-radius: 4px;
  transition: width 0.6s ease;
  position: relative;
}

.poll-option.user-vote .poll-option-fill {
  background: linear-gradient(90deg, #28a745 0%, #1e7e34 100%);
  box-shadow: 0 0 8px rgba(40, 167, 69, 0.4);
}

/* Animation for vote status changes */
@keyframes voteSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.poll-option.user-vote {
  animation: voteSuccess 0.4s ease;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .poll-option-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .poll-option-text {
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .vote-status {
    font-size: 0.8rem;
    padding: 4px 8px;
  }
} 
