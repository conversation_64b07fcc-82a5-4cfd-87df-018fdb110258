@echo off
echo  WC Arena Core - Build Installer
echo ============================================

:: Check if we're in the right directory
if not exist "package.json" (
    echo Error: package.json not found!
    echo Please run this script from the electron-app directory.
    pause
    exit /b 1
)

echo Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Checking npm installation...
npm --version >nul 2>&1
if errorlevel 1 (
    echo Error: npm is not installed or not in PATH
    pause
    exit /b 1
)

:: Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    call npm install
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo Dependencies already installed.
)

echo.
echo Building Windows installer...
echo This may take a few minutes...
echo.

:: Build the installer
call npm run dist

if errorlevel 1 (
    echo.
    echo Error: Failed to build installer
    echo Check the console output above for details
    pause
    exit /b 1
) else (
    echo.
    echo ============================================
    echo  Build completed successfully!
    echo ============================================
    echo.
    echo Installer created in: dist\
    
    :: List the created files
    if exist "dist\*.exe" (
        echo.
        echo Created installers:
        dir /b "dist\*.exe"
        echo.
        
        :: Copy to frontend directory for download
        echo Copying installer to frontend directory for web download...
        copy "dist\WC Arena Core Setup *.exe" "..\frontend\wc-arena-core-setup.exe" >nul 2>&1
        if errorlevel 1 (
            echo Warning: Could not copy installer to frontend directory
        ) else (
            echo Installer copied to frontend directory successfully!
        )
    )
    
    echo.
    echo You can now distribute the installer to users!
    pause
) 