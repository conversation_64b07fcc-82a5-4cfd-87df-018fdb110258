/**
 * Modern API Client
 * Handles all API communications with consistent error handling
 */

class ApiClient {
  constructor() {
    this.baseUrl = window.location.hostname === 'localhost' ? 'http://localhost:3000' : 'http://127.0.0.1:3000';
    this.authToken = null;
    this.authRequested = false; // Flag to prevent multiple auth requests
    this.initializeAuth();
  }

  initializeAuth() {
    // Check URL parameters first
    const urlParams = new URLSearchParams(window.location.search);
    const urlToken = urlParams.get('authToken');
    
    if (urlToken) {
      this.authToken = urlToken;
      localStorage.setItem('authToken', urlToken);
      console.log('🔐 ApiClient: JWT token found in URL parameters');
      return;
    }

    // Check localStorage
    const storedToken = localStorage.getItem('authToken');
    if (storedToken) {
      this.authToken = storedToken;
      console.log('🔐 ApiClient: JWT token found in localStorage');
    }

    // Check for Electron-specific token
    if (window.electron?.getAuthToken) {
      const electronToken = window.electron.getAuthToken();
      if (electronToken) {
        this.authToken = electronToken;
        localStorage.setItem('authToken', electronToken);
        console.log('🔐 ApiClient: JWT token found via electron API');
      }
    }
    
    // Set up message listener for Electron authentication
    this.setupElectronAuthListener();
  }
  
  setupElectronAuthListener() {
    // Listen for authentication messages from Electron parent window
    window.addEventListener('message', (event) => {
      // Only accept messages from our parent window (Electron)
      if (event.source !== window.parent) {
        return;
      }
      
      if (event.data.type === 'ELECTRON_AUTH' && event.data.token) {
        console.log('🔐 ApiClient: Received JWT token from Electron parent');
        this.setAuthToken(event.data.token);
        
        // Trigger a custom event to notify other components
        const authEvent = new CustomEvent('electronAuthReceived', { 
          detail: { token: event.data.token, user: event.data.user } 
        });
        window.dispatchEvent(authEvent);
      } else if (event.data.type === 'AUTH_TOKEN_RESPONSE' && event.data.token) {
        console.log('🔐 ApiClient: Received JWT token response from Electron parent');
        this.setAuthToken(event.data.token);
        
        // Trigger a custom event to notify other components
        const authEvent = new CustomEvent('electronAuthReceived', { 
          detail: { token: event.data.token, user: event.data.user } 
        });
        window.dispatchEvent(authEvent);
      }
    });
    
    // Request authentication token from parent if we're in an iframe (only once)
    if (window !== window.top && !this.authRequested) {
      console.log('🔐 ApiClient: Requesting authentication token from Electron parent');
      this.authRequested = true; // Mark as requested to prevent multiple requests
      window.parent.postMessage({
        type: 'REQUEST_AUTH_TOKEN'
      }, '*');
    }
  }

  setAuthToken(token) {
    this.authToken = token;
    if (token) {
      localStorage.setItem('authToken', token);
    } else {
      localStorage.removeItem('authToken');
    }
  }

  getAuthToken() {
    if (!this.authToken) {
      this.authToken = localStorage.getItem('authToken');
    }
    return this.authToken;
  }

  clearAuthToken() {
    this.authToken = null;
    localStorage.removeItem('authToken');
  }
  
  refreshAuthToken() {
    // Don't re-initialize auth if we already have a token
    if (this.authToken) {
      console.log('🔐 ApiClient: Already have auth token, skipping refresh');
      return;
    }
    
    // Reset the request flag and re-initialize auth
    this.authRequested = false;
    this.initializeAuth();
    console.log('🔐 ApiClient: Refreshed authentication token');
  }

  getAuthHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };
    
    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }
    
    return headers;
  }

  async request(endpoint, options = {}) {
    const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;
    
    // Use session authentication (cookies) instead of JWT headers for consistency
    options.credentials = 'include';
    
    // Add auth headers if we have a JWT token
    options.headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };
    
    if (this.authToken) {
      options.headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    try {
      const response = await fetch(url, options);
      
      // Handle 401 by clearing auth and redirecting to login
      if (response.status === 401) {
        this.setAuthToken(null);
        // Only redirect to login if we're not already there and not in Electron
        if (window.location.pathname !== '/views/login.html' && !window.isElectronMode) {
          window.location.href = '/views/login.html';
        }
        return null;
      }

      return response;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  /**
   * GET request
   */
  async get(endpoint, params = {}) {
    const url = new URL(`${this.baseUrl}${endpoint}`, window.location.origin);
    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
    
    return this.request(url.pathname + url.search);
  }

  /**
   * POST request
   */
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  /**
   * PUT request
   */
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  /**
   * DELETE request
   */
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE'
    });
  }

  /**
   * Upload file with FormData
   */
  async uploadFile(endpoint, formData) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const response = await fetch(url, {
        method: 'POST',
        credentials: 'include',
        headers: window.getAuthHeaders ? window.getAuthHeaders() : {},
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Upload failed' }));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const jsonResponse = await response.json();
      
      // If the response already has a success field, return it directly
      if (typeof jsonResponse === 'object' && 'success' in jsonResponse) {
        return jsonResponse;
      }
      
      // Otherwise wrap it for consistency
      return {
        success: true,
        data: jsonResponse
      };
    } catch (error) {
      console.error('File upload failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // === USER ENDPOINTS ===

  /**
   * Get current user data
   */
  async getCurrentUser() {
    try {
      const headers = {
        'Content-Type': 'application/json'
      };
      
      // Add JWT token if available
      if (this.authToken) {
        headers['Authorization'] = `Bearer ${this.authToken}`;
        console.log('🔐 ApiClient: Using JWT token for /api/me request');
      }
      
      const response = await fetch('/api/me', {
        credentials: 'include',
        headers: headers
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const userData = await response.json();
      console.log('ApiClient.getCurrentUser() response:', userData);
      return userData; // Return user data directly, not wrapped
    } catch (error) {
      console.error('getCurrentUser failed:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData) {
    return this.put('/me', profileData);
  }

  /**
   * Update username
   */
  async updateUsername(newUsername) {
    return this.patch('/me/username', { username: newUsername });
  }

  /**
   * Update social links
   */
  async updateSocialLinks(socialLinks) {
    return this.patch('/me/social-links', { socialLinks });
  }

  /**
   * Update game preference
   */
  async updateGamePreference(platform, gameType, isEnabled) {
    return this.patch('/me/content-creator', {
      platform,
      gameType,
      isEnabled
    });
  }

  // === PLAYER ENDPOINTS ===

  /**
   * Get user's players
   */
  async getUserPlayers() {
    return this.get('/api/ladder/my-players');
  }

  /**
   * Add new player
   */
  async addPlayer(playerData) {
    return this.post('/api/ladder/players', playerData);
  }

  /**
   * Update player
   */
  async updatePlayer(playerId, playerData) {
    return this.put(`/players/${playerId}`, playerData);
  }

  /**
   * Remove player
   */
  async removePlayer(playerId) {
    return this.delete(`/players/${playerId}`);
  }

  /**
   * Get player stats
   */
  async getPlayerStats(playerId) {
    return this.get(`/players/${playerId}/stats`);
  }

  // === TOURNAMENT ENDPOINTS ===

  /**
   * Get user's tournaments
   */
  async getUserTournaments() {
    return this.get('/tournaments/user');
  }

  /**
   * Get tournament details
   */
  async getTournament(tournamentId) {
    return this.get(`/tournaments/${tournamentId}`);
  }

  /**
   * Create tournament
   */
  async createTournament(tournamentData) {
    return this.post('/tournaments', tournamentData);
  }

  /**
   * Update tournament
   */
  async updateTournament(tournamentId, tournamentData) {
    return this.put(`/tournaments/${tournamentId}`, tournamentData);
  }

  /**
   * Join tournament
   */
  async joinTournament(tournamentId, playerData) {
    return this.post(`/tournaments/${tournamentId}/register`, playerData);
  }

  /**
   * Leave tournament
   */
  async leaveTournament(tournamentId) {
    return this.post(`/tournaments/${tournamentId}/leave`);
  }

  // === LADDER ENDPOINTS ===

  /**
   * Get ladder rankings
   */
  async getLadderRankings(params = {}) {
    return this.get('/ladder/rankings', params);
  }

  /**
   * Get ladder ranks
   */
  async getLadderRanks() {
    return this.get('/ladder/ranks');
  }

  /**
   * Submit match report
   */
  async submitMatchReport(matchData) {
    return this.post('/ladder/report', matchData);
  }

  /**
   * Get recent matches
   */
  async getRecentMatches(params = {}) {
    return this.get('/ladder/matches', params);
  }

  /**
   * Dispute match
   */
  async disputeMatch(matchId, disputeData) {
    return this.post('/ladder/dispute-match', { matchId, ...disputeData });
  }

  /**
   * Get global stats
   */
  async getGlobalStats() {
    return this.get('/ladder/global-stats');
  }

  // === MAP ENDPOINTS ===

  /**
   * Get maps
   */
  async getMaps(params = {}) {
    return this.get('/maps', params);
  }

  /**
   * Search maps
   */
  async searchMaps(query) {
    return this.get('/maps/search', { q: query });
  }

  /**
   * Get user's maps
   */
  async getUserMaps() {
    return this.get('/maps/user');
  }

  /**
   * Upload map
   */
  async uploadMap(mapData) {
    return this.uploadFile('/api/war2maps/upload', mapData);
  }

  // === ACTIVITY ENDPOINTS ===

  /**
   * Get user activity
   */
  async getUserActivity() {
    return this.get('/activity/user');
  }

  /**
   * Get user reports
   */
  async getUserReports() {
    return this.get('/reports/user');
  }

  // === NOTIFICATION ENDPOINTS ===

  /**
   * Get notifications
   */
  async getNotifications() {
    return this.get('/notifications');
  }

  /**
   * Mark notification as read
   */
  async markNotificationRead(notificationId) {
    return this.patch(`/notifications/${notificationId}/read`);
  }

  /**
   * Mark all notifications as read
   */
  async markAllNotificationsRead() {
    return this.patch('/notifications/read-all');
  }

  // === CHAT ENDPOINTS ===

  /**
   * Get chat messages
   */
  async getChatMessages(params = {}) {
    return this.get('/chat/messages', params);
  }

  /**
   * Send chat message
   */
  async sendChatMessage(messageData) {
    return this.post('/chat/messages', messageData);
  }

  // === ADMIN ENDPOINTS ===

  /**
   * Get admin stats
   */
  async getAdminStats() {
    return this.get('/admin/stats');
  }

  /**
   * Get admin users
   */
  async getAdminUsers(params = {}) {
    return this.get('/admin/users', params);
  }

  /**
   * Update user admin
   */
  async updateUserAdmin(userId, userData) {
    return this.put(`/admin/users/${userId}`, userData);
  }

  // Ladder specific methods
  async getMyPlayers() {
    return this.get('/api/ladder/my-players');
  }

  async selectGameType(gameType) {
    return this.post('/api/user/game-type', { gameType });
  }

  async createPlayer(playerData) {
    return this.post('/api/ladder/players', playerData);
  }

  async getMapsByType(gameType) {
    return this.get('/api/war2maps', { gameType });
  }

  async reportMatch(matchData) {
    return this.post('/api/ladder/matches', matchData);
  }

  async getPlayerDetails(playerName) {
    return this.get(`/ladder/player/${encodeURIComponent(playerName)}`);
  }

  /**
   * Get the user's active clan (returns the first clan if multiple, or null if none)
   */
  async getUserClan() {
    try {
      const response = await this.get('/api/clans/user');
      if (response && Array.isArray(response) && response.length > 0) {
        return response[0];
      }
      return null;
    } catch (error) {
      console.error('getUserClan failed:', error);
      return null;
    }
  }
}

// Create singleton instance
const apiClient = new ApiClient();

// Export both named and default
export { ApiClient, apiClient };
export default apiClient; 