<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download WC Arena</title>
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .download-container {
            max-width: 800px;
            margin: 100px auto;
            padding: 40px;
            text-align: center;
        }

        .download-header {
            margin-bottom: 40px;
        }

        .download-header h1 {
            font-size: 2.5rem;
            color: #c9aa71;
            margin-bottom: 10px;
        }

        .download-header p {
            font-size: 1.2rem;
            color: #bbb;
            margin-bottom: 30px;
        }

        .app-preview {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 40px;
            backdrop-filter: blur(10px);
        }

        .app-preview img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .download-buttons {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 40px;
        }

        .primary-download {
            background: linear-gradient(135deg, #c9aa71, #8b7355);
            color: white;
            padding: 20px 40px;
            border: none;
            border-radius: 12px;
            font-size: 1.3rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .primary-download:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(201, 170, 113, 0.3);
        }

        .platform-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .platform-download {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(201, 170, 113, 0.3);
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
        }

        .platform-download:hover {
            border-color: #c9aa71;
            background: rgba(201, 170, 113, 0.1);
            transform: translateY(-2px);
        }

        .platform-download .icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .platform-download .name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .platform-download .size {
            font-size: 0.9rem;
            color: #bbb;
        }

        .features {
            text-align: left;
            margin-top: 40px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 30px;
        }

        .features h3 {
            color: #c9aa71;
            margin-bottom: 20px;
            text-align: center;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .features li:last-child {
            border-bottom: none;
        }

        .features li::before {
            content: "⚔️";
            font-size: 1.2rem;
        }

        .unavailable {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .unavailable:hover {
            transform: none;
            border-color: rgba(201, 170, 113, 0.3);
            background: rgba(255, 255, 255, 0.1);
        }

        .coming-soon {
            color: #ff6b6b;
            font-size: 0.8rem;
            margin-top: 5px;
        }
    </style>
  <!-- Universal Navbar Consistency - MUST be loaded last for proper override -->`r`n  <link rel=stylesheet href=/css/navbar-universal.css />`r`n</head>
<body>
    <!-- Navigation will be loaded here -->
    <div id="navbar-container"></div>

    <div class="download-container">
        <div class="download-header">
                    <h1>Download WC Arena</h1>
        <p>Experience the ultimate WC2 gaming platform on your desktop</p>
        </div>

        <div class="app-preview">
            <p style="color: #bbb; font-style: italic;">Desktop app preview coming soon</p>
        </div>

        <div class="download-buttons">
            <a href="#" id="primaryDownload" class="primary-download">
                <span class="icon">💻</span>
                <div>
                    <div>Download for <span id="detectedOS">Your Platform</span></div>
                    <div style="font-size: 0.9rem; opacity: 0.8;">Version 1.0.0</div>
                </div>
            </a>

            <div class="platform-options">
                <a href="/downloads/WC-Arena-Setup-1.0.0.exe" class="platform-download" download>
                    <div class="icon">🪟</div>
                    <div class="name">Windows</div>
                    <div class="size">~147 MB</div>
                </a>

                <div class="platform-download unavailable">
                    <div class="icon">🍎</div>
                    <div class="name">macOS</div>
                    <div class="size">Coming Soon</div>
                    <div class="coming-soon">Building for macOS requires a Mac</div>
                </div>

                <div class="platform-download unavailable">
                    <div class="icon">🐧</div>
                    <div class="name">Linux</div>
                    <div class="size">Coming Soon</div>
                    <div class="coming-soon">AppImage format</div>
                </div>
            </div>
        </div>

        <div class="features">
            <h3>Desktop App Features</h3>
            <ul>
                <li>Native desktop experience with better performance</li>
                <li>Seamless OAuth authentication (Google, Discord, Twitch)</li>
                <li>Automatic updates when new versions are available</li>
                                    <li>Full access to all WC Arena features</li>
                <li>Offline capability for certain features</li>
                <li>Native notifications for tournaments and matches</li>
                <li>Secure local data storage</li>
                <li>Cross-platform compatibility</li>
            </ul>
        </div>
    </div>

    <!-- Footer will be loaded here -->
    <div id="footer-container"></div>

    <script src="/js/utils.js"></script>
    <script src="/js/main.js"></script>
    <script>
        // Platform detection
        function detectPlatform() {
            const userAgent = navigator.userAgent.toLowerCase();
            const platform = navigator.platform.toLowerCase();
            
            if (platform.includes('win') || userAgent.includes('windows')) {
                return 'Windows';
            } else if (platform.includes('mac') || userAgent.includes('mac')) {
                return 'macOS';
            } else if (platform.includes('linux') || userAgent.includes('linux')) {
                return 'Linux';
            }
            return 'Your Platform';
        }

        // Set up primary download button
        function setupDownloads() {
            const detectedOS = detectPlatform();
            const primaryDownload = document.getElementById('primaryDownload');
            const detectedOSSpan = document.getElementById('detectedOS');
            
            console.log('🔧 Setting up downloads for detected OS:', detectedOS);
            
            detectedOSSpan.textContent = detectedOS;
            
            // Set up primary download link based on platform
            switch(detectedOS) {
                case 'Windows':
                    primaryDownload.href = '/downloads/WC-Arena-Setup-1.0.0.exe';
                    primaryDownload.setAttribute('download', '');
                    console.log('✅ Windows download link set:', primaryDownload.href);
                    break;
                case 'macOS':
                    primaryDownload.style.opacity = '0.5';
                    primaryDownload.style.cursor = 'not-allowed';
                    primaryDownload.addEventListener('click', (e) => {
                        e.preventDefault();
                        alert('macOS version coming soon! Building for macOS requires a Mac development environment.');
                    });
                    console.log('⚠️ macOS download disabled');
                    break;
                case 'Linux':
                    primaryDownload.style.opacity = '0.5';
                    primaryDownload.style.cursor = 'not-allowed';
                    primaryDownload.addEventListener('click', (e) => {
                        e.preventDefault();
                        alert('Linux version coming soon! Will be available as an AppImage.');
                    });
                    console.log('⚠️ Linux download disabled');
                    break;
                default:
                    primaryDownload.href = '/downloads/WC-Arena-Setup-1.0.0.exe';
                    primaryDownload.setAttribute('download', '');
                    console.log('✅ Default (Windows) download link set:', primaryDownload.href);
            }
        }

        // Track downloads
        function trackDownload(platform) {
            // Send analytics event if you have analytics set up
            console.log(`Desktop app download started: ${platform}`);
            
            // You could send this to your backend for tracking
            fetch('/api/downloads/track', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    platform: platform,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent
                })
            }).then(response => {
                if (response.ok) {
                    console.log(`Download tracking successful for ${platform}`);
                } else {
                    console.warn(`Download tracking failed for ${platform}:`, response.status);
                }
            }).catch(err => {
                // Don't let tracking errors interfere with download
                console.log('Download tracking failed:', err);
            });
        }

        // Function to start download
        function startDownload(url, platform) {
            console.log(`Starting download from: ${url} for platform: ${platform}`);
            
            // Track the download
            trackDownload(platform);
            
            // Create a temporary link and click it to start download
            const link = document.createElement('a');
            link.href = url;
            link.download = ''; // This will use the filename from the URL
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            console.log(`Download initiated for ${platform}`);
        }

        // Add tracking to download links
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Download page DOM loaded, setting up downloads...');
            setupDownloads();
            
            // Track primary download
            const primaryBtn = document.getElementById('primaryDownload');
            console.log('📎 Primary download button:', primaryBtn);
            
            primaryBtn.addEventListener('click', (e) => {
                console.log('🖱️ Primary download button clicked');
                const primaryBtn = document.getElementById('primaryDownload');
                console.log('🔗 Button href:', primaryBtn.href);
                console.log('🎯 Button cursor style:', primaryBtn.style.cursor);
                
                if (!primaryBtn.style.cursor.includes('not-allowed')) {
                    e.preventDefault(); // Prevent default link behavior
                    const platform = detectPlatform();
                    const url = primaryBtn.href;
                    console.log('✅ Starting download:', { platform, url });
                    startDownload(url, platform);
                } else {
                    console.log('❌ Download blocked - platform not supported');
                }
            });
            
            // Track platform-specific downloads
            const platformButtons = document.querySelectorAll('.platform-download[href]');
            console.log('📎 Platform download buttons found:', platformButtons.length);
            
            platformButtons.forEach(link => {
                link.addEventListener('click', (e) => {
                    console.log('🖱️ Platform download button clicked:', link);
                    e.preventDefault(); // Prevent default link behavior
                    const platform = link.querySelector('.name').textContent;
                    const url = link.href;
                    console.log('✅ Starting platform download:', { platform, url });
                    startDownload(url, platform);
                });
            });
            
            console.log('✅ All download event handlers set up');
        });

        // Initialize unified navigation system
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🔄 Initializing navigation on download page...');

            // Load unified navigation
            if (typeof window.loadNavigation === 'function') {
                await window.loadNavigation();
            } else if (typeof window.loadNavbar === 'function') {
                await window.loadNavbar();
            }

            // Update navbar profile
            setTimeout(async () => {
                if (window.updateNavbarProfileUnified) {
                    console.log('🔄 Updating navbar profile (unified) on download page');
                    await window.updateNavbarProfileUnified();
                } else if (window.updateNavbarProfile) {
                    console.log('🔄 Updating navbar profile (legacy) on download page');
                    await window.updateNavbarProfile();
                }
            }, 500);
        });
    </script>
</body>
</html>
