:root {
  /* Colors */
  --primary-gold: #D4AF37;
  --gold-bg: rgba(212, 175, 55, 0.1);
  --gold-text: #D4AF37;
  --text-color: #ffffff;
  --text-muted: rgba(255, 255, 255, 0.7);
  --border-color: rgba(255, 255, 255, 0.1);
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --accent-bg: rgba(255, 255, 255, 0.1);
  --error-color: #ff6b6b;
  --neutral-300: rgba(255, 255, 255, 0.7);
  --neutral-900: #000000;
  --primary-gold-light: #E5C158;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  
  /* Border radius */
  --border-radius: 8px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  
  /* Fonts */
  --font-display: 'Cin<PERSON>', serif;
  --text-sm: 0.875rem;
  --text-xs: 0.75rem;
  --text-2xl: 1.5rem;
  
  /* Transitions */
  --transition-normal: 0.3s ease;
  
  /* Shadows */
  --shadow-glow: 0 0 20px rgba(212, 175, 55, 0.3);
}

/* Wizard's Tower Styles */

/* Game Selector */
.game-selector-container {
  margin: var(--space-6) 0;
  display: flex;
  justify-content: center;
}

.game-tabs {
  display: flex;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-2);
  backdrop-filter: blur(20px);
  gap: var(--space-2);
}

.game-tab {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border: none;
  border-radius: var(--radius-lg);
  background: transparent;
  color: var(--neutral-300);
  font-family: var(--font-display);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.game-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
}

.game-tab:hover::before {
  opacity: 0.1;
}

.game-tab:hover {
  color: var(--primary-gold);
  transform: translateY(-2px);
}

.game-tab.active {
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
  color: var(--neutral-900);
  box-shadow: var(--shadow-glow);
}

.game-tab.active:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

.game-tab i {
  font-size: 1.2em;
  z-index: 2;
  position: relative;
}

.game-tab span {
  z-index: 2;
  position: relative;
}

/* Wiki Container */
.wiki-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6);
}

/* Sub-tabs Container */
.sub-tabs-container {
  margin: var(--space-4) 0;
  display: flex;
  justify-content: center;
}

.sub-tabs {
  display: flex;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-1);
  backdrop-filter: blur(20px);
  gap: var(--space-1);
}

.sub-tab {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--neutral-300);
  font-family: var(--font-display);
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.sub-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
}

.sub-tab:hover::before {
  opacity: 0.1;
}

.sub-tab:hover {
  color: var(--primary-gold);
  transform: translateY(-1px);
}

.sub-tab.active {
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
  color: var(--neutral-900);
  box-shadow: var(--shadow-glow);
}

.sub-tab.active:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.4);
}

.sub-tab i {
  font-size: 1em;
  z-index: 2;
  position: relative;
}

.sub-tab span {
  z-index: 2;
  position: relative;
}

/* Sub-content Container */
.sub-content-container {
  margin-top: var(--space-4);
}

/* Game content sections */
.game-content {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
  opacity: 0;
  transition: all 0.3s ease-in-out;
  /* Ensure proper dimensions when visible */
  min-height: 400px;
  width: 100%;
}

.game-content.active {
  display: block;
  opacity: 1;
}

/* Sub-content sections */
.sub-content {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
  opacity: 0;
  transition: all 0.3s ease-in-out;
  /* Ensure proper dimensions when visible */
  min-height: 300px;
  width: 100%;
}

.sub-content.active {
  display: block !important;
  opacity: 1 !important;
}

/* Sub-tabs sections */
.sub-tabs {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}

.sub-tabs.active {
  display: flex !important;
  opacity: 1 !important;
}

.wiki-content {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}

.wiki-content.active {
  display: block;
  opacity: 1;
}

/* Ensure proper visibility states */
.sub-content[style*="display: block"] {
  opacity: 1;
}

.wiki-sections {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
  /* Ensure proper dimensions */
  min-height: 200px;
  width: 100%;
}

/* Wiki Sections */
.wiki-section {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  backdrop-filter: blur(20px);
  transition: all var(--transition-normal);
}

.wiki-section:hover {
  border-color: var(--primary-gold);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-6);
  font-family: var(--font-display);
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--primary-gold);
  border-bottom: 2px solid var(--glass-border);
  padding-bottom: var(--space-3);
}

.section-title i {
  font-size: 1.5em;
  color: var(--primary-gold);
}

/* Wiki Grid */
.wiki-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-4);
  min-height: 200px;
  width: 100%;
  /* Force container to take up space */
  position: relative;
  overflow: visible;
}

/* Loading and Error States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  min-height: 200px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-data, .error {
  text-align: center;
  padding: 40px;
  color: var(--text-muted);
  font-style: italic;
}

.error {
  color: #ff6b6b;
}

/* Unit and Building Cards Enhanced */
.wiki-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-height: 150px;
  width: 100%;
}

.wiki-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.wiki-card h4 {
  color: var(--accent-color);
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
}

.wiki-card p {
  margin: 0 0 16px 0;
  line-height: 1.5;
  color: var(--text-color);
}

.unit-stats, .building-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.unit-stats span, .building-stats span {
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-color);
}

.unit-costs, .building-costs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.unit-costs span, .building-costs span {
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

/* Wiki Content Text */
.wiki-content-text {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
  line-height: 1.6;
}

.wiki-content-text h4 {
  color: var(--accent-color);
  margin: 24px 0 12px 0;
  font-size: 18px;
  font-weight: 600;
}

.wiki-content-text h4:first-child {
  margin-top: 0;
}

.wiki-content-text p {
  margin: 0 0 16px 0;
  color: var(--text-color);
}

.wiki-content-text ul {
  margin: 0 0 16px 0;
  padding-left: 20px;
}

.wiki-content-text li {
  margin-bottom: 8px;
  color: var(--text-color);
}

.wiki-content-text strong {
  color: var(--accent-color);
  font-weight: 600;
}

/* Page Title Animation for Wizard's Tower */
.page-title[data-theme="wizards-tower"] {
  background: linear-gradient(135deg, #8B5CF6, #A855F7, #C084FC);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: wizardsTowerPulse 3s ease-in-out infinite;
}

@keyframes wizardsTowerPulse {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-tabs {
    flex-direction: column;
    gap: var(--space-1);
  }
  
  .game-tab {
    justify-content: center;
  }

  .sub-tabs {
    flex-wrap: wrap;
    gap: var(--space-1);
  }

  .sub-tab {
    padding: var(--space-1) var(--space-2);
    font-size: var(--text-xs);
  }

  .sub-tab span {
    display: none;
  }

  .sub-tab i {
    font-size: 1.2em;
  }
  
  .wiki-grid {
    grid-template-columns: 1fr;
  }
  
  .wiki-container {
    padding: var(--space-4);
  }
  
  .wiki-section {
    padding: var(--space-4);
  }
  
  .section-title {
    font-size: var(--text-xl);
  }
}

@media (max-width: 480px) {
  .game-tab {
    padding: var(--space-2) var(--space-3);
  }
  
  .game-tab span {
    font-size: var(--text-sm);
  }
  
  .wiki-card {
    padding: var(--space-3);
  }
  
  .unit-stats,
  .building-stats {
    flex-direction: column;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading State */
.wiki-content.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.wiki-content.loading::after {
  content: 'Loading...';
  color: var(--primary-gold);
  font-family: var(--font-display);
  font-size: var(--text-lg);
}

/* Empty State */
.wiki-content.empty {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  text-align: center;
}

.wiki-content.empty::after {
  content: 'No content available for this game yet.';
  color: var(--neutral-400);
  font-family: var(--font-display);
  font-size: var(--text-lg);
} 

/* Unit upgrade sections */
.unit-upgrades {
  margin-top: var(--space-3);
  padding: var(--space-3);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--accent-color);
}

.unit-upgrades h5 {
  color: var(--accent-color);
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 var(--space-2) 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.unit-upgrades ul {
  margin: 0;
  padding-left: var(--space-3);
  list-style: none;
}

.unit-upgrades li {
  color: var(--text-secondary);
  font-size: 0.85rem;
  margin-bottom: var(--space-1);
  line-height: 1.4;
}

.unit-upgrades li strong {
  color: var(--text-primary);
  font-weight: 600;
}

.unit-upgrades li:last-child {
  margin-bottom: 0;
}

/* Spell sections within upgrades */
.unit-upgrades h5:last-of-type {
  margin-top: var(--space-3);
  color: var(--accent-color-secondary);
}

/* Responsive adjustments for upgrades */
@media (max-width: 768px) {
  .unit-upgrades {
    padding: var(--space-2);
  }
  
  .unit-upgrades h5 {
    font-size: 0.8rem;
  }
  
  .unit-upgrades li {
    font-size: 0.8rem;
  }
} 

/* Unit and Building Upgrades */
.unit-upgrades,
.building-upgrades {
  margin-top: 15px;
  padding: 12px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border-left: 3px solid var(--accent-color);
}

.unit-upgrades h5,
.building-upgrades h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--accent-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.unit-upgrades ul,
.building-upgrades ul {
  margin: 0;
  padding-left: 16px;
  list-style: none;
}

.unit-upgrades li,
.building-upgrades li {
  margin-bottom: 6px;
  font-size: 13px;
  line-height: 1.4;
  color: var(--text-color);
}

.unit-upgrades strong,
.building-upgrades strong {
  color: var(--accent-color);
  font-weight: 600;
} 

/* Unit Abilities */
.unit-abilities {
  margin-top: 15px;
  padding: 12px;
  background: rgba(138, 43, 226, 0.1);
  border-radius: 8px;
  border-left: 3px solid #8a2be2;
}

.unit-abilities h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #8a2be2;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.unit-abilities ul {
  margin: 0;
  padding-left: 16px;
  list-style: none;
}

.unit-abilities li {
  margin-bottom: 6px;
  font-size: 13px;
  line-height: 1.4;
  color: var(--text-color);
}

.unit-abilities strong {
  color: #8a2be2;
  font-weight: 600;
}

/* Item Styles */
.item-card {
  position: relative;
  border-left: 4px solid var(--neutral-400);
}

.item-card.common {
  border-left-color: var(--neutral-400);
}

.item-card.uncommon {
  border-left-color: #4ade80;
}

.item-card.rare {
  border-left-color: #3b82f6;
}

.item-card.epic {
  border-left-color: #a855f7;
}

.item-card.legendary {
  border-left-color: #f59e0b;
}

.item-stats {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin: var(--space-3) 0;
}

.item-stats span {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-sm);
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--neutral-300);
}

.item-costs {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin: var(--space-3) 0;
}

.item-costs span {
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
  color: var(--neutral-900);
  border-radius: var(--radius-sm);
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  font-weight: 700;
}

.item-abilities {
  margin-top: var(--space-4);
  padding-top: var(--space-3);
  border-top: 1px solid var(--glass-border);
}

.item-abilities h5 {
  color: var(--primary-gold);
  font-family: var(--font-display);
  font-size: var(--text-lg);
  font-weight: 700;
  margin-bottom: var(--space-2);
}

.item-abilities ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.item-abilities li {
  padding: var(--space-2);
  margin-bottom: var(--space-1);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  line-height: 1.5;
}

.item-abilities strong {
  color: var(--primary-gold);
  font-weight: 700;
}

.item-rarity {
  position: absolute;
  top: var(--space-3);
  right: var(--space-3);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.item-rarity.common {
  background: var(--neutral-400);
  color: var(--neutral-900);
}

.item-rarity.uncommon {
  background: #4ade80;
  color: var(--neutral-900);
}

.item-rarity.rare {
  background: #3b82f6;
  color: white;
}

.item-rarity.epic {
  background: #a855f7;
  color: white;
}

.item-rarity.legendary {
  background: #f59e0b;
  color: var(--neutral-900);
} 

/* Unit and Building Cards */
.unit-card,
.building-card {
  background: var(--glass-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--space-4);
  margin-bottom: var(--space-3);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-height: 200px;
  width: 100%;
  /* Force cards to be visible */
  position: relative;
  display: block;
}

.unit-card:hover,
.building-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: var(--primary-gold);
}

.unit-card h4,
.building-card h4 {
  color: var(--primary-gold);
  margin: 0 0 var(--space-2) 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.unit-card p,
.building-card p {
  color: var(--text-color);
  margin: 0 0 var(--space-3) 0;
  line-height: 1.5;
}

.unit-stats,
.building-stats,
.unit-costs,
.building-costs,
.building-production {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
}

.unit-stats span,
.building-stats span,
.unit-costs span,
.building-costs span,
.building-production span {
  background: var(--accent-bg);
  color: var(--text-color);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}

.unit-costs span,
.building-costs span {
  background: var(--gold-bg);
  color: var(--gold-text);
}

.no-data,
.error {
  text-align: center;
  padding: var(--space-6);
  color: var(--text-muted);
  font-style: italic;
}

.error {
  color: var(--error-color);
}

/* Loading indicator */
.loading-container {
  display: none;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  color: var(--text-muted);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-gold);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: var(--space-8);
  padding: var(--space-6) 0;
}

.page-title {
  font-family: 'Cinzel', serif;
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-gold);
  margin: 0;
  text-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
}

.page-title i {
  font-size: 3.5rem;
  color: var(--primary-gold);
  text-shadow: 0 0 30px rgba(212, 175, 55, 0.8);
  animation: tower-glow 3s ease-in-out infinite alternate;
}

@keyframes tower-glow {
  0% {
    text-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
  }
  100% {
    text-shadow: 0 0 40px rgba(212, 175, 55, 1), 0 0 60px rgba(212, 175, 55, 0.8);
  }
} 

/* Content Messages */
.no-data, .error {
  text-align: center;
  padding: var(--space-8);
  margin: var(--space-4) 0;
  border-radius: var(--border-radius);
  font-style: italic;
}

.no-data {
  background: var(--glass-bg);
  color: var(--text-muted);
  border: 1px solid var(--border-color);
}

.error {
  background: rgba(255, 107, 107, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(255, 107, 107, 0.3);
}

/* Wiki Content Text */
.wiki-content-text {
  background: var(--glass-bg);
  padding: var(--space-6);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  margin-bottom: var(--space-4);
}

.wiki-content-text h4 {
  color: var(--primary-gold);
  margin-top: var(--space-6);
  margin-bottom: var(--space-3);
  font-family: 'Cinzel', serif;
}

.wiki-content-text h4:first-child {
  margin-top: 0;
}

.wiki-content-text p {
  margin-bottom: var(--space-4);
  line-height: 1.6;
}

.wiki-content-text ul {
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
}

.wiki-content-text li {
  margin-bottom: var(--space-2);
  line-height: 1.5;
}

.wiki-content-text strong {
  color: var(--primary-gold);
}

/* Static content styling */
.sub-content[data-subtab="general"],
.sub-content[data-subtab="strategy"] {
  display: none !important;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}

.sub-content[data-subtab="general"].active,
.sub-content[data-subtab="strategy"].active {
  display: block !important;
  opacity: 1;
}

/* Ensure static content is properly styled */
.sub-content[data-subtab="general"] .wiki-content-text,
.sub-content[data-subtab="strategy"] .wiki-content-text {
  background: var(--glass-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--space-6);
  margin: var(--space-4) 0;
  backdrop-filter: blur(10px);
} 