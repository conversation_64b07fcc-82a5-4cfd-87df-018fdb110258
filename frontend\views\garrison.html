<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WC Arena Garrison</title>
    <link rel="stylesheet" href="../css/warcraft-unified.css">
    <link rel="stylesheet" href="../css/chat-unified.css">
    <style>
        .garrison-container {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            color: #ecf0f1;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .garrison-header {
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .garrison-title {
            font-size: 2.5em;
            font-weight: bold;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .garrison-subtitle {
            font-size: 1.1em;
            opacity: 0.9;
            margin-top: 8px;
        }

        .garrison-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .garrison-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .garrison-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .section-header {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #3498db;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-card {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .action-card:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .action-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .action-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .action-description {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .status-indicators {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-value {
            font-size: 2em;
            font-weight: bold;
            color: #2ecc71;
        }

        .status-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }

        .chat-preview {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chat-message {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chat-message:last-child {
            border-bottom: none;
        }

        .message-author {
            font-weight: bold;
            color: #3498db;
        }

        .message-time {
            font-size: 0.8em;
            opacity: 0.6;
            margin-left: 10px;
        }

        .message-content {
            margin-top: 5px;
            opacity: 0.9;
        }

        .notification-item {
            background: rgba(231, 76, 60, 0.2);
            border-left: 4px solid #e74c3c;
            padding: 12px;
            margin-bottom: 10px;
            border-radius: 4px;
        }

        .notification-title {
            font-weight: bold;
            color: #e74c3c;
        }

        .notification-time {
            font-size: 0.8em;
            opacity: 0.6;
            margin-top: 5px;
        }

        .friends-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .friend-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: background 0.2s ease;
        }

        .friend-item:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .friend-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .friend-info {
            flex: 1;
        }

        .friend-name {
            font-weight: bold;
            color: #3498db;
        }

        .friend-status {
            font-size: 0.8em;
            opacity: 0.7;
        }

        .online-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #2ecc71;
            margin-left: 10px;
        }

        .offline-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #95a5a6;
            margin-left: 10px;
        }

        @media (max-width: 768px) {
            .garrison-content {
                grid-template-columns: 1fr;
                padding: 15px;
            }

            .garrison-title {
                font-size: 2em;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }
        }

        /* FIX: Ensure garrison content remains visible when chat window opens */
        .garrison-container {
            position: relative;
            z-index: 1;
        }

        .garrison-content {
            position: relative;
            z-index: 2;
        }

        /* Ensure chat window doesn't interfere with garrison content */
        .floating-chat-window {
            z-index: 9999 !important;
        }

        /* Fix for chat content visibility - specifically target the global messages */
        .context-content {
            display: none;
        }

        .context-content.active {
            display: flex !important;
            flex-direction: column;
            height: 100%;
        }

        /* Ensure chat messages are always visible */
        .chat-messages {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            height: auto !important;
            max-height: none !important;
        }

        /* Specifically fix the global messages div */
        #global-messages {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            height: auto !important;
            overflow-y: auto !important;
        }

        /* Ensure the chat window content is properly displayed */
        .floating-chat-window .context-content.active #global-messages {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* Override any hiding styles that might be applied */
        .chat-window-content {
            display: block !important;
        }

        .chat-window-content .context-content.active {
            display: flex !important;
        }
    </style>
</head>
<body>
    <!-- Main Site Navbar -->
    <div id="navbar-container"></div>
    
    <div class="garrison-container">
        <div class="garrison-header">
            <h1 class="garrison-title">🏕️ WC Arena Garrison</h1>
            <p class="garrison-subtitle">Your Command Center for Warcraft Gaming</p>
        </div>

        <div class="garrison-content">
            <!-- Quick Actions Section -->
            <div class="garrison-section">
                <h2 class="section-header">⚡ Quick Actions</h2>
                <div class="quick-actions">
                    <div class="action-card" onclick="openChat()">
                        <div class="action-icon">💬</div>
                        <div class="action-title">Global Chat</div>
                        <div class="action-description">Join the community conversation</div>
                    </div>
                    <div class="action-card" onclick="openGameChat()">
                        <div class="action-icon">🎮</div>
                        <div class="action-title">Game Chat</div>
                        <div class="action-description">Chat with players in your game</div>
                    </div>
                    <div class="action-card" onclick="openFriends()">
                        <div class="action-icon">👥</div>
                        <div class="action-title">Friends</div>
                        <div class="action-description">Manage your friends list</div>
                    </div>
                    <div class="action-card" onclick="openLadder()">
                        <div class="action-icon">🏆</div>
                        <div class="action-title">Ladder</div>
                        <div class="action-description">View rankings and stats</div>
                    </div>
                </div>
            </div>

            <!-- Status Overview Section -->
            <div class="garrison-section">
                <h2 class="section-header">📊 Status Overview</h2>
                <div class="status-indicators">
                    <div class="status-card">
                        <div class="status-value" id="online-players">0</div>
                        <div class="status-label">Players Online</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="active-games">0</div>
                        <div class="status-label">Active Games</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="unread-messages">0</div>
                        <div class="status-label">Unread Messages</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="notifications">0</div>
                        <div class="status-label">Notifications</div>
                    </div>
                </div>
            </div>

            <!-- Recent Chat Section -->
            <div class="garrison-section">
                <h2 class="section-header">💬 Recent Chat</h2>
                <div class="chat-preview" id="chat-preview">
                    <div class="chat-message">
                        <div class="message-author">System</div>
                        <div class="message-time">Just now</div>
                        <div class="message-content">Welcome to the Garrison! Connect with other players and stay updated.</div>
                    </div>
                </div>
            </div>

            <!-- Friends Online Section -->
            <div class="garrison-section">
                <h2 class="section-header">👥 Friends Online</h2>
                <div class="friends-list" id="friends-list">
                    <div class="friend-item">
                        <div class="friend-avatar">A</div>
                        <div class="friend-info">
                            <div class="friend-name">ArenaMaster</div>
                            <div class="friend-status">Playing Warcraft II</div>
                        </div>
                        <div class="online-indicator"></div>
                    </div>
                    <div class="friend-item">
                        <div class="friend-avatar">B</div>
                        <div class="friend-info">
                            <div class="friend-name">BattleLord</div>
                            <div class="friend-status">Online</div>
                        </div>
                        <div class="online-indicator"></div>
                    </div>
                </div>
            </div>

            <!-- Notifications Section -->
            <div class="garrison-section">
                <h2 class="section-header">🔔 Recent Notifications</h2>
                <div id="notifications-list">
                    <div class="notification-item">
                        <div class="notification-title">New Friend Request</div>
                        <div class="notification-time">2 minutes ago</div>
                    </div>
                    <div class="notification-item">
                        <div class="notification-title">Match Result Available</div>
                        <div class="notification-time">5 minutes ago</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Essential Scripts for Navbar and User Management -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="/js/api-config.js"></script>
    <script src="/js/navbar-modern.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/utils.js"></script>

    <script>
        // Initialize garrison when page loads
        window.addEventListener('load', async () => {
            console.log('🔄 Garrison page loaded, initializing...');

            // Load unified navigation first
            if (typeof window.loadNavigation === 'function') {
                await window.loadNavigation();
            } else if (typeof window.loadNavbar === 'function') {
                await window.loadNavbar();
            }

            // Update navbar profile with user data
            setTimeout(async () => {
                if (window.updateNavbarProfileUnified) {
                    console.log('🔄 Updating navbar profile (unified) on garrison page');
                    await window.updateNavbarProfileUnified();
                } else if (window.updateNavbarProfile) {
                    console.log('🔄 Updating navbar profile (legacy) on garrison page');
                    await window.updateNavbarProfile();
                }
            }, 500);
        });
        
        // Garrison functionality - Updated to use unified chat system
        function openChat() {
            console.log('🔄 Opening global chat from garrison...');
            if (window.chatManager) {
                window.chatManager.showFloatingWindow();
                window.chatManager.switchContext('global');

                // Ensure chat messages are visible after opening
                setTimeout(() => {
                    const globalMessages = document.getElementById('global-messages');
                    if (globalMessages) {
                        globalMessages.style.display = 'block';
                        globalMessages.style.visibility = 'visible';
                        globalMessages.style.opacity = '1';
                        console.log('✅ Ensured global messages are visible');
                    }
                }, 100);
            } else {
                console.warn('⚠️ Chat manager not available, falling back to page navigation');
                window.open('/views/chat.html', '_blank');
            }
        }

        function openGameChat() {
            console.log('🔄 Opening game chat from garrison...');
            if (window.chatManager) {
                window.chatManager.showFloatingWindow();
                window.chatManager.switchContext('global'); // Use global for now, can be enhanced later

                // Ensure chat messages are visible after opening
                setTimeout(() => {
                    const globalMessages = document.getElementById('global-messages');
                    if (globalMessages) {
                        globalMessages.style.display = 'block';
                        globalMessages.style.visibility = 'visible';
                        globalMessages.style.opacity = '1';
                        console.log('✅ Ensured global messages are visible');
                    }
                }, 100);
            } else {
                console.warn('⚠️ Chat manager not available, falling back to page navigation');
                window.open('/views/chat.html?context=game', '_blank');
            }
        }

        function openFriends() {
            window.open('/views/friends.html', '_blank');
        }

        function openLadder() {
            window.open('/views/ladder.html', '_blank');
        }

        // Update status indicators
        function updateStatus() {
            // Simulate real-time updates
            document.getElementById('online-players').textContent = Math.floor(Math.random() * 100) + 50;
            document.getElementById('active-games').textContent = Math.floor(Math.random() * 20) + 5;
            document.getElementById('unread-messages').textContent = Math.floor(Math.random() * 10);
            document.getElementById('notifications').textContent = Math.floor(Math.random() * 5);
        }

        // Update status every 30 seconds
        setInterval(updateStatus, 30000);

        // Initial status update
        updateStatus();

        // Handle window focus to refresh data
        window.addEventListener('focus', () => {
            updateStatus();
        });
    </script>
</body>
</html> 