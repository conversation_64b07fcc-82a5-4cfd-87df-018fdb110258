/**
 * Enhanced Ladder Manager for War1/War2/War3 System
 * Handles game type switching, map search, and comprehensive ladder functionality
 */

export class LadderManager {
  constructor() {
    this.currentGameType = 'war2'; // Keep this for backend API consistency
    this.currentMatchType = 'all';
    this.currentPage = 1;
    this.totalPages = 1;
    this.searchQuery = '';
    this.mapSearch = '';
    this.players = [];
    this.debounceTimer = null;
    this.chartInstances = {};
    this.isInitialized = false;
    this.isActive = true; // LadderManager starts active for WC2/WC3
    
    // Game configuration for UI display
    this.gameConfigs = {
      'war1': { 
        title: 'WC1', 
        fullTitle: 'Warcraft I', 
        races: ['human', 'orc'] 
      },
      'war2': { 
        title: 'WC2', 
        fullTitle: 'Warcraft II', 
        races: ['human', 'orc'] 
      },
      'war3': { 
        title: 'WC3', 
        fullTitle: 'Warcraft III', 
        races: ['human', 'orc', 'undead', 'night_elf'] 
      }
    };
    
        // Don't listen to gameTabChanged anymore - let direct tab handlers manage this
    
    this.init();
  }

  async init() {
    console.log('🎯 Initializing Enhanced Ladder Manager...');
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
    } else {
      this.setupEventListeners();
    }
    
    await this.loadInitialData();
    this.isInitialized = true;
  }

  setupEventListeners() {
    console.log('🔧 Setting up event listeners...');
    
    // Game type tab switching - handle all tabs
    const gameTabs = document.querySelectorAll('.game-tab');
    gameTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        const gameType = e.currentTarget.dataset.gameType;
        console.log(`🎮 Tab clicked: ${gameType}`);
        
        // Update visual tab state first
        document.querySelectorAll('.game-tab').forEach(t => t.classList.remove('active'));
        e.currentTarget.classList.add('active');
        
        if (gameType === 'war1') {
          // Handle WC1
          this.deactivate();
          if (window.wc1LadderManager) {
            window.wc1LadderManager.onActivate();
          }
        } else if (gameType === 'war2' || gameType === 'war3') {
          // Handle WC2/WC3
          this.activate();
          if (window.wc1LadderManager) {
            window.wc1LadderManager.onDeactivate();
          }
          this.switchGameType(gameType);
        }
      });
    });

    // Player search functionality
    const searchBtn = document.getElementById('search-btn');
    const searchInput = document.getElementById('player-search');
    const clearSearchBtn = document.getElementById('clear-search-btn');
    
    if (searchBtn && searchInput) {
      searchBtn.addEventListener('click', () => this.searchPlayers());
      searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') this.searchPlayers();
      });
      
      searchInput.addEventListener('input', (e) => {
        const value = e.target.value.trim();
        if (clearSearchBtn) {
          clearSearchBtn.style.display = value.length > 0 ? 'flex' : 'none';
        }
        
        if (value === '') {
          this.searchQuery = '';
          this.currentPage = 1;
          this.loadLeaderboard();
        }
      });
      
      if (clearSearchBtn) {
        clearSearchBtn.style.display = 'none';
        clearSearchBtn.addEventListener('click', () => {
          searchInput.value = '';
          clearSearchBtn.style.display = 'none';
          this.searchQuery = '';
          this.currentPage = 1;
          this.loadLeaderboard();
          searchInput.focus();
        });
      }
    }

    // Map search functionality
    const mapSearchBtn = document.getElementById('map-search-btn');
    const mapSearchInput = document.getElementById('map-search');
    
    if (mapSearchBtn && mapSearchInput) {
      mapSearchBtn.addEventListener('click', () => this.searchByMap());
      mapSearchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') this.searchByMap();
      });
      
      mapSearchInput.addEventListener('input', (e) => {
        const value = e.target.value.trim();
        
        if (value === '') {
          this.mapSearch = '';
          this.currentPage = 1;
          this.loadLeaderboard();
        }
      });
    }

    // Match type filter buttons - only target WC2/WC3 controls
    const wc2wc3Controls = document.getElementById('wc2-wc3-controls');
    if (wc2wc3Controls) {
      const filterButtons = wc2wc3Controls.querySelectorAll('.filter-btn');
      filterButtons.forEach(btn => {
        btn.addEventListener('click', (e) => this.filterByMatchType(e.target.dataset.matchType));
      });
    }

    // Pagination
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    if (prevBtn) prevBtn.addEventListener('click', () => this.previousPage());
    if (nextBtn) nextBtn.addEventListener('click', () => this.nextPage());

    // Report match functionality
    const reportBtn = document.getElementById('report-match-btn');
    if (reportBtn) {
      reportBtn.addEventListener('click', () => this.openReportModal());
    }

    // Modal functionality
    document.querySelectorAll('.close-modal').forEach(btn => {
      btn.addEventListener('click', (e) => this.closeModal(e.target.closest('.modal')));
    });

    document.querySelectorAll('.modal').forEach(modal => {
      modal.addEventListener('click', (e) => {
        if (e.target === modal || e.target.classList.contains('modal-overlay')) {
          this.closeModal(modal);
        }
      });
    });

    console.log('✅ Enhanced event listeners set up');
  }

  async loadInitialData() {
    try {
      console.log('📊 Loading initial enhanced ladder data...');
      
      // Update the title immediately
      this.updateLeaderboardHeader('all', {});
      
      await Promise.all([
        this.loadRanks(),
        this.loadLeaderboard(),
        this.loadRecentMatches(),
        this.loadStats()
      ]);
      
      console.log('✅ Initial enhanced data loaded successfully');
    } catch (error) {
      console.error('❌ Error loading initial data:', error);
      // Show error message instead of mock data
      this.showErrorMessage('Failed to load ladder data. Please check your connection and try again.');
    }
  }

  async switchGameType(gameType) {
    // Ensure we use the correct gameType format for the backend API
    // The backend expects 'war2' and 'war3', not 'warcraft2' and 'warcraft3'
    let apiGameType = gameType;
    if (gameType === 'warcraft2') apiGameType = 'war2';
    if (gameType === 'warcraft3') apiGameType = 'war3';
    
    console.log(`🎮 LadderManager: Switching from ${this.currentGameType} to ${apiGameType}`);
    
    if (this.currentGameType === apiGameType && this.isActive) {
      console.log('🎮 Already on the same game type and active, skipping switch');
      return;
    }
    
    this.currentGameType = apiGameType;
    this.currentPage = 1; // Reset to first page
    this.searchQuery = ''; // Clear search
    this.mapSearch = ''; // Clear map filter
    
    // Clear current data
    this.players = [];
    
    // Clear search inputs
    const searchInput = document.getElementById('player-search');
    const mapSearchInput = document.getElementById('map-search');
    if (searchInput) searchInput.value = '';
    if (mapSearchInput) mapSearchInput.value = '';
    
    // Show WC2/WC3 controls and hide any WC1-specific controls
    this.showControls();
    
    // Update UI immediately
    this.updateGameTypeDisplay();
    
    // Load new data
    await this.loadInitialData();
  }

  /**
   * Show WC2/WC3 controls with consistent horizontal styling
   */
  showControls() {
    const controls = document.getElementById('wc2-wc3-controls');
    if (controls) {
      controls.style.display = 'flex';
      
      // Ensure consistent styling through GameSwitchManager if available
      if (window.gameSwitchManager) {
        window.gameSwitchManager.applyConsistentControlsStyle(controls);
      }
    }
  }

  async loadLeaderboard(matchType = null, page = null, search = null, mapSearch = null) {
    try {
      const type = matchType || this.currentMatchType;
      const pageNum = page || this.currentPage;
      const playerSearch = search !== null ? search : this.searchQuery;
      const mapFilter = mapSearch !== null ? mapSearch : this.mapSearch;
      
      console.log(`🎯 Loading leaderboard: ${this.currentGameType}/${type}, page ${pageNum}`);
      
      const params = new URLSearchParams({
        gameType: this.currentGameType,
        matchType: type,
        limit: 10,
        page: pageNum
      });
      
      if (playerSearch) params.append('search', playerSearch);
      if (mapFilter) params.append('map', mapFilter);
      
      const response = await fetch(`/api/ladder/rankings?${params}`);
      if (!response.ok) throw new Error('Failed to fetch leaderboard');
      
      const data = await response.json();
      console.log('📊 Leaderboard API response:', data);
      
      let players, totalPages;
      if (Array.isArray(data)) {
        players = data;
        totalPages = Math.ceil(players.length / 10);
      } else {
        players = data.players || [];
        totalPages = data.pagination?.pages || 1;
      }
      
      this.totalPages = totalPages;
      this.displayLeaderboard(players);
      this.updatePagination(pageNum, totalPages);
      
      // Update header information
      this.updateLeaderboardHeader(type, { total: players.length });
      
    } catch (error) {
      console.error('❌ Error loading leaderboard:', error);
      // Show empty state instead of mock data
      this.displayLeaderboard([]);
    }
  }

  displayLeaderboard(players) {
    const tbody = document.getElementById('leaderboard-body');
    if (!tbody) return;

    if (!players || players.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="6" class="no-results">
            ${this.currentMatchType && this.currentMatchType !== 'all' ? 
              `No players found for ${this.currentMatchType} matches in ${this.gameConfigs[this.currentGameType].title}` :
              this.mapSearch ? 
                `No players found for map "${this.mapSearch}" in ${this.gameConfigs[this.currentGameType].title}` :
                `No players found in ${this.gameConfigs[this.currentGameType].title}`
            }
          </td>
        </tr>
      `;
      return;
    }

    const startRank = (this.currentPage - 1) * 10 + 1;
    
    tbody.innerHTML = players.map((player, index) => {
      const rank = startRank + index;
      const wins = player.stats?.wins || player.wins || 0;
      const losses = player.stats?.losses || player.losses || 0;
      const totalGames = wins + losses;
      const winRate = totalGames > 0 ? ((wins / totalGames) * 100).toFixed(1) : '0.0';
      const winRateClass = this.getWinRateClass(parseFloat(winRate));
      
      // Get race display for current game type
      const raceDisplay = this.formatRaceDisplay(player.preferredRace, this.currentGameType);
      
      return `
        <tr class="player-row" data-player-id="${player._id}">
          <td class="rank-cell">
            <div class="rank-info">
              <span class="rank-number">${rank}</span>
              ${player.rank ? `
                <div class="rank-badge">
                  <img src="/assets/img/ranks/${player.rank.name.toLowerCase()}.png" 
                       alt="${player.rank.name}" 
                       onerror="this.src='/assets/img/ranks/emblem.png';">
                </div>
              ` : ''}
            </div>
          </td>
          <td class="player-cell">
            <div class="player-info">
              <span class="player-name" onclick="window.ladderManager.showPlayerStats('${player.name}')">${player.name}</span>
            </div>
          </td>
          <td class="race-cell">${raceDisplay}</td>
          <td class="mmr-cell">
            <span class="mmr-value">${this.getDisplayMMR(player)}</span>
          </td>
          <td class="stats-cell">
            <div class="stats-info">
              <span class="wins">${wins}W</span>
              <span class="losses">${losses}L</span>
              <span class="win-rate ${winRateClass}">${winRate}%</span>
            </div>
          </td>
          <td class="games-cell">
            <span class="total-games">${totalGames}</span>
          </td>
        </tr>
      `;
    }).join('');
  }

  formatRaceDisplay(race, gameType) {
    const raceMap = {
      human: 'Human',
      orc: 'Orc',
      undead: 'Undead',
      night_elf: 'Night Elf',
      random: 'Random'
    };
    
    return raceMap[race] || race || 'Unknown';
  }

  getWinRateClass(winRate) {
    if (winRate >= 70) return 'excellent';
    if (winRate >= 60) return 'good';
    if (winRate >= 50) return 'average';
    if (winRate >= 40) return 'below-average';
    return 'poor';
  }

  getDisplayMMR(player) {
    // Debug logging
    console.log(`🔍 getDisplayMMR for ${player.name}:`);
    console.log(`  currentMatchType: ${this.currentMatchType}`);
    console.log(`  player.mmr: ${player.mmr}`);
    console.log(`  player.matchTypeStats:`, player.matchTypeStats);
    
    // If we're filtering by a specific match type, show match-type-specific MMR
    if (this.currentMatchType !== 'all' && player.matchTypeStats) {
      const mmr = player.matchTypeStats.mmr || player.mmr || 1500;
      console.log(`  → Using match-type MMR: ${mmr}`);
      return mmr;
    }
    
    // For 'all' match types or no match type stats, show overall MMR
    const mmr = player.mmr || 1500;
    console.log(`  → Using overall MMR: ${mmr}`);
    return mmr;
  }

  updateLeaderboardHeader(matchType, filterInfo) {
    // Update any header information based on current filters
    const gameConfig = this.gameConfigs[this.currentGameType];
    document.title = `WC Arena - ${gameConfig.title} Ladder`;
    
    // Update leaderboard title
    const leaderboardTitle = document.getElementById('leaderboard-title');
    if (leaderboardTitle) {
      leaderboardTitle.textContent = `${gameConfig.title} Leaderboard`;
    }
    
    // Update report button text
    const reportBtn = document.getElementById('report-match-text');
    if (reportBtn) {
      reportBtn.textContent = `REPORT ${gameConfig.title.toUpperCase()} MATCH`;
    }
  }

  async loadStats() {
    try {
      console.log(`📊 Loading stats for ${this.currentGameType}`);
      const response = await fetch(`/api/ladder/stats?gameType=${this.currentGameType}`);
      if (!response.ok) throw new Error('Failed to fetch stats');
      
      const stats = await response.json();
      console.log('📊 Stats loaded:', stats);
      this.displayStats(stats);
    } catch (error) {
      console.error('Error loading stats:', error);
      // Don't show mock stats - just log the error
    }
  }

  displayStats(stats) {
    this.updateOverviewStats(stats.overview);
    this.updateRaceStats(stats.races);
    this.updateMatchTypeStats(stats.matchTypes);
    this.createCharts(stats);
  }

  updateRaceStats(races) {
    const racesList = document.getElementById('race-stats-list');
    if (!racesList || !races) return;

    const gameConfig = this.gameConfigs[this.currentGameType];
    if (!gameConfig || !gameConfig.races) {
      console.warn(`No race configuration found for game type: ${this.currentGameType}`);
      return;
    }
    
    const allowedRaces = gameConfig.races;
    
    racesList.innerHTML = allowedRaces.map(race => {
      const raceData = races[race] || { count: 0, percentage: 0 };
      const raceName = this.formatRaceDisplay(race, this.currentGameType);
      
      return `
        <li>
          <span class="stats-name">${raceName}</span>
          <span class="stats-value">${raceData.count}</span>
          <span class="stats-percentage">(${raceData.percentage}%)</span>
        </li>
      `;
    }).join('');
  }

  updateMatchTypeStats(matchTypes) {
    const modesList = document.getElementById('modes-stats-list');
    if (!modesList || !matchTypes) return;

    const modes = ['1v1', '2v2', '3v3', '4v4', 'ffa', 'vsai'];
    modesList.innerHTML = modes.map(mode => {
      const modeData = matchTypes[mode] || { count: 0, percentage: 0 };
      const modeName = mode === 'vsai' ? 'vs AI' : mode.toUpperCase();
      
      return `
        <li>
          <span class="stats-name">${modeName}</span>
          <span class="stats-value">${modeData.count}</span>
          <span class="stats-percentage">(${modeData.percentage}%)</span>
        </li>
      `;
    }).join('');
  }

  updateOverviewStats(overview) {
    if (!overview) return;
    console.log('📊 Overview stats:', overview);
  }

  async loadRecentMatches() {
    try {
      console.log(`📊 Loading recent matches for ${this.currentGameType}`);
      const response = await fetch(`/api/ladder/recent-matches?gameType=${this.currentGameType}&limit=10`);
      if (!response.ok) throw new Error('Failed to fetch recent matches');
      
      const matches = await response.json();
      console.log('📊 Recent matches loaded:', matches);
      this.displayRecentMatches(matches);
    } catch (error) {
      console.error('Error loading recent matches:', error);
      // Show empty state instead of mock data
      this.displayRecentMatches([]);
    }
  }

  displayRecentMatches(matches) {
    const container = document.getElementById('recent-matches-container');
    if (!container) return;

    if (!matches || matches.length === 0) {
      container.innerHTML = '<div class="no-matches">No recent matches</div>';
      return;
    }

    container.innerHTML = matches.map(match => `
      <div class="match-item">
        <div class="match-header">
          <span class="match-type">${match.matchType}</span>
          <span class="match-map">${match.map}</span>
        </div>
        <div class="match-players">
          ${match.players.map(player => `
            <span class="match-player ${player.result}">${player.name}</span>
          `).join(' vs ')}
        </div>
        <div class="match-time">${this.formatDate(match.createdAt || match.date)}</div>
      </div>
    `).join('');
  }

  async loadRanks() {
    try {
      console.log('📊 Loading ranks');
      const response = await fetch('/api/ladder/ranks');
      if (!response.ok) throw new Error('Failed to fetch ranks');
      
      const ranks = await response.json();
      console.log('📊 Ranks loaded:', ranks);
      this.displayRanks(ranks);
    } catch (error) {
      console.error('Error loading ranks:', error);
      // Show empty state instead of mock data
      this.displayRanks([]);
    }
  }

  displayRanks(ranks) {
    const container = document.getElementById('ranks-container');
    if (!container || !ranks) return;

    const reversedRanks = [...ranks].reverse();
    
    container.innerHTML = reversedRanks.map(rank => {
      const rankImagePath = rank.image.startsWith('/') || rank.image.startsWith('http')
        ? rank.image
        : `/assets/img/ranks/${rank.image}`;
      
      return `
        <div class="rank-item">
          <img src="${rankImagePath}" 
               alt="${rank.name}" 
               class="rank-image" 
               onerror="this.style.display='none'">
          <div class="rank-details">
            <h4 class="rank-name">${rank.name}</h4>
            <p class="rank-threshold">${rank.threshold}+ MMR</p>
          </div>
        </div>
      `;
    }).join('');
  }

  // Search and filter methods
  searchPlayers() {
    const searchInput = document.getElementById('player-search');
    if (!searchInput) return;
    
    this.searchQuery = searchInput.value.trim();
    this.currentPage = 1;
    this.loadLeaderboard();
  }

  searchByMap() {
    const mapSearchInput = document.getElementById('map-search');
    if (!mapSearchInput) return;
    
    this.mapSearch = mapSearchInput.value.trim();
    this.currentPage = 1;
    this.loadLeaderboard();
  }

  filterByMatchType(matchType) {
    this.currentMatchType = matchType;
    this.currentPage = 1;
    
    // Update active filter button - only for WC2/WC3 controls
    const wc2wc3Controls = document.getElementById('wc2-wc3-controls');
    if (wc2wc3Controls) {
      wc2wc3Controls.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.matchType === matchType) {
          btn.classList.add('active');
        }
      });
    }
    
    this.loadLeaderboard();
  }

  // Pagination methods
  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.loadLeaderboard();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.loadLeaderboard();
    }
  }

  updatePagination(currentPage, totalPages) {
    const pageInfo = document.getElementById('page-info');
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    
    if (pageInfo) {
      pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
    }
    
    if (prevBtn) {
      prevBtn.disabled = currentPage <= 1;
    }
    
    if (nextBtn) {
      nextBtn.disabled = currentPage >= totalPages;
    }
  }

  // Modal and utility methods
  openReportModal() {
    console.log(`🎯 Opening report modal for: ${this.currentGameType}, isActive: ${this.isActive}`);
    
    // Check if WC1 is active by checking WC1LadderManager state
    if (window.wc1LadderManager && window.wc1LadderManager.isActive) {
      console.log('🗡️ WC1 is active, routing to WC1 report modal');
      window.wc1LadderManager.openWC1ReportModal();
      return;
    }
    
    // Handle WC2/WC3 report modal
    console.log('🎮 Opening WC2/WC3 report modal');
    const modal = document.getElementById('report-match-modal');
    if (modal) {
      modal.classList.add('show');
      document.body.style.overflow = 'hidden';
    } else {
      console.warn('Report match modal not found for', this.currentGameType);
    }
  }

  closeModal(modal) {
    if (modal) {
      modal.classList.remove('show');
      document.body.style.overflow = 'auto';
    }
  }

  showPlayerStats(playerName) {
    console.log('Showing stats for player:', playerName);
    // Call the global player details modal function
    if (window.openPlayerDetailsModal) {
      window.openPlayerDetailsModal(playerName);
    } else if (window.showPlayerDetails) {
      window.showPlayerDetails(playerName);
    } else {
      console.error('Player details modal function not found');
    }
  }

  formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  }

  createCharts(stats) {
    // Destroy existing charts
    Object.values(this.chartInstances).forEach(chart => {
      if (chart) chart.destroy();
    });
    this.chartInstances = {};

    // Create race distribution chart
    if (stats.races) {
      this.createRaceChart(stats.races);
    }

    // Create match type distribution chart
    if (stats.matchTypes) {
      this.createMatchTypeChart(stats.matchTypes);
    }
  }

  createRaceChart(raceData) {
    const canvas = document.getElementById('race-chart');
    if (!canvas || !window.Chart) return;

    const gameConfig = this.gameConfigs[this.currentGameType];
    const data = gameConfig.races.map(race => raceData[race]?.count || 0);
    const labels = gameConfig.races.map(race => this.formatRaceDisplay(race, this.currentGameType));

    this.chartInstances.raceChart = new Chart(canvas, {
      type: 'doughnut',
      data: {
        labels: labels,
        datasets: [{
          data: data,
          backgroundColor: [
            'rgba(218, 165, 32, 0.8)',
            'rgba(220, 20, 60, 0.8)',
            'rgba(128, 0, 128, 0.8)',
            'rgba(34, 139, 34, 0.8)'
          ],
          borderColor: [
            'rgba(218, 165, 32, 1)',
            'rgba(220, 20, 60, 1)',
            'rgba(128, 0, 128, 1)',
            'rgba(34, 139, 34, 1)'
          ],
          borderWidth: 2
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        }
      }
    });
  }

  createMatchTypeChart(matchTypeData) {
    const canvas = document.getElementById('match-type-chart');
    if (!canvas || !window.Chart) return;

    const modes = ['1v1', '2v2', '3v3', '4v4', 'ffa', 'vsai'];
    const data = modes.map(mode => matchTypeData[mode]?.count || 0);
    const labels = modes.map(mode => mode === 'vsai' ? 'vs AI' : mode.toUpperCase());

    this.chartInstances.matchTypeChart = new Chart(canvas, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          data: data,
          backgroundColor: 'rgba(218, 165, 32, 0.8)',
          borderColor: 'rgba(218, 165, 32, 1)',
          borderWidth: 2
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              color: '#ddd'
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          x: {
            ticks: {
              color: '#ddd'
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        }
      }
    });
  }

  updateGameTypeDisplay() {
    // Update any UI elements that show the current game type
    const gameConfig = this.gameConfigs[this.currentGameType];
    if (!gameConfig) return;
    
    // Update page title
    document.title = `WC Arena - ${gameConfig.fullTitle} Ladder`;
    
    // Update leaderboard title
    const leaderboardTitle = document.getElementById('leaderboard-title');
    if (leaderboardTitle) {
      leaderboardTitle.textContent = `${gameConfig.fullTitle} Leaderboard`;
    }
    
    // Update report button text
    const reportBtn = document.getElementById('report-match-text');
    if (reportBtn) {
      reportBtn.textContent = `REPORT ${gameConfig.title.toUpperCase()} MATCH`;
    }
    
    console.log(`✅ Updated display for ${gameConfig.fullTitle}`);
  }

  /**
   * Show error message to user instead of mock data
   */
  showErrorMessage(message) {
    const tbody = document.getElementById('leaderboard-body');
    if (tbody) {
      tbody.innerHTML = `
        <tr>
          <td colspan="6" class="error-message">
            <div class="error-content">
              <i class="fas fa-exclamation-triangle"></i>
              <span>${message}</span>
              <button onclick="ladderManager.loadInitialData()" class="retry-btn">
                <i class="fas fa-refresh"></i> Retry
              </button>
            </div>
          </td>
        </tr>
      `;
    }
  }

  /**
   * Deactivate this manager (used when switching to WC1)
   */
  deactivate() {
    console.log('🔽 LadderManager deactivating for WC1');
    this.isActive = false;
  }

  /**
   * Activate this manager (used when switching back from WC1)
   */
  activate() {
    console.log('🔼 LadderManager activating');
    this.isActive = true;
    
    // Restore filter button states for WC2/WC3
    this.filterByMatchType(this.currentMatchType);
    
    // Reload content when reactivating
    this.loadInitialData();
  }
}

// Initialize ladder manager and player manager when DOM is ready
let ladderManager;
let playerManager;

async function initLadderPage() {
  if (!ladderManager) {
    // Initialize PlayerManager first
    const { PlayerManager } = await import('./PlayerManager.js');
    playerManager = new PlayerManager();
    window.playerManager = playerManager;
    
    // Then initialize LadderManager
    ladderManager = new LadderManager();
    window.ladderManager = ladderManager; // Make it globally accessible
    
    console.log('✅ Ladder page initialized with all managers');
  }
}

// Auto-init
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initLadderPage);
} else {
  initLadderPage();
} 