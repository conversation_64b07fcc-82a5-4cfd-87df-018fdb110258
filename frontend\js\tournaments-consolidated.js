/**
 * Consolidated Tournament System
 * Optimized replacement for tournaments.js (47KB) + tournament-manage.js (29KB) + bracket.js (11KB)
 * 
 * Features:
 * - Tournament listing and creation
 * - Tournament management and administration  
 * - Bracket generation and visualization
 * - User participation (join/leave)
 * - Single-file architecture for better performance
 */

class ConsolidatedTournamentSystem {
  constructor() {
    this.currentUser = null;
    this.currentTournament = null;
    this.tournaments = [];
    this.state = {
      activeFilter: 'all',
      isLoading: false,
      currentPage: 'list' // 'list' or 'manage'
    };
    
    this.init();
  }

  /**
   * Initialize the system
   */
  async init() {
    console.log('🏆 Initializing Consolidated Tournament System...');
    
    try {
      // Load current user
      await this.loadCurrentUser();
      
      // Setup event listeners based on page type
      this.setupEventListeners();
      
      // Initialize based on current page
      const urlParams = new URLSearchParams(window.location.search);
      const tournamentId = urlParams.get('id');
      
      if (tournamentId) {
        // Management page
        this.state.currentPage = 'manage';
        await this.initManagementPage(tournamentId);
      } else {
        // List page
        this.state.currentPage = 'list';
        await this.initListPage();
      }
      
      console.log('✅ Tournament system initialized');
    } catch (error) {
      console.error('❌ Failed to initialize tournament system:', error);
      this.showError('Failed to initialize tournament system');
    }
  }

  /**
   * Load current user
   */
  async loadCurrentUser() {
    try {
      const response = await fetch('/api/users/me', { credentials: 'include' });
      if (response.ok) {
        this.currentUser = await response.json();
        console.log('👤 Current user loaded:', this.currentUser.username);
      }
    } catch (error) {
      console.error('Failed to load current user:', error);
    }
  }

  /**
   * Initialize tournament list page
   */
  async initListPage() {
    console.log('📋 Initializing tournament list page...');
    
    // Setup filters
    this.setupFilters();
    
    // Load tournaments
    await this.loadTournaments();
  }

  /**
   * Initialize tournament management page
   */
  async initManagementPage(tournamentId) {
    console.log('⚙️ Initializing tournament management page...');
    
    // Load tournament details
    await this.loadTournamentDetails(tournamentId);
    
    // Setup management interface
    this.setupManagementInterface();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Create tournament button
    const createBtn = document.getElementById('create-tournament-btn');
    if (createBtn) {
      createBtn.addEventListener('click', () => this.showCreateTournamentModal());
    }

    // Create tournament form
    const createForm = document.getElementById('create-tournament-form');
    if (createForm) {
      createForm.addEventListener('submit', (e) => this.handleCreateTournament(e));
    }

    // Modal close handlers
    this.setupModalHandlers();
    
    // Tournament action handlers (dynamically added)
    document.addEventListener('click', (e) => {
      // Don't interfere with navbar elements
      if (e.target.closest('#navbar-container') || e.target.closest('.navbar')) {
        return;
      }
      this.handleTournamentActions(e);
    });
  }

  /**
   * Setup filter buttons
   */
  setupFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(button => {
      button.addEventListener('click', () => {
        const filter = button.dataset.status || 'all';
        this.setActiveFilter(filter);
        this.loadTournaments(filter);
      });
    });
  }

  /**
   * Set active filter
   */
  setActiveFilter(filter) {
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.classList.toggle('active', (btn.dataset.status || 'all') === filter);
    });
    this.state.activeFilter = filter;
  }

  /**
   * Load tournaments with optional filter
   */
  async loadTournaments(filter = this.state.activeFilter) {
    const tournamentList = document.getElementById('tournaments-list');
    if (!tournamentList) return;

    try {
      this.showLoading(tournamentList, 'Loading tournaments...');
      
      const response = await fetch('/api/tournaments', { credentials: 'include' });
      if (!response.ok) throw new Error('Failed to fetch tournaments');
      
      const data = await response.json();
      this.tournaments = Array.isArray(data) ? data : data.tournaments || [];
      
      // Apply filter
      const filteredTournaments = this.filterTournaments(this.tournaments, filter);
      
      this.renderTournaments(filteredTournaments);
      
    } catch (error) {
      console.error('Failed to load tournaments:', error);
      tournamentList.innerHTML = `
        <div class="error-state">
          <h3>Failed to Load Tournaments</h3>
          <p>There was an error loading tournaments. Please try again.</p>
          <button class="btn btn-primary" onclick="window.tournamentSystem.loadTournaments()">
            <i class="fas fa-refresh"></i> Retry
          </button>
        </div>
      `;
    }
  }

  /**
   * Filter tournaments
   */
  filterTournaments(tournaments, filter) {
    if (!filter || filter === 'all') return tournaments;
    
    return tournaments.filter(tournament => {
      switch (filter) {
        case 'my-tournaments':
          return this.isUserInvolved(tournament);
        case 'registration':
        case 'in_progress':
        case 'completed':
          return tournament.status === filter;
        default:
          return true;
      }
    });
  }

  /**
   * Check if user is involved in tournament
   */
  isUserInvolved(tournament) {
    if (!this.currentUser) return false;
    
    const userId = this.currentUser._id || this.currentUser.id;
    
    // Check if organizer
    if (tournament.organizer?.userId === userId) return true;
    
    // Check if participant
    return tournament.participants?.some(p => p.userId === userId);
  }

  /**
   * Render tournaments list
   */
  renderTournaments(tournaments) {
    const container = document.getElementById('tournaments-list');
    
    if (tournaments.length === 0) {
      container.innerHTML = `
        <div class="empty-state">
          <h3>No Tournaments Found</h3>
          <p>No tournaments match your current filter.</p>
          <button class="btn btn-primary" onclick="window.tournamentSystem.showCreateTournamentModal()">
            <i class="fas fa-plus"></i> Create Tournament
          </button>
        </div>
      `;
      return;
    }

    container.innerHTML = tournaments
      .map(tournament => this.createTournamentCard(tournament))
      .join('');
  }

  /**
   * Create tournament card HTML
   */
  createTournamentCard(tournament) {
    const isOrganizer = this.isUserOrganizer(tournament);
    const isParticipant = this.isUserParticipant(tournament);
    const participantInfo = this.getUserParticipantInfo(tournament);
    
    const actionButtons = this.getTournamentActions(tournament, isOrganizer, isParticipant);
    
    return `
      <div class="tournament-card" data-tournament-id="${tournament._id}">
        <div class="tournament-header">
          <h3>${tournament.name}</h3>
          <span class="tournament-status ${tournament.status}">
            ${tournament.status.replace('_', ' ').toUpperCase()}
          </span>
        </div>
        <p class="tournament-description">${tournament.description || 'No description provided'}</p>
        <div class="tournament-info">
          <div class="info-row">
            <span class="info-label">
              <i class="fas fa-users"></i> Participants
            </span>
            <span class="info-value">${tournament.participants?.length || 0}/${tournament.maxParticipants}</span>
          </div>
          <div class="info-row">
            <span class="info-label">
              <i class="fas fa-trophy"></i> Type
            </span>
            <span class="info-value">${tournament.type.replace('_', ' ').toUpperCase()}</span>
          </div>
          <div class="info-row">
            <span class="info-label">
              <i class="fas fa-calendar"></i> Start Date
            </span>
            <span class="info-value">${new Date(tournament.startDate).toLocaleDateString()}</span>
          </div>
        </div>
        ${isParticipant && participantInfo ? `
          <div class="participant-status">
            <i class="fas fa-user-circle"></i> 
            Participating as: <strong>${participantInfo.playerName}</strong>
          </div>
        ` : ''}
        <div class="tournament-actions">
          ${actionButtons}
        </div>
      </div>
    `;
  }

  /**
   * Get tournament action buttons
   */
  getTournamentActions(tournament, isOrganizer, isParticipant) {
    switch (tournament.status) {
      case 'draft':
        return isOrganizer ? 
          `<button class="btn btn-primary" data-action="publish" data-tournament-id="${tournament._id}">
            <i class="fas fa-upload"></i> Publish
          </button>` : '';
          
      case 'registration':
        if (isOrganizer) {
          return `
            <button class="btn btn-primary" data-action="start" data-tournament-id="${tournament._id}">
              <i class="fas fa-play"></i> Start Tournament
            </button>
            <button class="btn btn-secondary" data-action="manage" data-tournament-id="${tournament._id}">
              <i class="fas fa-cogs"></i> Manage
            </button>
            ${!isParticipant ? `<button class="btn btn-primary" data-action="join" data-tournament-id="${tournament._id}">
              <i class="fas fa-sign-in-alt"></i> Join
            </button>` : ''}
          `;
        } else if (!isParticipant) {
          return `<button class="btn btn-primary" data-action="join" data-tournament-id="${tournament._id}">
            <i class="fas fa-sign-in-alt"></i> Join Tournament
          </button>`;
        } else {
          return `<button class="btn btn-secondary" data-action="leave" data-tournament-id="${tournament._id}">
            <i class="fas fa-sign-out-alt"></i> Leave Tournament
          </button>`;
        }
        
      case 'in_progress':
        if (isOrganizer) {
          return `<button class="btn btn-primary" data-action="manage" data-tournament-id="${tournament._id}">
            <i class="fas fa-cogs"></i> Manage Tournament
          </button>`;
        } else if (isParticipant) {
          return `<button class="btn btn-secondary" data-action="view-bracket" data-tournament-id="${tournament._id}">
            <i class="fas fa-sitemap"></i> View Bracket
          </button>`;
        }
        return '';
        
      case 'completed':
        return `<button class="btn btn-primary" data-action="view-results" data-tournament-id="${tournament._id}">
          <i class="fas fa-trophy"></i> View Results
        </button>`;
        
      default:
        return '';
    }
  }

  /**
   * Handle tournament action clicks
   */
  async handleTournamentActions(e) {
    const action = e.target.dataset.action;
    const tournamentId = e.target.dataset.tournamentId;
    
    if (!action || !tournamentId) return;
    
    try {
      switch (action) {
        case 'join':
          await this.joinTournament(tournamentId);
          break;
        case 'leave':
          await this.leaveTournament(tournamentId);
          break;
        case 'start':
          await this.startTournament(tournamentId);
          break;
        case 'manage':
          this.manageTournament(tournamentId);
          break;
        case 'publish':
          await this.publishTournament(tournamentId);
          break;
        case 'view-bracket':
          this.viewBracket(tournamentId);
          break;
        case 'view-results':
          this.viewResults(tournamentId);
          break;
      }
    } catch (error) {
      console.error(`Failed to ${action} tournament:`, error);
      this.showError(`Failed to ${action} tournament: ${error.message}`);
    }
  }

  /**
   * Show create tournament modal
   */
  showCreateTournamentModal() {
    const modal = document.getElementById('create-tournament-modal');
    if (!modal) return;
    
    // Set default dates
    this.setDefaultDates();
    
    modal.style.display = 'block';
  }

  /**
   * Set default dates for tournament form
   */
  setDefaultDates() {
    const now = new Date();
    const startDate = new Date(now);
    startDate.setHours(20, 0, 0, 0); // 8 PM today
    
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 2); // 2 days later
    
    const startInput = document.getElementById('tournament-start-date-input');
    const endInput = document.getElementById('tournament-end-date-input');
    
    if (startInput) startInput.value = startDate.toISOString().slice(0, 16);
    if (endInput) endInput.value = endDate.toISOString().slice(0, 16);
  }

  /**
   * Handle create tournament form submission
   */
  async handleCreateTournament(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const tournamentData = {
      name: formData.get('name') || document.getElementById('tournament-name-input')?.value,
      description: formData.get('description') || document.getElementById('tournament-description-input')?.value,
      type: formData.get('type') || document.getElementById('tournament-type-input')?.value,
      maxParticipants: parseInt(formData.get('maxParticipants') || document.getElementById('tournament-max-participants-input')?.value),
      startDate: formData.get('startDate') || document.getElementById('tournament-start-date-input')?.value,
      endDate: formData.get('endDate') || document.getElementById('tournament-end-date-input')?.value,
      matchType: formData.get('matchType') || document.getElementById('tournament-match-type-input')?.value,
      status: 'registration'
    };

    // Validate
    if (!tournamentData.name?.trim()) {
      this.showError('Tournament name is required');
      return;
    }

    if (new Date(tournamentData.startDate) < new Date()) {
      this.showError('Start date must be in the future');
      return;
    }

    try {
      const response = await fetch('/api/tournaments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(tournamentData),
        credentials: 'include'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create tournament');
      }

      const tournament = await response.json();
      this.showSuccess('Tournament created successfully!');
      
      // Hide modal and redirect to management
      this.hideModal('create-tournament-modal');
      window.location.href = `/views/tournament-manage.html?id=${tournament._id}`;
      
    } catch (error) {
      console.error('Error creating tournament:', error);
      this.showError(`Error creating tournament: ${error.message}`);
    }
  }

  /**
   * Join tournament
   */
  async joinTournament(tournamentId) {
    if (!this.currentUser) {
      this.showError('You must be logged in to join tournaments');
      return;
    }

    // Show player selection modal if user has multiple players
    await this.showPlayerSelectionModal(tournamentId);
  }

  /**
   * Show player selection modal
   */
  async showPlayerSelectionModal(tournamentId) {
    try {
      // Get user's players
      const response = await fetch('/api/ladder/my-players', { credentials: 'include' });
      if (!response.ok) throw new Error('Failed to load players');
      
      const players = await response.json();
      
      if (players.length === 0) {
        this.showError('You need to create a player profile first');
        return;
      }

      if (players.length === 1) {
        // Auto-join with single player
        await this.joinWithPlayer(tournamentId, players[0]._id);
        return;
      }

      // Show selection modal for multiple players
      this.renderPlayerSelection(players, tournamentId);
      
    } catch (error) {
      console.error('Error loading players:', error);
      this.showError('Failed to load player profiles');
    }
  }

  /**
   * Render player selection modal
   */
  renderPlayerSelection(players, tournamentId) {
    const modal = document.getElementById('player-selection-modal');
    const container = modal?.querySelector('.player-selection-container');
    
    if (!modal || !container) return;

    container.innerHTML = players.map(player => `
      <div class="player-card" data-player-id="${player._id}">
        <div class="player-info">
          <h4>${player.name}</h4>
          <p>Level ${player.level || 1} • ${player.rank?.name || 'Unranked'}</p>
        </div>
        <button class="btn btn-primary select-player-btn" 
                data-player-id="${player._id}" 
                data-tournament-id="${tournamentId}">
          Select
        </button>
      </div>
    `).join('');

    // Add event listeners
    container.querySelectorAll('.select-player-btn').forEach(btn => {
      btn.addEventListener('click', async () => {
        const playerId = btn.dataset.playerId;
        await this.joinWithPlayer(tournamentId, playerId);
        this.hideModal('player-selection-modal');
      });
    });

    modal.style.display = 'block';
  }

  /**
   * Join tournament with specific player
   */
  async joinWithPlayer(tournamentId, playerId) {
    try {
      const response = await fetch(`/api/tournaments/${tournamentId}/join`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ playerId }),
        credentials: 'include'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to join tournament');
      }

      this.showSuccess('Successfully joined tournament!');
      await this.loadTournaments(); // Refresh list
      
    } catch (error) {
      console.error('Error joining tournament:', error);
      this.showError(`Failed to join tournament: ${error.message}`);
    }
  }

  /**
   * Leave tournament
   */
  async leaveTournament(tournamentId) {
    if (!confirm('Are you sure you want to leave this tournament?')) return;

    try {
      const response = await fetch(`/api/tournaments/${tournamentId}/leave`, {
        method: 'POST',
        credentials: 'include'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to leave tournament');
      }

      this.showSuccess('Successfully left tournament');
      await this.loadTournaments(); // Refresh list
      
    } catch (error) {
      console.error('Error leaving tournament:', error);
      this.showError(`Failed to leave tournament: ${error.message}`);
    }
  }

  /**
   * Start tournament
   */
  async startTournament(tournamentId) {
    if (!confirm('Are you sure you want to start this tournament?')) return;

    try {
      const response = await fetch(`/api/tournaments/${tournamentId}/start`, {
        method: 'POST',
        credentials: 'include'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to start tournament');
      }

      this.showSuccess('Tournament started successfully!');
      await this.loadTournaments(); // Refresh list
      
    } catch (error) {
      console.error('Error starting tournament:', error);
      this.showError(`Failed to start tournament: ${error.message}`);
    }
  }

  /**
   * Manage tournament
   */
  manageTournament(tournamentId) {
    window.location.href = `/views/tournament-manage.html?id=${tournamentId}`;
  }

  /**
   * Publish tournament
   */
  async publishTournament(tournamentId) {
    try {
      const response = await fetch(`/api/tournaments/${tournamentId}/publish`, {
        method: 'POST',
        credentials: 'include'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to publish tournament');
      }

      this.showSuccess('Tournament published successfully!');
      await this.loadTournaments(); // Refresh list
      
    } catch (error) {
      console.error('Error publishing tournament:', error);
      this.showError(`Failed to publish tournament: ${error.message}`);
    }
  }

  /**
   * View bracket
   */
  viewBracket(tournamentId) {
    // Open bracket in modal or new page
    window.open(`/views/tournament-bracket.html?id=${tournamentId}`, '_blank');
  }

  /**
   * View results
   */
  viewResults(tournamentId) {
    window.location.href = `/views/tournament-results.html?id=${tournamentId}`;
  }

  /**
   * Management Interface Setup
   */
  setupManagementInterface() {
    this.setupTabs();
    this.setupStatusControls();
    this.setupManagementActions();
  }

  /**
   * Setup tabs for management interface
   */
  setupTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        // Remove active from all
        tabButtons.forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        // Activate clicked tab
        button.classList.add('active');
        const tabId = button.dataset.tab + '-tab';
        const tabContent = document.getElementById(tabId);
        if (tabContent) {
          tabContent.classList.add('active');
          
          // Load tab content
          switch (button.dataset.tab) {
            case 'participants':
              this.loadParticipants();
              break;
            case 'matches':
              this.loadMatches();
              break;
            case 'brackets':
              this.loadBrackets();
              break;
          }
        }
      });
    });
  }

  /**
   * Load tournament details for management
   */
  async loadTournamentDetails(tournamentId) {
    try {
      const response = await fetch(`/api/tournaments/${tournamentId}`, { credentials: 'include' });
      if (!response.ok) throw new Error('Failed to load tournament');
      
      this.currentTournament = await response.json();
      console.log('🏆 Tournament loaded:', this.currentTournament.name);
      
      // Update UI
      this.updateTournamentDetails();
      
    } catch (error) {
      console.error('Error loading tournament details:', error);
      this.showError('Failed to load tournament details');
    }
  }

  /**
   * Update tournament details in management UI
   */
  updateTournamentDetails() {
    const tournament = this.currentTournament;
    
    // Update title
    const titleElement = document.querySelector('h1');
    if (titleElement) titleElement.textContent = tournament.name;

    // Update stats
    const participantCount = document.getElementById('participant-count');
    const matchCount = document.getElementById('match-count');
    
    if (participantCount) participantCount.textContent = tournament.participants?.length || 0;
    if (matchCount) {
      const totalMatches = tournament.brackets?.reduce((count, round) => count + round.matches.length, 0) || 0;
      matchCount.textContent = totalMatches;
    }
  }

  /**
   * Utility functions
   */
  isUserOrganizer(tournament) {
    if (!this.currentUser || !tournament.organizer) return false;
    const userId = this.currentUser._id || this.currentUser.id;
    return tournament.organizer.userId === userId;
  }

  isUserParticipant(tournament) {
    if (!this.currentUser || !tournament.participants) return false;
    const userId = this.currentUser._id || this.currentUser.id;
    return tournament.participants.some(p => p.userId === userId);
  }

  getUserParticipantInfo(tournament) {
    if (!this.isUserParticipant(tournament)) return null;
    const userId = this.currentUser._id || this.currentUser.id;
    return tournament.participants.find(p => p.userId === userId);
  }

  showLoading(container, message = 'Loading...') {
    container.innerHTML = `<div class="loading">${message}</div>`;
  }

  showError(message) {
    console.error(message);
    // Use existing notification system if available
    if (window.NotificationUtils) {
      window.NotificationUtils.error(message);
    } else {
      alert(`Error: ${message}`);
    }
  }

  showSuccess(message) {
    console.log(message);
    if (window.NotificationUtils) {
      window.NotificationUtils.success(message);
    } else {
      alert(message);
    }
  }

  hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) modal.style.display = 'none';
  }

  setupModalHandlers() {
    // Close modals when clicking X or outside
    document.addEventListener('click', (e) => {
      // Don't interfere with navbar elements
      if (e.target.closest('#navbar-container') || e.target.closest('.navbar')) {
        return;
      }
      
      if (e.target.classList.contains('close-modal')) {
        const modal = e.target.closest('.modal');
        if (modal) modal.style.display = 'none';
      }
      
      if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
      }
    });

    // Close modals on Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        document.querySelectorAll('.modal[style*="block"]').forEach(modal => {
          modal.style.display = 'none';
        });
      }
    });
  }

  // Placeholder methods for management features
  setupStatusControls() { /* Implementation for status controls */ }
  setupManagementActions() { /* Implementation for management actions */ }
  loadParticipants() { /* Implementation for loading participants */ }
  loadMatches() { /* Implementation for loading matches */ }
  loadBrackets() { /* Implementation for loading brackets */ }
}

// Initialize the system
document.addEventListener('DOMContentLoaded', () => {
  window.tournamentSystem = new ConsolidatedTournamentSystem();
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ConsolidatedTournamentSystem;
} 