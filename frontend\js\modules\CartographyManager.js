/**
 * Cartography Manager
 * Manages the Cartography section on the profile page
 */
class CartographyManager {
  constructor() {
    this.userMaps = [];
    this.isLoading = false;
    this.init();
  }

  init() {
    console.log('🗺️ CartographyManager initialized');
    this.loadUserMaps();
  }

  async loadUserMaps() {
    console.log('📥 Loading user maps...');
    this.isLoading = true;
    this.updateLoadingState();

    try {
      // Use the existing ProfileDataLoader if available
      if (window.profileDataLoader) {
        this.userMaps = await window.profileDataLoader.loadUserMaps();
      } else {
        // Fallback to direct API call
        const response = await fetch('/api/war2maps?limit=1000', {
          credentials: 'include',
          headers: window.getAuthHeaders ? window.getAuthHeaders() : {}
        });

        if (!response.ok) {
          throw new Error('Failed to load user maps');
        }

        const data = await response.json();
        
        // Handle different response formats
        if (data.success && data.data) {
          this.userMaps = data.data;
        } else if (data.maps) {
          this.userMaps = data.maps;
        } else if (Array.isArray(data)) {
          this.userMaps = data;
        } else {
          this.userMaps = [];
        }
      }

      console.log('✅ User maps loaded:', this.userMaps.length);
      this.renderUserMaps();
    } catch (error) {
      console.error('❌ Failed to load user maps:', error);
      this.showError('Failed to load your maps. Please try again.');
    } finally {
      this.isLoading = false;
      this.updateLoadingState();
    }
  }

  updateLoadingState() {
    const container = document.getElementById('user-maps-container');
    const countElement = document.getElementById('user-maps-count');
    
    if (this.isLoading) {
      container.innerHTML = '<div class="loading">Loading your maps...</div>';
      countElement.textContent = 'Loading...';
    }
  }

  renderUserMaps() {
    const container = document.getElementById('user-maps-container');
    const countElement = document.getElementById('user-maps-count');
    
    // Update count
    countElement.textContent = `${this.userMaps.length} maps`;

    if (this.userMaps.length === 0) {
      container.innerHTML = `
        <div class="no-maps-message">
          <i class="fas fa-map"></i>
          <h4>No Maps Yet</h4>
          <p>You haven't uploaded any maps yet. Create your first map and share it with the community!</p>
        </div>
      `;
      return;
    }

    // Create maps grid
    const mapsGrid = document.createElement('div');
    mapsGrid.className = 'user-maps-grid';
    
    this.userMaps.forEach(map => {
      const mapCard = this.createMapCard(map);
      mapsGrid.appendChild(mapCard);
    });

    container.innerHTML = '';
    container.appendChild(mapsGrid);
  }

  createMapCard(map) {
    const card = document.createElement('div');
    card.className = 'user-map-card';
    
    const gameType = this.getGameType(map);
    const gameBadgeClass = gameType.toLowerCase().replace(/[^a-z0-9]/g, '');
    
    const mapId = map._id || map.id;
    
    card.innerHTML = `
      <div class="user-map-header">
        <h4 class="user-map-title">${this.escapeHtml(map.name || 'Unnamed Map')}</h4>
        <span class="user-map-game-badge ${gameBadgeClass}">${gameType}</span>
      </div>
      
      <div class="user-map-stats">
        <div class="user-map-stat">
          <i class="fas fa-download"></i>
          <span>${map.downloads || 0}</span>
        </div>
        <div class="user-map-stat">
          <i class="fas fa-star"></i>
          <span>${map.rating || 0}</span>
        </div>
        <div class="user-map-stat">
          <i class="fas fa-users"></i>
          <span>${map.maxPlayers || 'N/A'}</span>
        </div>
      </div>
      
      ${map.description ? `
        <div class="user-map-description">
          ${this.escapeHtml(map.description)}
        </div>
      ` : ''}
      
      <div class="user-map-actions">
        <a href="/views/maps.html?id=${mapId}" class="user-map-action-btn">
          <i class="fas fa-arrow-right"></i>
          View on Atlas
        </a>
      </div>
    `;
    
    return card;
  }

  getGameType(map) {
    // Determine game type based on map properties
    if (map.game) {
      return map.game.toUpperCase();
    }
    
    // Fallback logic based on map properties
    if (map.tileset) {
      return 'WC2';
    }
    
    if (map.scenario) {
      return 'WC1';
    }
    
    return 'WC2'; // Default
  }



  showError(message) {
    const container = document.getElementById('user-maps-container');
    container.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <p>${message}</p>
      </div>
    `;
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  // Public methods for integration
  refresh() {
    this.loadUserMaps();
  }

  getMapCount() {
    return this.userMaps.length;
  }

  getMaps() {
    return this.userMaps;
  }
}

// Export for use in other modules
window.CartographyManager = CartographyManager;

// Auto-initialize if we're on the profile page
if (document.querySelector('#section-cartography')) {
  console.log('🏗️ Auto-initializing CartographyManager...');
  window.cartographyManager = new CartographyManager();
} 