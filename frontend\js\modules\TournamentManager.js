/**
 * Tournament Management Module
 * Handles tournament creation, management, and administration
 */

export class TournamentManager {
  constructor(apiClient) {
    this.api = apiClient;
    console.log('🏆 TournamentManager initialized');
  }

  /**
   * Load tournament management interface
   */
  async loadTournamentManagement() {
    console.log('🏆 Loading tournament management...');
    
    return `
      <div class="admin-section">
        <div class="section-header">
          <h2><i class="fas fa-crown"></i> Tournament Management</h2>
          <p>Create and manage tournaments</p>
        </div>
        
        <div class="admin-grid">
          <div class="admin-card">
            <h3>Active Tournaments</h3>
            <div id="active-tournaments">
              <p class="text-muted">Loading tournaments...</p>
            </div>
          </div>
          
          <div class="admin-card">
            <h3>Create Tournament</h3>
            <form id="create-tournament-form">
              <div class="form-group">
                <label>Tournament Name</label>
                <input type="text" class="form-control" name="name" required>
              </div>
              <div class="form-group">
                <label>Game Type</label>
                <select class="form-control" name="gameType" required>
                  <option value="">Select Game Type</option>
                  <option value="wc1">Warcraft 1</option>
                  <option value="wc2">Warcraft 2</option>
                  <option value="wc3">Warcraft 3</option>
                </select>
              </div>
              <button type="submit" class="btn btn-primary">Create Tournament</button>
            </form>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Setup tournament management event listeners
   */
  setupEventListeners() {
    console.log('🏆 Setting up tournament event listeners...');
    
    // Create tournament form
    const form = document.getElementById('create-tournament-form');
    if (form) {
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        await this.createTournament(new FormData(form));
      });
    }
  }

  /**
   * Create a new tournament
   */
  async createTournament(formData) {
    try {
      console.log('🏆 Creating tournament...');
      
      const tournamentData = {
        name: formData.get('name'),
        gameType: formData.get('gameType'),
        status: 'upcoming',
        createdAt: new Date().toISOString()
      };
      
      // TODO: Implement API call
      console.log('Tournament data:', tournamentData);
      
      if (window.adminUtils) {
        window.adminUtils.showNotification('Tournament created successfully!', 'success');
      }
      
      // Reset form
      document.getElementById('create-tournament-form').reset();
      
    } catch (error) {
      console.error('Error creating tournament:', error);
      if (window.adminUtils) {
        window.adminUtils.showNotification('Error creating tournament: ' + error.message, 'error');
      }
    }
  }

  /**
   * Load tournaments list
   */
  async loadTournaments() {
    try {
      console.log('🏆 Loading tournaments...');
      
      // TODO: Implement API call
      const tournaments = [
        { id: 1, name: 'Sample Tournament', gameType: 'wc2', status: 'active', participants: 8 }
      ];
      
      const container = document.getElementById('active-tournaments');
      if (container) {
        container.innerHTML = tournaments.length > 0 
          ? tournaments.map(t => `
              <div class="tournament-item">
                <strong>${t.name}</strong>
                <span class="badge badge-${t.status}">${t.status}</span>
                <small>${t.participants} participants</small>
              </div>
            `).join('')
          : '<p class="text-muted">No active tournaments</p>';
      }
      
    } catch (error) {
      console.error('Error loading tournaments:', error);
    }
  }
}

export default TournamentManager;
