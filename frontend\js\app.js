/**
 * Optimized Application Core
 * Single source of truth for application state and initialization
 */

class Application {
  constructor() {
    if (window._appInstance) {
      console.warn('⚠️ Application already instantiated, returning existing instance');
      return window._appInstance;
    }
    window._appInstance = this;

    // Use WeakMap for private state
    this._state = new WeakMap();
    this._state.set(this, {
      isInitialized: false,
      isElectron: this._detectElectron(),
      loadStartTime: performance.now(),
      components: new Map(),
      initQueue: new Set(),
      deferredModules: new Set()
    });

    // Performance monitoring
    this._metrics = {
      initTime: 0,
      componentLoadTimes: new Map(),
      resourceLoadTimes: new Map()
    };
  }

  async init() {
    const state = this._state.get(this);
    if (state.isInitialized) {
      console.warn('⚠️ Application already initialized');
      return;
    }

    console.log('🚀 Initializing Application...');
    performance.mark('app-init-start');

    try {
      // Load ApiClient first and make it globally available
      await this._loadApiClientFirst();
      
      // Initialize core features in parallel
      await Promise.all([
        this._initCriticalFeatures(),
        this._initDeferredFeatures()
      ]);

      // Mark initialization complete
      state.isInitialized = true;
      performance.mark('app-init-end');
      performance.measure('app-initialization', 'app-init-start', 'app-init-end');
      
      this._metrics.initTime = performance.now() - state.loadStartTime;
      console.log(`✅ Application initialized in ${this._metrics.initTime.toFixed(2)}ms`);

      // Emit ready event
      window.dispatchEvent(new CustomEvent('app:ready', { 
        detail: { metrics: this._metrics } 
      }));

    } catch (error) {
      console.error('❌ Application initialization failed:', error);
      this._handleInitError(error);
    }
  }

  async _loadApiClientFirst() {
    console.log('🔐 Loading ApiClient first...');
    const startTime = performance.now();
    
    try {
      const module = await import('./modules/ApiClient.js');
      
      // ApiClient exports a singleton instance as default
      const apiClient = module.default;
      
      // Make it globally available immediately
      window.apiClient = apiClient;
      window.ApiClient = module.ApiClient; // Export the class too
      
      const loadTime = performance.now() - startTime;
      console.log(`✅ ApiClient loaded and made globally available in ${loadTime.toFixed(2)}ms`);
      
      // Store in components map
      this._state.get(this).components.set('api', apiClient);
      
    } catch (error) {
      console.error('❌ Failed to load ApiClient:', error);
      throw error;
    }
  }

  async _initCriticalFeatures() {
    const state = this._state.get(this);
    
    // Critical features that must load first (ApiClient is already loaded)
    const criticalModules = [
      ['auth', () => import('./modules/AuthManager.js')],
      ['ui', () => import('./modules/UIManager.js')]
    ];

    // Load critical modules in parallel
    const moduleLoads = criticalModules.map(async ([name, importFn]) => {
      const startTime = performance.now();
      try {
        const module = await importFn();
        
        // Handle different module export patterns
        let instance;
        if (module.default && typeof module.default === 'function') {
          // Module exports a class constructor
          instance = new module.default();
        } else if (module.default && typeof module.default === 'object' && !module.default.prototype) {
          // Module exports a singleton instance
          instance = module.default;
        } else if (module[name] && typeof module[name] === 'object') {
          // Module exports a named singleton (e.g., module.authManager)
          instance = module[name];
        } else {
          // Try to find any exported class or instance
          const exportedKeys = Object.keys(module);
          const classKey = exportedKeys.find(key => 
            typeof module[key] === 'function' && 
            module[key].prototype && 
            module[key].prototype.constructor
          );
          
          if (classKey) {
            instance = new module[classKey]();
          } else {
            throw new Error(`No suitable export found in ${name} module`);
          }
        }
        
        state.components.set(name, instance);
        
        const loadTime = performance.now() - startTime;
        this._metrics.componentLoadTimes.set(name, loadTime);
        
        console.log(`✅ Loaded ${name} in ${loadTime.toFixed(2)}ms`);
      } catch (error) {
        console.error(`❌ Failed to load ${name}:`, error);
        throw error;
      }
    });

    await Promise.all(moduleLoads);
  }

  async _initDeferredFeatures() {
    const state = this._state.get(this);
    
    // Features that can load after critical ones
    const deferredModules = [
      ['achievements', './core/AchievementEngine.js'],
      ['chat', './modules/ChatManager.js'],
      ['maps', './modules/MapsCore.js'],
      ['profile', './modules/ProfileManager.js']
    ];

    // Queue deferred modules for lazy loading
    deferredModules.forEach(([name, path]) => {
      state.deferredModules.add({ name, path });
    });

    // Start loading first deferred module
    this._loadNextDeferredModule();
  }

  async _loadNextDeferredModule() {
    const state = this._state.get(this);
    const nextModule = state.deferredModules.values().next().value;
    
    if (!nextModule) return;

    const startTime = performance.now();
    try {
      const module = await import(nextModule.path);
      
      // Handle different module export patterns
      let instance;
      if (nextModule.name === 'achievements') {
        // AchievementEngine exports a singleton instance
        instance = module.achievementEngine;
      } else if (nextModule.name === 'chat') {
        // ChatManager exports a singleton instance
        instance = module.chatManager;
      } else if (nextModule.name === 'maps') {
        // MapsCore exports a singleton instance
        instance = module.mapsCore;
      } else if (nextModule.name === 'profile') {
        // ProfileManager exports a singleton instance
        instance = module.profileManager;
      } else if (module.default && typeof module.default === 'function') {
        // Module exports a class constructor
        instance = new module.default();
      } else if (module.default && typeof module.default === 'object' && !module.default.prototype) {
        // Module exports a singleton instance
        instance = module.default;
      } else {
        // Try to find any exported class or instance
        const exportedKeys = Object.keys(module);
        const classKey = exportedKeys.find(key => 
          typeof module[key] === 'function' && 
          module[key].prototype && 
          module[key].prototype.constructor
        );
        
        if (classKey) {
          instance = new module[classKey]();
        } else {
          // Try to find a named export that matches the module name
          const namedKey = exportedKeys.find(key => 
            key.toLowerCase().includes(nextModule.name.toLowerCase())
          );
          
          if (namedKey && typeof module[namedKey] === 'object') {
            instance = module[namedKey];
          } else {
            throw new Error(`No suitable export found in ${nextModule.name} module`);
          }
        }
      }
      
      state.components.set(nextModule.name, instance);
      
      const loadTime = performance.now() - startTime;
      this._metrics.componentLoadTimes.set(nextModule.name, loadTime);
      
      state.deferredModules.delete(nextModule);
      console.log(`✅ Loaded ${nextModule.name} in ${loadTime.toFixed(2)}ms`);
      
      // Load next module
      if (state.deferredModules.size > 0) {
        setTimeout(() => this._loadNextDeferredModule(), 100);
      }
    } catch (error) {
      console.error(`❌ Failed to load ${nextModule.name}:`, error);
      state.deferredModules.delete(nextModule);
      
      // Continue with next module even if this one fails
      if (state.deferredModules.size > 0) {
        setTimeout(() => this._loadNextDeferredModule(), 100);
      }
    }
  }

  _detectElectron() {
    return window.electronAPI !== undefined;
  }

  _handleInitError(error) {
    // Log error details
    console.error('Application initialization failed:', {
      error,
      metrics: this._metrics,
      state: this._state.get(this)
    });

    // Show user-friendly error
    const errorContainer = document.createElement('div');
    errorContainer.className = 'app-error';
    errorContainer.innerHTML = `
      <h2>Application Error</h2>
      <p>Failed to initialize the application. Please try refreshing the page.</p>
      ${error.message ? `<pre>${error.message}</pre>` : ''}
    `;
    document.body.appendChild(errorContainer);
  }

  // Public API for accessing components
  getComponent(name) {
    return this._state.get(this).components.get(name);
  }

  // Clean up resources
  destroy() {
    const state = this._state.get(this);
    state.components.forEach(component => {
      if (typeof component.destroy === 'function') {
        component.destroy();
      }
    });
    state.components.clear();
    state.deferredModules.clear();
    this._metrics.componentLoadTimes.clear();
    this._metrics.resourceLoadTimes.clear();
    window._appInstance = null;
  }
}

// Create global application instance
const app = new Application();

// Auto-initialize when script loads
app.init().catch(error => {
  console.error('💥 Application failed to start:', error);
});

// Make app available globally for non-module scripts
window._appInstance = app; 