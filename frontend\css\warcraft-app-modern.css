/* 
 * WC Arena - MODERN APP DESIGN SYSTEM
 * A comprehensive design system for a modern Warcraft-themed gaming platform
 */

/* ===== DESIGN TOKENS ===== */
:root {
  /* Brand Colors */
  --primary-gold: #D4AF37;
  --primary-gold-dark: #B8941F;
  --primary-gold-light: #E8C547;
  
  --alliance-blue: #2563EB;
  --alliance-blue-dark: #1D4ED8;
  --alliance-blue-light: #3B82F6;
  
  --horde-red: #DC2626;
  --horde-red-dark: #B91C1C;
  --horde-red-light: #EF4444;
  
  /* Neutral Colors */
  --neutral-50: #F8FAFC;
  --neutral-100: #F1F5F9;
  --neutral-200: #E2E8F0;
  --neutral-300: #CBD5E1;
  --neutral-400: #94A3B8;
  --neutral-500: #64748B;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1E293B;
  --neutral-900: #0F172A;
  
  /* App Background Gradients */
  --bg-primary: linear-gradient(135deg, #0F172A 0%, #1E293B 50%, #334155 100%);
  --bg-secondary: linear-gradient(135deg, #1E293B 0%, #334155 100%);
  --bg-card: rgba(15, 23, 42, 0.8);
  --bg-card-hover: rgba(30, 41, 59, 0.9);
  
  /* Glass Effect */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  
  /* Typography */
  --font-primary: 'Inter', 'Segoe UI', system-ui, sans-serif;
  --font-display: 'Cinzel', 'Inter', serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(212, 175, 55, 0.3);
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* ===== COMPATIBILITY ALIASES FOR LEGACY CSS ===== */
  /* Primary color aliases */
  --primary-color: var(--primary-gold);
  --primary-hover: var(--primary-gold-light);
  
  /* Spacing aliases (tournaments.css expects these) */
  --spacing-sm: var(--space-2);      /* 0.5rem */
  --spacing-md: var(--space-4);      /* 1rem */
  --spacing-lg: var(--space-6);      /* 1.5rem */
  --spacing-xl: var(--space-8);      /* 2rem */
  
  /* Background aliases */
  --bg-hover: var(--bg-card-hover);
  
  /* Border aliases */
  --border-primary: var(--glass-border);
  --border-hover: var(--primary-gold);
  
  /* Text color aliases */
  --text-primary: var(--neutral-100);
  --text-secondary: var(--neutral-400);
}

/* ===== GLOBAL RESET & BASE ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  background: var(--bg-primary);
  color: var(--neutral-100);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  padding-top: 0;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* ===== TYPOGRAPHY SYSTEM ===== */
.text-display {
  font-family: var(--font-display);
  font-weight: 700;
  letter-spacing: -0.025em;
}

.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }

.text-primary { color: var(--primary-gold); }
.text-alliance { color: var(--alliance-blue); }
.text-horde { color: var(--horde-red); }
.text-muted { color: var(--neutral-400); }

/* ===== LAYOUT SYSTEM ===== */
.app-container {
  min-height: 100vh;
  background: var(--bg-primary);
}

.app-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  padding: var(--space-4) 0;
}

.app-main {
  flex: 1;
  padding: var(--space-6) var(--space-4);
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.app-sidebar {
  width: 280px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--glass-border);
  position: sticky;
  top: 0;
  height: 100vh;
  overflow-y: auto;
  padding: var(--space-6);
}

.app-content {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
}

/* ===== GLASS CARD SYSTEM ===== */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--glass-shadow);
  padding: var(--space-6);
  transition: all var(--transition-normal);
}

.glass-card:hover {
  background: var(--glass-border);
  border-color: var(--primary-gold);
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.glass-card-sm {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
}

.glass-card-lg {
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
}

/* ===== NAVIGATION SYSTEM ===== */
/* Main navbar container */
.navbar-modern {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 70px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: all var(--transition-normal);
}

/* Improved focus styles for accessibility */
.navbar-modern *:focus {
  outline: 2px solid var(--primary-gold);
  outline-offset: 2px;
}

.navbar-modern *:focus:not(:focus-visible) {
  outline: none;
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

/* Brand section */
.navbar-brand {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
  color: var(--primary-gold);
  font-family: var(--font-display);
  font-size: var(--text-xl);
  font-weight: 700;
  transition: all var(--transition-fast);
  border-radius: var(--radius-md);
  padding: var(--space-2);
}

.brand-link:hover, .brand-link:focus {
  color: var(--primary-gold-light);
  text-shadow: 0 0 10px var(--primary-gold);
  transform: translateY(-1px);
}

.brand-logo {
  height: 40px;
  width: auto;
  transition: all var(--transition-fast);
}

.brand-link:hover .brand-logo, .brand-link:focus .brand-logo {
  filter: brightness(1.2);
  transform: rotate(10deg);
}

.brand-text {
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Desktop navigation */
.navbar-nav-desktop {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.nav-primary {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  color: var(--neutral-300);
  text-decoration: none;
  font-weight: 600;
  font-size: var(--text-sm);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  position: relative;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  will-change: transform, background-color, border-color; /* Optimize animations */
}

.nav-item::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-gold), var(--primary-gold-light));
  transition: all var(--transition-normal);
  transform: translateX(-50%);
}

.nav-item:hover, .nav-item:focus {
  color: var(--primary-gold);
  background: rgba(212, 175, 55, 0.1);
  transform: translateY(-1px);
  border-color: var(--primary-gold);
  box-shadow: 0 4px 20px rgba(212, 175, 55, 0.2);
}

.nav-item:hover::before, .nav-item:focus::before {
  width: 80%;
}

.nav-item.active {
  color: var(--primary-gold);
  background: rgba(212, 175, 55, 0.15);
  border-color: var(--primary-gold);
}

.nav-item.active::before {
  width: 100%;
}

.nav-item i {
  font-size: var(--text-lg);
  transition: all var(--transition-fast);
}

.nav-item:hover i, .nav-item:focus i {
  transform: scale(1.1);
  filter: drop-shadow(0 0 4px var(--primary-gold));
}

/* Controls section */
.navbar-controls {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  flex-shrink: 0;
}

/* Support section */
.support-section {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.support-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  color: var(--neutral-400);
  text-decoration: none;
  transition: all var(--transition-fast);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.support-btn:hover, .support-btn:focus {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  border-color: var(--primary-gold);
}

.support-btn.patreon:hover, .support-btn.patreon:focus {
  color: #ff424d;
  background: rgba(255, 66, 77, 0.1);
}

.support-btn.paypal:hover, .support-btn.paypal:focus {
  color: #00457c;
  background: rgba(0, 69, 124, 0.1);
}

.support-btn.coinbase:hover, .support-btn.coinbase:focus {
  color: var(--coinbase-color);
  background: rgba(0, 82, 255, 0.1);
}

/* Music control */
.music-control {
  display: flex;
  align-items: center;
}

.music-toggle-modern {
  display: flex;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
  padding: var(--space-1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  gap: var(--space-1);
}

.music-toggle-modern input[type="radio"] {
  display: none;
}

.music-toggle-modern label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  color: var(--neutral-400);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.music-toggle-modern label:hover, .music-toggle-modern label:focus {
  color: var(--primary-gold);
  background: rgba(212, 175, 55, 0.1);
}

.music-toggle-modern input[type="radio"]:checked + label {
  color: var(--primary-gold);
  background: rgba(212, 175, 55, 0.2);
  box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
}

/* User profile */
.user-profile-modern {
  display: flex;
  align-items: center;
}

.profile-dropdown {
  position: relative;
}

.profile-dropdown-toggle {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-2) var(--space-3);
  color: var(--neutral-100);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.profile-dropdown-toggle:hover, .profile-dropdown-toggle:focus {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--primary-gold);
  transform: translateY(-1px);
}

/* Navbar specific profile avatar - higher specificity */
.navbar-modern .profile-avatar {
  position: relative;
  width: 64px;  /* Increased from 42px to 64px */
  height: 64px; /* Increased from 42px to 64px */
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--primary-gold);
}

.navbar-modern .profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Remove the profile status circle completely */
.navbar-modern .profile-status {
  display: none;
}

/* Notification bell positioned at bottom right of avatar */
.navbar-modern .avatar-notifications {
  position: absolute;
  bottom: -8px;
  left: 56px; /* Position at right edge of 64px avatar (64px - 8px for overlap) */
  z-index: 10;
  pointer-events: none; /* Allow clicks to pass through the container */
}

.navbar-modern .notification-bell-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.9), rgba(255, 215, 0, 0.7));
  border: 2px solid var(--neutral-900);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  pointer-events: auto; /* Re-enable clicks for the button itself */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.navbar-modern .notification-bell-avatar:hover {
  background: linear-gradient(135deg, rgba(255, 215, 0, 1), rgba(255, 215, 0, 0.8));
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.navbar-modern .notification-bell-avatar i {
  font-size: 14px;
}

.navbar-modern .notification-bell-avatar .notification-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: var(--horde-red);
  color: white;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--neutral-900);
}

.profile-info {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.profile-title {
  font-weight: 700;
  font-size: 22px; /* Direct pixel value - 75% bigger than 16px base */
  color: var(--primary-gold);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.profile-username {
  font-weight: 500;
  font-size: calc(var(--text-xs) * 0.8); /* Decreased from 0.9 to 0.8 */
  color: var(--neutral-300);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: -2px;
}

.profile-name {
  font-weight: 600;
  font-size: calc(var(--text-base) * 1.5); /* Made 50% larger from base size */
  color: var(--neutral-100);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.profile-role {
  font-size: var(--text-xs);
  color: var(--neutral-400);
}

.profile-chevron {
  font-size: var(--text-xs);
  transition: transform var(--transition-fast);
}

.profile-dropdown.active .profile-chevron {
  transform: rotate(180deg);
}

/* Dropdown menu - Enhanced styling */
.profile-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  min-width: 200px;
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(0, 0, 0, 0.2);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-normal);
  overflow: hidden;
  z-index: 1001;
}

.profile-dropdown.active .profile-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.nav-dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  color: var(--neutral-300);
  text-decoration: none;
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
  border-left: 3px solid transparent;
  cursor: pointer;
}

.nav-dropdown-item:last-child {
  border-bottom: none;
}

.nav-dropdown-item:hover, .nav-dropdown-item:focus {
  color: var(--primary-gold);
  background: rgba(212, 175, 55, 0.1);
  border-left-color: var(--primary-gold);
}

.nav-dropdown-item i {
  width: 16px;
  text-align: center;
  font-size: var(--text-sm);
}

.dropdown-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0; /* Remove extra margin to ensure consistent spacing */
}

/* Admin link */
.admin-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  border-radius: 50%;
  color: var(--horde-red);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.admin-link:hover, .admin-link:focus {
  background: rgba(220, 38, 38, 0.2);
  border-color: var(--horde-red);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* Mobile Menu Toggle Animations */
.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
  position: fixed;
  top: 70px;
  right: -400px;
  width: 350px;
  height: calc(100vh - 70px);
  background: rgba(46, 52, 64, 0.95);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(136, 192, 208, 0.2);
  overflow-y: auto;
  transition: all 0.3s ease;
  z-index: 999;
}

.mobile-nav.active {
  right: 0;
}

.mobile-nav-overlay {
  position: fixed;
  top: 70px;
  left: 0;
  width: 100vw;
  height: calc(100vh - 70px);
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 998;
}

.mobile-nav-overlay.active {
  opacity: 1;
  visibility: visible;
}

.mobile-nav-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-profile {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mobile-nav .mobile-profile img {
  width: 60px;  /* 3x larger than 20px */
  height: 60px; /* 3x larger than 20px */
  border-radius: 50%;
  border: 2px solid rgba(255, 215, 0, 0.3); /* Increased border back to 2px for better visibility */
}

.mobile-profile-info {
  display: flex;
  flex-direction: column;
}

.mobile-profile-title {
  font-weight: 700;
  font-size: calc(var(--text-base) * 1.1);
  color: var(--primary-gold);
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  margin-bottom: 2px;
}

.mobile-profile-username {
  font-weight: 500;
  font-size: var(--text-xs);
  color: var(--neutral-300);
}

.mobile-profile-name {
  font-weight: 600;
  color: #eceff4;
}

.mobile-nav-content {
  padding: 1rem 0;
}

.mobile-nav-section {
  margin-bottom: 2rem;
}

.mobile-nav-title {
  padding: 0 1.5rem 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #ffd700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  color: #d8dee9;
  text-decoration: none;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.mobile-nav-item:hover {
  color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
  border-left: 3px solid #ffd700;
  text-decoration: none;
}

.mobile-nav-item i {
  width: 20px;
  text-align: center;
}

/* Body class for mobile menu */
.mobile-menu-open {
  overflow: hidden;
}

/* Responsive Design Updates */
/* All responsive media query rules removed for simplified approach - Phase 3 CSS optimization */

/* =============================================================================
   GAME TABS SYSTEM
   ============================================================================= */

/* Game Tabs Container */
.game-tabs {
  display: flex;
  justify-content: center;
  margin: var(--space-6) auto var(--space-8);
  max-width: 600px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  overflow: hidden;
  padding: var(--space-1);
  gap: var(--space-1);
}

/* Individual Game Tab */
.game-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  color: var(--neutral-400);
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
}

.game-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left var(--transition-slow);
}

.game-tab:hover::before {
  left: 100%;
}

.game-tab:hover {
  color: var(--neutral-200);
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-1px);
}

.game-tab.active {
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-dark));
  color: var(--neutral-900);
  box-shadow: 
    0 4px 12px rgba(212, 175, 55, 0.3),
    0 0 20px rgba(212, 175, 55, 0.2);
  transform: translateY(-2px);
}

.game-tab.active:hover {
  background: linear-gradient(135deg, var(--primary-gold-light), var(--primary-gold));
  transform: translateY(-2px);
}

.game-tab i {
  font-size: var(--text-lg);
  transition: all var(--transition-fast);
}

.game-tab:hover i {
  transform: scale(1.1);
}

.game-tab.active i {
  filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.3));
}

.game-tab span {
  font-family: var(--font-display);
  letter-spacing: 0.5px;
}

/* Game-specific styling */
.game-tab[data-game="war1"] i {
  color: #8B4513; /* Saddle Brown for War1 */
}

.game-tab[data-game="war2"] i {
  color: var(--alliance-blue); /* Alliance Blue for War2 */
}

.game-tab[data-game="war3"] i {
  color: var(--horde-red); /* Horde Red for War3 */
}

.game-tab.active[data-game="war1"] i,
.game-tab.active[data-game="war2"] i,
.game-tab.active[data-game="war3"] i {
  color: var(--neutral-900); /* Dark text when active */
}

/* All responsive game tab rules removed for simplified approach - Phase 3 CSS optimization */
/* Animation for tab switching */
@keyframes tabSwitch {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.game-tab.active {
  animation: tabSwitch 0.3s ease-out;
}

/* =============================================================================
   END GAME TABS SYSTEM
   ============================================================================= */

/* Admin Link */
.admin-link:hover {
  background: rgba(220, 38, 38, 0.2);
  border-color: #EF4444;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
  text-decoration: none;
}

/* ===== MAIN CONTENT STYLING ===== */
main {
  min-height: calc(100vh - 140px);
  margin-top: 80px;
  padding: 0 var(--space-4) var(--space-6) var(--space-4);
  background: transparent;
  position: relative;
}

main::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(220, 38, 38, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* =============================================================================
   ENHANCED MATCH HISTORY SYSTEM
   ============================================================================= */

/* Enhanced Match Edit Modal */
.edit-notice {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 8px;
  padding: 12px;
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #ffc107;
}

.edit-notice i {
  font-size: 16px;
}

.edit-match-modal .modal-content,
.dispute-match-modal .modal-content {
  min-width: 600px;
  max-width: 800px;
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.95) 0%, 
    rgba(30, 41, 59, 0.95) 50%, 
    rgba(15, 23, 42, 0.95) 100%);
  border: 2px solid rgba(212, 175, 55, 0.3);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 0 40px rgba(212, 175, 55, 0.2);
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-light);
  font-family: 'Cinzel', serif;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(75, 85, 99, 0.6);
  border-radius: 6px;
  background: rgba(30, 41, 59, 0.8);
  color: var(--text-light);
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
  background: rgba(30, 41, 59, 0.9);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-help {
  font-size: 12px;
  color: var(--neutral-400);
  margin-top: 4px;
  font-style: italic;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid rgba(75, 85, 99, 0.3);
}

/* Match Action Buttons */
.match-actions {
  display: flex;
  gap: var(--space-2);
  margin-left: var(--space-4);
}

.btn-action {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--glass-bg);
  color: var(--neutral-400);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--glass-border);
}

.btn-action:hover {
  background: var(--bg-card-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-screenshot:hover {
  color: var(--alliance-blue);
  border-color: var(--alliance-blue);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-edit:hover {
  color: var(--primary-gold);
  border-color: var(--primary-gold);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.btn-dispute:hover {
  color: var(--horde-red);
  border-color: var(--horde-red);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* Enhanced No Data State */
.no-data {
  text-align: center;
  padding: var(--space-8);
  color: var(--neutral-400);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
}

.no-data i {
  font-size: 3rem;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.no-data p {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-lg);
}

.no-data .sub-text {
  font-size: var(--text-sm);
  opacity: 0.7;
}

/* Screenshot Modal */
.screenshot-modal .modal-content {
  max-width: 800px;
  width: 95%;
}

.screenshot-container {
  text-align: center;
  position: relative;
  min-height: 300px;
  background: var(--glass-bg);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  border: 1px solid var(--glass-border);
}

.loading-screenshot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--neutral-400);
}

.loading-screenshot .spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--glass-border);
  border-top: 3px solid var(--primary-gold);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-3);
}

.no-screenshot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--neutral-400);
}

.no-screenshot i {
  font-size: 3rem;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

/* Dispute Modal Specific */
.dispute-warning {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-6);
  display: flex;
  gap: var(--space-3);
  align-items: flex-start;
}

.dispute-warning i {
  color: #F59E0B;
  font-size: var(--text-lg);
  margin-top: 2px;
  flex-shrink: 0;
}

.dispute-warning p {
  margin: 0;
  color: var(--neutral-200);
  font-size: var(--text-sm);
  line-height: 1.5;
}

/* Match ID Display */
.match-id {
  font-size: var(--text-sm);
  color: var(--neutral-400);
  font-family: monospace;
  background: var(--glass-bg);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--glass-border);
}

/* Enhanced Chart Container Fixes */
.chart-container {
  position: relative;
  background: rgba(17, 24, 39, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  min-height: 300px;
}

.race-chart-container {
  width: 100%;
  height: 300px;
  position: relative;
  background: rgba(17, 24, 39, 0.6);
  border: 1px solid rgba(75, 85, 99, 0.2);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.race-chart-container h4 {
  text-align: center;
  color: var(--text-light);
  font-family: 'Cinzel', serif;
  margin-bottom: 15px;
  font-size: 16px;
}

.race-chart-container canvas {
  max-width: 100%;
  max-height: 250px;
  height: auto;
  display: block;
}

/* Resource Chart Specific Fixes */
#resourceDistributionChart {
  max-width: 100%;
  max-height: 250px;
  height: auto;
  display: block;
}

/* Chart Canvas Visibility Fix */
canvas {
  display: block;
  visibility: visible;
  opacity: 1;
}

/* Charts Grid Layout */
.races-stats-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.stats-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-top: 20px;
}

.stats-column h4 {
  color: var(--primary-gold);
  font-family: 'Cinzel', serif;
  margin-bottom: 15px;
  border-bottom: 2px solid rgba(212, 175, 55, 0.3);
  padding-bottom: 8px;
}

.stats-table table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(17, 24, 39, 0.6);
  border-radius: 8px;
  overflow: hidden;
}

.stats-table th,
.stats-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
}

.stats-table th {
  background: rgba(75, 85, 99, 0.3);
  color: var(--primary-gold);
  font-family: 'Cinzel', serif;
  font-weight: 600;
}

.stats-table td {
  color: var(--text-light);
}

.stats-table tr:last-child td {
  border-bottom: none;
}

.stats-table tr:hover {
  background: rgba(75, 85, 99, 0.2);
}

/* Map Resource Combinations */
.map-resource-list {
  background: rgba(17, 24, 39, 0.6);
  border-radius: 8px;
  padding: 20px;
}

.map-resource-list h4 {
  color: var(--primary-gold);
  font-family: 'Cinzel', serif;
  margin-bottom: 15px;
}

/* Notification System */
.notification {
  position: fixed;
  top: var(--space-6);
  right: var(--space-6);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  z-index: 11000;
  min-width: 300px;
  max-width: 500px;
  box-shadow: var(--shadow-xl);
  animation: slideInRight 0.3s ease-out;
}

.notification.success {
  border-color: #10B981;
  background: rgba(16, 185, 129, 0.1);
}

.notification.error {
  border-color: var(--horde-red);
  background: rgba(220, 38, 38, 0.1);
}

.notification.info {
  border-color: var(--alliance-blue);
  background: rgba(37, 99, 235, 0.1);
}

.notification i {
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.notification.success i {
  color: #10B981;
}

.notification.error i {
  color: var(--horde-red);
}

.notification.info i {
  color: var(--alliance-blue);
}

.notification span {
  flex: 1;
  color: var(--neutral-100);
  font-size: var(--text-sm);
}

.notification button {
  background: none;
  border: none;
  color: var(--neutral-400);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.notification button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--neutral-100);
}

/* Responsive Design for Charts and Forms */
/* Media query removed - Phase 3 CSS optimization */
  
  .stats-columns {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .edit-match-modal .modal-content,
  .dispute-match-modal .modal-content {
    min-width: auto;
    max-width: 95vw;
    margin: 10px;
  }
  
  /* .form-actions column-reverse removed - handled by specific components */
  
  .race-chart-container {
    height: 250px;
  }
  
  .match-actions {
    margin-left: 0;
    justify-content: center;
    width: 100%;
  }
  
  .notification {
    right: var(--space-4);
    left: var(--space-4);
    max-width: none;
}

/* Media query removed - Phase 3 CSS optimization */
  
  .race-chart-container {
    padding: 10px;
    height: 200px;
  }
  
  .race-chart-container canvas {
    max-height: 180px;
  }
  
  .stats-table th,
  .stats-table td {
    padding: 8px;
    font-size: 14px;
  }
  
  .screenshot-modal .modal-content,
  .edit-match-modal .modal-content,
  .dispute-match-modal .modal-content {
    width: 98%;
    margin: var(--space-2);
}

/* =============================================================================
   END ENHANCED MATCH HISTORY SYSTEM
   ============================================================================= */ 

/* ===== UNIVERSAL MODAL SYSTEM FIXES - HIGHEST SPECIFICITY ===== */

/* Force all modals to follow the same pattern */
.modal, 
div.modal,
#report-match-modal,
.epic-modal,
[id*="modal"] {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 10000;
  display: none;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

/* Active modal states */
.modal.show,
.modal.active,
div.modal.show,
div.modal.active,
#report-match-modal.show,
.epic-modal.show,
[id*="modal"].show {
  display: flex;
  opacity: 1;
  visibility: visible;
}

/* Modal content containers */
.modal-content,
.modal .modal-content,
div.modal .modal-content,
#report-match-modal .modal-content {
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.98), rgba(40, 40, 40, 0.95));
  border: 2px solid var(--primary-gold);
  border-radius: var(--radius-xl);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.8),
    0 0 40px rgba(212, 175, 55, 0.3);
  backdrop-filter: blur(20px);
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  margin: auto;
  padding: 2rem;
  color: var(--neutral-100);
}

/* Modal headers */
.modal-header,
.modal .modal-header,
div.modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2,
.modal-header h3,
.modal .modal-header h2,
.modal .modal-header h3 {
  color: var(--primary-gold);
  font-family: var(--font-display);
  font-size: 1.5rem;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Universal close buttons */
.close-modal,
.modal-close,
.close,
.modal .close-modal,
.modal .modal-close,
div.modal .close-modal,
div.modal .modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(220, 38, 38, 0.2);
  border: 2px solid rgba(220, 38, 38, 0.4);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc2626;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10001;
  backdrop-filter: blur(10px);
  text-shadow: none;
  line-height: 1;
  padding: 0;
  margin: 0;
}

.close-modal:hover,
.modal-close:hover,
.close:hover,
.modal .close-modal:hover,
.modal .modal-close:hover {
  background: rgba(220, 38, 38, 0.4);
  border-color: rgba(220, 38, 38, 0.6);
  color: #ffffff;
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.5);
}

/* Notification bell dropdown - HIGHEST SPECIFICITY */
body .navbar-modern .avatar-notifications .notifications-dropdown,
body .navbar-modern .notifications-dropdown,
body .notifications-dropdown {
  display: none;
  position: absolute;
  top: calc(100% + 12px);
  right: 0;
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.98), rgba(40, 40, 40, 0.95));
  border: 2px solid var(--primary-gold);
  border-radius: var(--radius-lg);
  min-width: 320px;
  max-width: 400px;
  max-height: 450px;
  overflow-y: auto;
  z-index: 10003;
  box-shadow: 
    0 10px 40px rgba(0, 0, 0, 0.8),
    0 0 30px rgba(212, 175, 55, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  animation: slideDownFade 0.4s ease-out;
  transform-origin: top right;
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  margin: 0;
  padding: 0;
}

body .navbar-modern .avatar-notifications .notifications-dropdown.show,
body .navbar-modern .notifications-dropdown.show,
body .notifications-dropdown.show {
  display: block;
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

/* Report match modal - MAXIMUM specificity */
body div#report-match-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

body div#report-match-modal.show {
  display: flex;
  opacity: 1;
  visibility: visible;
  align-items: center;
  justify-content: center;
}

body div#report-match-modal .modal-content {
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.98), rgba(40, 40, 40, 0.95));
  border: 2px solid var(--primary-gold);
  border-radius: var(--radius-lg);
  padding: 2rem;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.5),
    0 0 20px rgba(212, 175, 55, 0.3);
  backdrop-filter: blur(10px);
  margin: auto;
}

/* Leaderboard player modals */
.player-details-modal,
[id*="player-details"],
[id*="player-modal"] {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 10000;
  display: none;
  align-items: center;
  justify-content: center;
}

.player-details-modal.show,
[id*="player-details"].show,
[id*="player-modal"].show {
  display: flex;
}

/* Force body scroll lock when modal is open */
body.modal-open {
  overflow: hidden;
}

/* ===== FOOTER STYLING ===== */
.footer {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--glass-border);
  padding: var(--space-6) 0;
  text-align: center;
  margin-top: auto;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--primary-gold) 50%, 
    transparent 100%);
  box-shadow: 0 0 10px var(--primary-gold);
}

.footer p {
  color: var(--neutral-400);
  font-size: var(--text-sm);
  margin: 0;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* Ensure body has proper flex layout for sticky footer */
body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content,
.app-main {
  flex: 1;
}

/* ===== SPARKLING LOGO EFFECTS ===== */
.logo-sparkle {
  position: relative;
  display: inline-block;
}

.logo-sparkle::before,
.logo-sparkle::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--primary-gold);
  border-radius: 50%;
  box-shadow: 0 0 6px var(--primary-gold);
  animation: sparkle 2s infinite;
  opacity: 0;
}

.logo-sparkle::before {
  top: -5px;
  right: -5px;
  animation-delay: 0s;
}

.logo-sparkle::after {
  bottom: -5px;
  left: -5px;
  animation-delay: 1s;
}

@keyframes sparkle {
  0%, 100% { 
    opacity: 0; 
    transform: scale(0); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* Enhanced brand logo hover effects */
.brand-logo {
  height: 40px;
  width: auto;
  transition: all var(--transition-normal);
  filter: drop-shadow(0 0 5px rgba(212, 175, 55, 0.3));
}

.brand-link:hover .brand-logo {
  filter: 
    brightness(1.3) 
    drop-shadow(0 0 15px var(--primary-gold))
    drop-shadow(0 0 25px var(--primary-gold));
  transform: rotate(10deg) scale(1.1);
  animation: gentle-pulse 1.5s infinite;
}

@keyframes gentle-pulse {
  0%, 100% { 
    transform: rotate(10deg) scale(1.1); 
  }
  50% { 
    transform: rotate(10deg) scale(1.15); 
  }
}

.game-tab.active[data-game="war3"] i {
  color: var(--neutral-900); /* Dark text when active */
}

/* Animation for tab switching */
@keyframes tabSwitch {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.game-tab.active {
  animation: tabSwitch 0.3s ease-out;
}

/* =============================================================================
   END GAME TABS SYSTEM
   ============================================================================= */

/* Logout button styling */
.nav-dropdown-item.logout-item {
  color: #ef4444;
  transition: all var(--transition-fast);
}

.nav-dropdown-item.logout-item:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #fca5a5;
}

.mobile-nav-item.logout-item {
  color: #ef4444;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: var(--space-2);
  padding-top: var(--space-4);
}

.mobile-nav-item.logout-item:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #fca5a5;
}

.mobile-nav-section-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: var(--space-4) var(--space-4) var(--space-2);
}

/* Responsive Design - Show mobile menu on smaller screens */
@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: flex;
  }
  
  .navbar-nav-desktop {
    display: none;
  }
  
  .support-section {
    display: none;
  }
  
  .navbar-container {
    padding: 0 var(--space-4);
  }
  
  .brand-text {
    display: none;
  }
  
  .brand-logo {
    height: 32px;
  }
}

/* Notifications Dropdown Styles */
.notifications-dropdown {
  display: none;
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.98), rgba(40, 40, 40, 0.95));
  border: 2px solid var(--primary-gold);
  border-radius: var(--radius-lg);
  min-width: 320px;
  max-width: 400px;
  max-height: 450px;
  overflow-y: auto;
  z-index: 10002;
  box-shadow: 
    0 10px 40px rgba(0, 0, 0, 0.6),
    0 0 30px rgba(212, 175, 55, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  animation: slideDownFade 0.3s ease-out;
  transform-origin: top right;
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.notifications-dropdown.show {
  display: block;
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

/* Higher specificity for navbar notifications dropdown */
.navbar-modern .notifications-dropdown,
.navbar-modern .avatar-notifications .notifications-dropdown {
  display: none;
  position: absolute;
  top: calc(100% + 12px);
  right: 0;
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.98), rgba(40, 40, 40, 0.95));
  border: 2px solid var(--primary-gold);
  border-radius: var(--radius-lg);
  min-width: 320px;
  max-width: 400px;
  max-height: 450px;
  overflow-y: auto;
  z-index: 10003;
  box-shadow: 
    0 10px 40px rgba(0, 0, 0, 0.8),
    0 0 30px rgba(212, 175, 55, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  animation: slideDownFade 0.4s ease-out;
  transform-origin: top right;
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.navbar-modern .notifications-dropdown.show,
.navbar-modern .avatar-notifications .notifications-dropdown.show {
  display: block;
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

@keyframes slideDownFade {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
    visibility: visible;
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    visibility: visible;
  }
}

.notifications-dropdown-container {
  position: relative;
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--horde-red);
  color: white;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--neutral-900);
  animation: none;
}

.notification-badge:empty {
  display: none;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.notifications-header h4 {
  margin: 0;
  color: var(--primary-gold);
  font-size: var(--text-sm);
  font-weight: 600;
}

.mark-all-read {
  background: none;
  border: none;
  color: var(--neutral-400);
  cursor: pointer;
  font-size: var(--text-xs);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.mark-all-read:hover {
  background: rgba(212, 175, 55, 0.1);
  color: var(--primary-gold);
}

.notifications-list {
  padding: var(--space-2);
}

.notification-item {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-2);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.notification-item.unread {
  background: rgba(212, 175, 55, 0.05);
  border-left: 3px solid var(--primary-gold);
}

.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(212, 175, 55, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-gold);
  flex-shrink: 0;
  font-size: var(--text-sm);
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-message {
  color: var(--neutral-100);
  font-size: var(--text-sm);
  line-height: 1.4;
  margin: 0 0 var(--space-1) 0;
}

.notification-time {
  color: var(--neutral-400);
  font-size: var(--text-xs);
}

.notifications-empty {
  text-align: center;
  padding: var(--space-8);
  color: var(--neutral-400);
}

.notifications-empty i {
  font-size: var(--text-2xl);
  margin-bottom: var(--space-2);
  color: rgba(212, 175, 55, 0.3);
}

/* Navbar specific profile name - more specific selector to ensure it takes precedence */
.profile-info {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.nav-dropdown-item.current-page,
.mobile-nav-item.current-page {
  color: var(--primary-gold);
  background: rgba(212, 175, 55, 0.15);
  border-left-color: var(--primary-gold);
  cursor: default;
  opacity: 0.8;
  position: relative;
}

.nav-dropdown-item.current-page::after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: var(--primary-gold);
  border-radius: 50%;
  box-shadow: 0 0 4px var(--primary-gold);
}

.mobile-nav-item.current-page::after {
  content: '';
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: var(--primary-gold);
  border-radius: 50%;
  box-shadow: 0 0 4px var(--primary-gold);
}

.nav-dropdown-item.current-page:hover,
.mobile-nav-item.current-page:hover {
  background: rgba(212, 175, 55, 0.15);
  color: var(--primary-gold);
  transform: none;
}

.dropdown-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0;
}

/* Force TOWN HALL text size with high specificity */
.navbar-modern .profile-dropdown-toggle .profile-info .profile-title,
.navbar-modern .profile-title,
.profile-dropdown-toggle .profile-title {
  font-weight: 700;
  font-size: 22px;
  color: var(--primary-gold);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Modal styles moved to /css/components/modals.css */

/* Report match modal specific */
#report-match-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10000;
  display: none;
  align-items: center;
  justify-content: center;
}

#report-match-modal.show {
  display: flex;
}

/* Notification dropdown simple fix */
.notifications-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: rgba(20, 20, 20, 0.95);
  border: 2px solid #D4AF37;
  border-radius: 8px;
  min-width: 300px;
  max-height: 400px;
  overflow-y: auto;
  z-index: 10001;
  display: none;
}

.notifications-dropdown.show {
  display: block;
}

/* Force all modals to work */
[id*="modal"],
[class*="modal"] {
  z-index: 10000;
}

[id*="modal"].show,
[class*="modal"].show {
  display: flex;
}

/* ==========================================================================
   LADDER PAGE COMPACT LAYOUT
   ========================================================================== */

.ladder-main-content {
  display: grid;
  grid-template-columns: 200px 1fr 220px;
  gap: 20px;
  margin-bottom: 30px;
  width: 100%;
  max-width: none;
}

/* Override media queries that constrain the layout */
@media (max-width: 1200px) {
  .ladder-main-content {
    grid-template-columns: 180px 1fr 200px;
  }
}

/* Ranks Sidebar */
.ranks-sidebar {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  height: fit-content;
}

.ranks-sidebar h3 {
  color: var(--warcraft-gold);
  font-family: 'Cinzel', serif;
  font-size: 16px;
  margin: 0 0 15px 0;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ranks-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rank-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.rank-item:hover {
  background: rgba(255, 215, 0, 0.2);
}

.rank-item img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.rank-item .rank-name {
  font-size: 11px;
  font-weight: 500;
  color: #fff;
}

.rank-item .rank-mmr {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
  margin-left: auto;
}

/* Recent Matches Sidebar */
.recent-matches-sidebar {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  height: fit-content;
}

.recent-matches-sidebar h3 {
  color: var(--warcraft-gold);
  font-family: 'Cinzel', serif;
  font-size: 16px;
  margin: 0 0 15px 0;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Compact Recent Matches */
.compact-matches-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.compact-match-item {
  background: rgba(255, 215, 0, 0.05);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.compact-match-item:hover {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.4);
  transform: translateY(-1px);
}

.compact-match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.compact-match-type {
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.compact-match-date {
  color: #999;
  font-size: 10px;
}

.compact-match-map {
  color: #fff;
  font-size: 11px;
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.compact-match-players {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.compact-match-players-list {
  color: #ccc;
  font-size: 10px;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.matches-view-all {
  margin-top: 12px;
  text-align: center;
}

.matches-view-all .btn {
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
  color: #ffd700;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 11px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.matches-view-all .btn:hover {
  background: rgba(255, 215, 0, 0.2);
  border-color: #ffd700;
  color: #fff;
}

.no-matches, .error {
  text-align: center;
  color: #999;
  font-size: 12px;
  padding: 16px;
  font-style: italic;
}

.error {
  color: #ff6b6b;
}

@media (max-width: 1200px) {
  .ladder-main-content {
    grid-template-columns: 180px 1fr 280px;
  }
}

@media (max-width: 992px) {
  .ladder-main-content {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .ranks-sidebar,
  .recent-matches-sidebar {
    height: auto;
  }

  .leaderboard-section {
    order: -1;
  }
}

@media (max-width: 768px) {
  .ranks-sidebar,
  .recent-matches-sidebar {
    display: none;
  }

  .ranks-sidebar h3,
  .recent-matches-sidebar h3 {
    font-size: 14px;
  }
}

/* Leaderboard Section - Now extends to fill available space */
.leaderboard-section {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 8px;
  padding: 20px;
  height: fit-content;
  min-height: 500px;
}

.leaderboard-section h2 {
  color: var(--warcraft-gold);
  font-family: 'Cinzel', serif;
  font-size: 24px;
  margin: 0 0 20px 0;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.leaderboard-table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.leaderboard-table th,
.leaderboard-table td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid rgba(255, 215, 0, 0.1);
}

.leaderboard-table th {
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

.leaderboard-table tbody tr:hover {
  background: rgba(255, 215, 0, 0.05);
}

.loading-cell {
  text-align: center;
  padding: 40px;
  color: #999;
  font-style: italic;
}

/* ===== NAVIGATION SYSTEM ===== */
.nav-dropdown {
  position: relative;
}

.nav-dropdown-menu {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  background: var(--bg-card);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-2);
  min-width: 220px;
  box-shadow: var(--shadow-xl);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-normal);
  z-index: 1010; /* Ensure it's above other navbar content */
}

.nav-dropdown.active > .nav-dropdown-menu,
.nav-dropdown[aria-expanded="true"] > .nav-dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.nav-chevron {
  font-size: 0.7rem;
  margin-left: var(--space-2);
  transition: transform var(--transition-normal);
}

.nav-dropdown.active .nav-chevron,
.nav-dropdown[aria-expanded="true"] .nav-chevron {
  transform: rotate(180deg);
}

/* ===== BLACKSMITH PAGE STYLES ===== */

/* Blacksmith Tabs */
.blacksmith-tabs {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-6);
  border-bottom: 2px solid var(--glass-border);
  flex-wrap: wrap;
}

.blacksmith-tab {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: var(--neutral-300);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  font-family: var(--font-display);
  font-weight: 600;
  transition: all var(--transition-normal);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  border-bottom: none;
  position: relative;
  top: 2px;
  min-width: 140px;
  justify-content: center;
  text-align: center;
}

.blacksmith-tab:hover {
  background: var(--bg-card-hover);
  color: var(--primary-gold-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.blacksmith-tab.active {
  background: var(--primary-gold);
  color: var(--neutral-900);
  border-color: var(--primary-gold);
  box-shadow: var(--shadow-glow);
}

.blacksmith-tab i {
  font-size: 1.1em;
}

/* Tab Panels */
.blacksmith-content {
  min-height: 400px;
}

.tab-panel {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.tab-panel.active {
  display: block;
}

.tab-title {
  font-family: var(--font-display);
  font-size: var(--text-2xl);
  color: var(--primary-gold);
  margin-bottom: var(--space-4);
  text-align: center;
}

/* Version Intro */
.version-intro {
  text-align: center;
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--glass-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border);
}

.version-intro p {
  font-size: var(--text-lg);
  color: var(--neutral-200);
  font-style: italic;
}

/* Features Section */
.features-section {
  margin-top: var(--space-6);
}

.features-header {
  font-family: var(--font-display);
  font-size: var(--text-xl);
  color: var(--primary-gold);
  margin-bottom: var(--space-4);
  text-align: center;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-3);
  margin-top: var(--space-3);
}

.feature-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-3);
  transition: all var(--transition-normal);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.feature-card:hover {
  background: var(--bg-card-hover);
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-gold);
}

.feature-card.detailed {
  min-height: 140px;
}

.feature-card.future {
  border: 1px solid rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, var(--glass-bg), rgba(59, 130, 246, 0.1));
}

.feature-card.future:hover {
  border-color: var(--alliance-blue);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.2);
}

.feature-icon {
  font-size: 1.5rem;
  color: var(--primary-gold);
  margin-bottom: var(--space-2);
  display: block;
}

.feature-card.future .feature-icon {
  color: var(--alliance-blue);
}

.feature-card h4 {
  font-family: var(--font-display);
  font-size: var(--text-base);
  color: var(--neutral-100);
  margin-bottom: var(--space-2);
}

.feature-card p {
  color: var(--neutral-300);
  line-height: 1.5;
  font-size: var(--text-xs);
}

/* Poll Styles */
.poll-container-large {
  max-width: 600px;
  margin: 0 auto;
  padding: var(--space-6);
  background: var(--glass-bg);
  border-radius: var(--radius-xl);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(20px);
}

.poll-question {
  font-family: var(--font-display);
  font-size: var(--text-2xl);
  color: var(--primary-gold);
  text-align: center;
  margin-bottom: var(--space-6);
  font-weight: 600;
}

.poll-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.poll-option {
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.poll-option:hover {
  border-color: var(--primary-gold);
  background: var(--bg-card-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.poll-option-label {
  font-family: var(--font-display);
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--neutral-100);
  margin-bottom: var(--space-2);
}

.poll-option-bar {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  height: 20px;
  overflow: hidden;
  margin-bottom: var(--space-2);
}

.poll-option-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-gold), var(--primary-gold-light));
  border-radius: var(--radius-md);
  transition: width 0.5s ease;
  width: 0%;
}

.poll-option-stats {
  display: flex;
  justify-content: space-between;
  font-size: var(--text-sm);
  color: var(--neutral-400);
}

.poll-option-percentage {
  font-weight: 600;
  color: var(--primary-gold);
}

.poll-results {
  margin-top: var(--space-4);
  text-align: center;
}

.poll-results-text {
  color: var(--neutral-300);
  margin-bottom: var(--space-4);
}

.save-marriage-btn {
  background: linear-gradient(135deg, var(--horde-red), var(--horde-red-light));
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-family: var(--font-display);
  font-weight: 600;
  font-size: var(--text-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: 0 auto;
}

.save-marriage-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
  background: linear-gradient(135deg, var(--horde-red-light), #FF6B6B);
}

.save-marriage-btn i {
  font-size: 1.2em;
}

/* Feedback Form Styles */
.feedback-container-large {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-6);
  background: var(--glass-bg);
  border-radius: var(--radius-xl);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(20px);
}

.feedback-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.feedback-container-large .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.feedback-container-large .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.feedback-container-large .form-group label {
  font-family: var(--font-display);
  font-weight: 600;
  color: var(--primary-gold);
  font-size: var(--text-sm);
}

.feedback-container-large .form-group input,
.feedback-container-large .form-group select,
.feedback-container-large .form-group textarea {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  color: var(--neutral-100);
  font-family: var(--font-primary);
  transition: all var(--transition-normal);
}

.feedback-container-large .form-group input:focus,
.feedback-container-large .form-group select:focus,
.feedback-container-large .form-group textarea:focus {
  outline: none;
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
  background: var(--bg-card-hover);
}

.feedback-container-large .form-group textarea {
  min-height: 120px;
  resize: vertical;
}

.feedback-container-large .checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  font-size: var(--text-sm);
  color: var(--neutral-300);
}

.feedback-container-large .checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.feedback-container-large .contact-info {
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.05);
}

.feedback-container-large .contact-input-spacing {
  margin-top: var(--space-2);
}

.feedback-container-large .form-actions {
  margin-top: var(--space-4);
  text-align: center;
}

.feedback-container-large .btn-primary {
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
  color: var(--neutral-900);
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-family: var(--font-display);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.feedback-container-large .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
  background: linear-gradient(135deg, var(--primary-gold-light), #F4D03F);
}

.feedback-message {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  margin-top: var(--space-4);
}

.feedback-message.success {
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.4);
  color: #86EFAC;
}

.feedback-message.error {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.4);
  color: #FCA5A5;
}

.feedback-message i {
  font-size: 1.2em;
}

/* Enhanced feedback success message styling */
.feedback-success-content {
  width: 100%;
}

.feedback-success-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.feedback-success-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: #86EFAC;
  font-weight: 700;
}

.feedback-success-header i {
  font-size: 1.5rem;
  color: #86EFAC;
}

.feedback-summary {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 6px;
  padding: var(--space-3);
  margin: var(--space-3) 0;
}

.feedback-summary p {
  margin: var(--space-1) 0;
  font-size: 0.9rem;
  color: var(--neutral-200);
}

.feedback-summary strong {
  color: #86EFAC;
  font-weight: 600;
  display: inline-block;
  min-width: 90px;
}

.feedback-success-footer {
  margin-top: var(--space-3);
}

.feedback-success-footer p {
  margin: 0;
  font-size: 0.95rem;
  color: var(--neutral-300);
  line-height: 1.5;
}

/* Membership Info */
.membership-info {
  margin-top: var(--space-8);
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
}

.membership-info p {
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.membership-info i {
  color: var(--primary-gold);
}

/* Currency Selector Improvements */
.currency-selector-wrapper {
  margin: var(--space-4) 0 var(--space-6) 0;
}

.currency-selector {
  transition: all var(--transition-normal);
}

.currency-selector:hover {
  border-color: var(--primary-gold);
  box-shadow: 0 6px 25px rgba(212, 175, 55, 0.2);
  transform: translateY(-2px);
}

/* Hero Page Specific Improvements */
.page-header {
  margin-bottom: var(--space-4);
}

.section-subtitle.enhanced {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-8) !important;
  line-height: 1.6;
}

/* Utility Classes */
.hidden {
  display: none;
}

.centered {
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .blacksmith-tabs {
    flex-direction: column;
    gap: var(--space-1);
  }
  
  .blacksmith-tab {
    border-radius: var(--radius-md);
    top: 0;
    min-width: auto;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .feedback-container-large .form-row {
    grid-template-columns: 1fr;
  }
  
  .poll-container-large,
  .feedback-container-large {
    padding: var(--space-4);
    margin: 0 var(--space-2);
  }
  
  .tab-title {
    font-size: var(--text-xl);
  }
  
  .poll-question {
    font-size: var(--text-xl);
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* ===================================
   MEMBERSHIP TIER STYLES
   =================================== */

/* Membership Container */
.membership-showcase {
  text-align: center;
}

/* Compact membership section for blacksmith page */
.glass-section:has(.membership-showcase) {
  margin-top: 0.75rem;
}

/* Fallback for browsers without :has() support */
[data-page="blacksmith"] .glass-section:nth-child(2) {
  margin-top: 0.75rem;
}

.membership-tiers {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-4);
  margin: var(--space-6) 0;
  padding: 0 var(--space-4);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

/* Individual Tier Card */
.membership-tier {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-5);
  position: relative;
  transition: all var(--transition-normal);
  backdrop-filter: blur(20px);
  overflow: hidden;
  min-height: 360px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.membership-tier::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 215, 0, 0.05) 0%,
    rgba(255, 215, 0, 0.02) 50%,
    rgba(255, 215, 0, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
}

.membership-tier:hover {
  border-color: var(--primary-gold);
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.membership-tier:hover::before {
  opacity: 1;
}

.membership-tier.featured {
  border-color: var(--primary-gold);
  background: linear-gradient(135deg, 
    var(--glass-bg),
    rgba(255, 215, 0, 0.08)
  );
  transform: scale(1.02);
  box-shadow: 0 15px 35px rgba(255, 215, 0, 0.15);
}

.membership-tier.featured::after {
  content: "✨ MOST POPULAR";
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
  color: var(--neutral-900);
  padding: 8px 20px;
  border-radius: 0 0 12px 12px;
  font-size: 0.75rem;
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: uppercase;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.membership-tier.current-tier {
  border-color: var(--success-color);
  background: linear-gradient(135deg, 
    var(--glass-bg),
    rgba(34, 197, 94, 0.05)
  );
}

.membership-tier.current-tier::after {
  content: "⚡ ACTIVE";
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, var(--success-color), #22c55e);
  color: white;
  padding: 8px 20px;
  border-radius: 0 0 12px 12px;
  font-size: 0.75rem;
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: uppercase;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.membership-tier.unlocked-tier {
  border-color: var(--alliance-blue);
  background: linear-gradient(135deg, 
    var(--glass-bg),
    rgba(59, 130, 246, 0.05)
  );
  opacity: 0.9;
}

/* Tier Badges */
.current-tier-badge,
.unlocked-tier-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.current-tier-badge {
  background: #00ff00;
  color: var(--neutral-900);
}

.unlocked-tier-badge {
  background: #90ee90;
  color: var(--neutral-900);
}

/* Tier Content */
.tier-name {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-gold);
  margin-bottom: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 2;
  text-align: center;
}

.tier-price {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--neutral-100);
  margin-bottom: var(--space-3);
  text-align: center;
  position: relative;
  z-index: 2;
}

.tier-price .currency {
  color: var(--primary-gold);
  font-weight: 700;
  font-size: 1.5rem;
}

.tier-description {
  color: var(--neutral-300);
  margin-bottom: var(--space-3);
  line-height: 1.5;
  font-size: 0.9rem;
  text-align: center;
  flex-grow: 1;
  position: relative;
  z-index: 2;
}

/* Tier Images */
.tier-images {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
}

.tier-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 2px solid var(--primary-gold);
  object-fit: cover;
  transition: all var(--transition-normal);
  background: var(--glass-bg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.tier-image:hover {
  transform: scale(1.15) translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
  border-color: var(--primary-gold-light);
}

/* Tier Buttons */
.tier-buttons {
  margin-top: auto;
  position: relative;
  z-index: 2;
}

.tier-btn {
  width: 100%;
  padding: var(--space-4) var(--space-5);
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  font-family: var(--font-display);
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.tier-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.tier-btn:hover::before {
  left: 100%;
}

.tier-btn > * {
  position: relative;
  z-index: 2;
}

.tier-btn.monthly {
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
  color: var(--neutral-900);
  border-color: var(--primary-gold);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
}

.tier-btn.monthly:hover {
  background: linear-gradient(135deg, var(--primary-gold-light), #ffed4a);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
  border-color: var(--primary-gold-light);
}

.tier-btn.upgrade {
  background: linear-gradient(135deg, var(--horde-red), var(--horde-red-light));
  color: white;
  border-color: var(--horde-red);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.2);
}

.tier-btn.upgrade:hover {
  background: linear-gradient(135deg, var(--horde-red-light), #f87171);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
  border-color: var(--horde-red-light);
}

.tier-btn.donation {
  background: linear-gradient(135deg, #e84393, #fd79a8);
  color: white;
  border-color: #e84393;
  box-shadow: 0 4px 15px rgba(232, 67, 147, 0.2);
}

.tier-btn.donation:hover {
  background: linear-gradient(135deg, #d63384, #e84393);
  transform: translateY(-2px);
}

.tier-btn.current {
  background: #28a745;
  color: white;
  cursor: not-allowed;
}

.tier-btn.unlocked {
  background: #6c757d;
  color: white;
  cursor: not-allowed;
}

.tier-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.tier-btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Membership Info */
.membership-info {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid var(--neutral-600);
  border-radius: 10px;
  padding: var(--space-3);
  margin: var(--space-4) 0;
  text-align: center;
}

.membership-info p {
  margin: var(--space-2) 0;
  color: var(--neutral-300);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.membership-info i {
  color: var(--primary-400);
}

/* Donation Links */
.donation-links {
  margin-top: var(--space-5);
}

.donation-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-200);
  margin-bottom: var(--space-3);
}

.donation-buttons {
  display: flex;
  justify-content: center;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.donation-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-5);
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.donation-btn.paypal {
  background: #0070ba;
  color: white;
}

.donation-btn.paypal:hover {
  background: #005a9c;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 112, 186, 0.3);
}

.donation-btn.coinbase {
  background: #1652f0;
  color: white;
}

.donation-btn.coinbase:hover {
  background: #0d47a1;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(22, 82, 240, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .membership-tiers {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3);
  }
}

@media (max-width: 768px) {
  .membership-tiers {
    grid-template-columns: 1fr;
    padding: 0 var(--space-2);
    gap: var(--space-4);
    margin: var(--space-4) 0;
  }

  .membership-tier {
    min-height: 320px;
    padding: var(--space-4);
  }

  .membership-tier.featured {
    transform: none;
  }

  .tier-images {
    gap: var(--space-2);
  }

  .tier-image {
    width: 40px;
    height: 40px;
  }

  .donation-buttons {
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
  }

  .donation-btn {
    width: 100%;
    max-width: 220px;
    justify-content: center;
    padding: var(--space-2) var(--space-4);
  }

  .currency-selector-wrapper {
    margin: var(--space-3) 0 var(--space-4) 0;
  }

  .currency-selector {
    max-width: 180px;
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }
}

/* ===== PAGE TITLE SYSTEM ===== */
.page-header {
  text-align: center;
  margin: 0 0 var(--space-6) 0;
  padding: 0 var(--space-4);
}

.page-title {
  font-family: var(--font-display);
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 800;
  line-height: 1.2;
  margin: 0;
  padding: var(--space-2) 0;
  position: relative;
  
  /* Fallback solid color - always visible */
  color: var(--primary-gold);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  
  /* Font rendering optimization */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Progressive enhancement with gradient support */
@supports (background-clip: text) or (-webkit-background-clip: text) {
  .page-title[data-theme="blacksmith"] {
    background: linear-gradient(
      45deg,
      #D4AF37 0%,     /* Gold */
      #FF8C00 50%,    /* Orange */
      #DC143C 100%    /* Fire Red */
    );
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: blacksmithPulse 4s ease-in-out infinite;
  }
  
  .page-title[data-theme="arena"] {
    background: linear-gradient(
      45deg,
      #FFD700 0%,     /* Bright Gold */
      #00BFFF 50%,    /* Bright Sky Blue */
      #1E90FF 100%    /* Bright Champion Blue */
    );
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: arenaPulse 4s ease-in-out infinite;
  }
  
  .page-title[data-theme="live"] {
    background: linear-gradient(
      45deg,
      #FF6B6B 0%,     /* Bright Red */
      #4ECDC4 50%,    /* Turquoise */
      #45B7D1 100%    /* Sky Blue */
    );
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: livePulse 4s ease-in-out infinite;
  }

  .page-title[data-theme="wizards-tower"] {
    background: linear-gradient(
      45deg,
      #8B5CF6 0%,     /* Purple */
      #A855F7 50%,    /* Violet */
      #C084FC 100%    /* Light Purple */
    );
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: wizardsTowerPulse 4s ease-in-out infinite;
  }
}

/* Blacksmith Theme Animation */
@keyframes blacksmithPulse {
  0%, 100% {
    background-position: 0% 50%;
    filter: brightness(1) saturate(1);
  }
  25% {
    background-position: 50% 0%;
    filter: brightness(1.2) saturate(1.3);
  }
  50% {
    background-position: 100% 50%;
    filter: brightness(1.4) saturate(1.5);
  }
  75% {
    background-position: 50% 100%;
    filter: brightness(1.2) saturate(1.3);
  }
}

/* Arena Theme Animation */
@keyframes arenaPulse {
  0%, 100% {
    background-position: 0% 50%;
    filter: brightness(1) saturate(1);
  }
  25% {
    background-position: 50% 0%;
    filter: brightness(1.2) saturate(1.3);
  }
  50% {
    background-position: 100% 50%;
    filter: brightness(1.4) saturate(1.5);
  }
  75% {
    background-position: 50% 100%;
    filter: brightness(1.2) saturate(1.3);
  }
}

/* Live Theme Animation */
@keyframes livePulse {
  0%, 100% {
    background-position: 0% 50%;
    filter: brightness(1) saturate(1);
  }
  25% {
    background-position: 50% 0%;
    filter: brightness(1.2) saturate(1.3);
  }
  50% {
    background-position: 100% 50%;
    filter: brightness(1.4) saturate(1.5);
  }
  75% {
    background-position: 50% 100%;
    filter: brightness(1.2) saturate(1.3);
  }
}

/* Wizard's Tower Theme Animation */
@keyframes wizardsTowerPulse {
  0%, 100% {
    background-position: 0% 50%;
    filter: brightness(1) saturate(1);
  }
  25% {
    background-position: 50% 0%;
    filter: brightness(1.1) saturate(1.2);
  }
  50% {
    background-position: 100% 50%;
    filter: brightness(1.2) saturate(1.3);
  }
  75% {
    background-position: 50% 100%;
    filter: brightness(1.1) saturate(1.2);
  }
}

/* Fallback for browsers that don't support background-clip */
.page-title[data-theme="blacksmith"]:not([style*="background-clip"]) {
  color: var(--primary-gold);
  animation: textGlowBlacksmith 4s ease-in-out infinite;
}

.page-title[data-theme="arena"]:not([style*="background-clip"]) {
  color: var(--primary-gold);
  animation: textGlowArena 4s ease-in-out infinite;
}

.page-title[data-theme="live"]:not([style*="background-clip"]) {
  color: var(--primary-gold);
  animation: textGlowLive 4s ease-in-out infinite;
}

@keyframes textGlowBlacksmith {
  0%, 100% {
    color: var(--primary-gold);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 
                 0 0 10px rgba(212, 175, 55, 0.3);
  }
  25% {
    color: #FF8C00;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 
                 0 0 15px rgba(255, 140, 0, 0.4);
  }
  50% {
    color: #DC143C;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 
                 0 0 20px rgba(220, 20, 60, 0.5);
  }
  75% {
    color: #FF8C00;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 
                 0 0 15px rgba(255, 140, 0, 0.4);
  }
}

@keyframes textGlowArena {
  0%, 100% {
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 
                 0 0 15px rgba(255, 215, 0, 0.5);
  }
  25% {
    color: #00BFFF;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 
                 0 0 20px rgba(0, 191, 255, 0.6);
  }
  50% {
    color: #1E90FF;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 
                 0 0 25px rgba(30, 144, 255, 0.7);
  }
  75% {
    color: #00BFFF;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 
                 0 0 20px rgba(0, 191, 255, 0.6);
  }
}

@keyframes textGlowLive {
  0%, 100% {
    color: #FF6B6B;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 
                 0 0 15px rgba(255, 107, 107, 0.5);
  }
  25% {
    color: #4ECDC4;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 
                 0 0 20px rgba(78, 205, 196, 0.6);
  }
  50% {
    color: #45B7D1;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 
                 0 0 25px rgba(69, 183, 209, 0.7);
  }
  75% {
    color: #4ECDC4;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 
                 0 0 20px rgba(78, 205, 196, 0.6);
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .page-title {
    font-size: clamp(1.5rem, 6vw, 2.5rem);
    padding: var(--space-1) 0;
  }
}

/* ===== BLACKSMITH PAGE STYLES ===== */

/* Screenshot Management Styles */
.screenshots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.screenshot-card {
  background: rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.screenshot-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.screenshot-image {
  position: relative;
  height: 160px;
  overflow: hidden;
  cursor: pointer;
}

.screenshot-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.screenshot-image:hover img {
  transform: scale(1.05);
}

.screenshot-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.screenshot-image:hover .screenshot-overlay {
  opacity: 1;
}

.screenshot-overlay i {
  font-size: 2rem;
  color: #fff;
}

.screenshot-info {
  padding: 1rem;
}

.screenshot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.screenshot-filename {
  font-weight: 500;
  color: #fff;
  font-size: 0.9rem;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.screenshot-time {
  font-size: 0.8rem;
  color: #888;
}

.screenshot-status {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.analysis-status, .reporting-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.85rem;
}

.analysis-status i, .reporting-status i {
  min-width: 16px;
}

/* Analysis Status Colors */
.analysis-status.analyzed {
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #22c55e;
}

.analysis-status.no-result {
  background: rgba(156, 163, 175, 0.2);
  border: 1px solid rgba(156, 163, 175, 0.3);
  color: #9ca3af;
}

.analysis-status.analyzing {
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

.analysis-status.pending {
  background: rgba(245, 158, 11, 0.2);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #f59e0b;
}

/* Reporting Status Colors */
.reporting-status.reported {
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #22c55e;
}

.reporting-status.ready-to-report {
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #3b82f6;
  justify-content: space-between;
}

.reporting-status.low-confidence {
  background: rgba(245, 158, 11, 0.2);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #f59e0b;
}

.reporting-status.not-applicable {
  background: rgba(156, 163, 175, 0.2);
  border: 1px solid rgba(156, 163, 175, 0.3);
  color: #9ca3af;
}

.btn-small {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-small.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-small.btn-primary:hover {
  background: #2563eb;
}

.screenshot-settings {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.screenshot-settings .checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ccc;
  font-size: 0.9rem;
}

.screenshot-settings .checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
}

.info-card {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.info-card h4 {
  color: #3b82f6;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-card li {
  padding: 0.5rem 0;
  color: #ccc;
  position: relative;
  padding-left: 1.5rem;
}

.info-card li:before {
  content: "•";
  color: #3b82f6;
  position: absolute;
  left: 0;
  font-weight: bold;
}

.screenshot-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.no-screenshots-message {
  text-align: center;
  padding: 3rem 1rem;
  color: #888;
}

.no-screenshots-message i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-screenshots-message p {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #ccc;
}

.no-screenshots-message small {
  display: block;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .screenshots-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
  
  .screenshot-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .screenshot-filename {
    max-width: 100%;
  }
  
  .reporting-status.ready-to-report {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
