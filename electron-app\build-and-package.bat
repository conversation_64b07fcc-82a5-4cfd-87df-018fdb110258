@echo off
cls
title WC Arena Companion - Build & Package
color 0E

echo.
echo ==========================================
echo    WC Arena Companion - Build & Package
echo ==========================================
echo.

REM Change to script directory
cd /d "%~dp0"

REM Check if package.json exists
if not exist "package.json" (
    echo ERROR: package.json not found!
    echo Make sure you're running this from the electron-app folder.
    pause
    exit /b 1
)

echo [INFO] Current directory: %CD%
echo.

REM Install dependencies if needed
if not exist "node_modules" (
    echo [INFO] Installing dependencies...
    call npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installed!
    echo.
)

REM Clean previous builds
echo [INFO] Cleaning previous builds...
if exist "dist" (
    rmdir /s /q "dist"
    echo [SUCCESS] Previous builds cleaned
) else (
    echo [INFO] No previous builds to clean
)
echo.

REM Build the application
echo [INFO] Building WC Arena Companion for distribution...
echo [INFO] This may take several minutes...
echo.

call npm run build

if errorlevel 1 (
    echo.
    echo [ERROR] Build failed!
    echo Check the output above for details.
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Build completed successfully!
echo.
echo [INFO] Build output is in the 'dist' folder:

if exist "dist" (
    dir "dist" /b
) else (
    echo [WARNING] No dist folder found - check build configuration
)

echo.
echo [INFO] You can find the installer/executable in the dist folder
echo [INFO] The installer can be distributed to other computers
echo.
pause 