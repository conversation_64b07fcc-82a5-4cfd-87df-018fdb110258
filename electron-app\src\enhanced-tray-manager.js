const { Tray, Menu, shell, dialog, nativeImage, BrowserWindow } = require('electron');
const path = require('path');
const fs = require('fs');

class EnhancedTrayManager {
  constructor(app, windowManager, gameDetector, store, screenshotManager) {
    this.app = app;
    this.windowManager = windowManager;
    this.gameDetector = gameDetector;
    this.store = store;
    this.screenshotManager = screenshotManager;
    this.tray = null;
    this.isQuitting = false;
    this.garrisonWindow = null;
    this.notificationCount = 0;
    this.statusIndicator = 'idle'; // idle, gaming, online, offline
    this.lastActivity = Date.now();
    
    // Garrison integration
    this.garrisonStatus = {
      online: false,
      unreadMessages: 0,
      activeChats: 0,
      notifications: 0
    };
  }

  createTray() {
    // Create enhanced tray icon with status indicators
    const iconPath = this.getEnhancedTrayIconPath();
    this.tray = new Tray(iconPath);
    
    this.tray.setToolTip('WC Arena Core - Next Level Gaming Hub');
    
    // Enhanced tray interactions
    this.tray.on('click', (event) => {
      if (event.ctrlKey || event.metaKey) {
        // Ctrl/Cmd + click opens garrison
        this.openGarrison();
      } else {
        this.toggleMainWindow();
      }
    });

    this.tray.on('right-click', () => {
      this.tray.popUpContextMenu();
    });

    this.tray.on('double-click', () => {
      this.openGarrison();
    });

    // Initial menu setup
    this.updateEnhancedTrayMenu();
    
    // Start status monitoring
    this.startStatusMonitoring();
    
    return this.tray;
  }

  getEnhancedTrayIconPath() {
    const iconDir = path.join(__dirname, '..', 'assets');
    
    // Create dynamic icon based on status
    return this.createDynamicTrayIcon();
  }

  createDynamicTrayIcon() {
    try {
      const iconDir = path.join(__dirname, '..', 'assets');
      const baseIconPath = path.join(iconDir, 'tray-icon.png');
      
      // Create a dynamic icon with status overlay
      const baseIcon = nativeImage.createFromPath(baseIconPath);
      
      // Add status indicator overlay
      const statusColor = this.getStatusColor();
      const overlaySize = 8;
      const overlay = nativeImage.createFromBuffer(this.createStatusOverlay(statusColor, overlaySize));
      
      // Composite the overlay onto the base icon
      const finalIcon = baseIcon.resize({ width: 16, height: 16 });
      finalIcon.addRepresentation({
        width: 16,
        height: 16,
        scaleFactor: 1,
        buffer: overlay.toPNG()
      });
      
      return finalIcon;
    } catch (error) {
      console.warn('⚠️ Error creating dynamic tray icon, using fallback:', error.message);
      // Return the base icon path as fallback
      const iconDir = path.join(__dirname, '..', 'assets');
      return path.join(iconDir, 'tray-icon.png');
    }
  }

  getStatusColor() {
    switch (this.statusIndicator) {
      case 'gaming': return '#FF6B6B'; // Red for gaming
      case 'online': return '#4ECDC4'; // Turquoise for online
      case 'offline': return '#95A5A6'; // Gray for offline
      case 'notification': return '#F39C12'; // Orange for notifications
      default: return '#2ECC71'; // Green for idle
    }
  }

  createStatusOverlay(color, size) {
    try {
      // Try to use canvas if available
      const canvas = require('canvas');
      const c = canvas.createCanvas(size, size);
      const ctx = c.getContext('2d');
      
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.arc(size/2, size/2, size/2, 0, 2 * Math.PI);
      ctx.fill();
      
      return c.toBuffer();
    } catch (error) {
      console.warn('⚠️ Canvas not available for status overlay, using fallback');
      // Return a simple fallback buffer - transparent overlay
      const buffer = Buffer.alloc(size * size * 4, 0);
      
      // Set alpha channel to make it transparent
      for (let i = 3; i < buffer.length; i += 4) {
        buffer[i] = 0; // Alpha = 0 (transparent)
      }
      
      return buffer;
    }
  }

  async updateEnhancedTrayMenu() {
    try {
      // Check if tray exists
      if (!this.tray) {
        console.log('⚠️ Tray not initialized, skipping menu update');
        return;
      }

      const games = await this.gameDetector.getDetectedGames();
      const serverUrl = this.store.get('serverUrl', 'http://127.0.0.1:3001');
      
      // Build enhanced menu template
      const template = [
        // Header with status
        {
          label: `🏰 WC Arena Core`,
          type: 'normal',
          enabled: false
        },
        {
          label: this.getStatusLabel(),
          type: 'normal',
          enabled: false
        },
        { type: 'separator' },
        
        // Quick Actions
        {
          label: '🚀 Open Arena Core',
          type: 'normal',
          click: () => this.toggleMainWindow()
        },
        {
          label: '🏕️ Open Garrison',
          type: 'normal',
          click: () => this.openGarrison()
        },
        {
          label: '📊 View Statistics',
          type: 'normal',
          click: () => this.openStatistics()
        },
        
        { type: 'separator' },
        
        // Garrison Integration
        this.buildGarrisonMenu(),
        
        { type: 'separator' },
        
        // Games section with enhanced styling
        this.buildEnhancedGamesMenu(games),
        
        { type: 'separator' },
        
        // Enhanced Actions
        {
          label: '🔍 Find Games',
          type: 'normal',
          click: () => this.findGames()
        },
        {
          label: '🔄 Refresh Games',
          type: 'normal',
          click: () => this.refreshGames()
        },
        {
          label: '📸 Screenshots',
          type: 'submenu',
          submenu: [
            {
              label: '📁 Open Screenshots Folder',
              type: 'normal',
              click: () => this.openScreenshotsFolder()
            },
            {
              label: '📊 View Screenshot Stats',
              type: 'normal',
              click: () => this.openScreenshotStats()
            }
          ]
        },
        
        { type: 'separator' },
        
        // Settings and info
        {
          label: '⚙️ Settings',
          type: 'normal',
          click: () => this.openSettings()
        },
        {
          label: 'ℹ️ About',
          type: 'normal',
          click: () => this.showAbout()
        },
        
        { type: 'separator' },
        
        // Exit
        {
          label: '🚪 Quit',
          type: 'normal',
          click: () => this.quitApp()
        }
      ];

      const contextMenu = Menu.buildFromTemplate(template);
      
      // Double-check tray exists before setting context menu
      if (this.tray) {
        this.tray.setContextMenu(contextMenu);
      } else {
        console.warn('⚠️ Tray is null, cannot set context menu');
      }
    } catch (error) {
      console.error('❌ Error updating tray menu:', error);
    }
  }

  buildGarrisonMenu() {
    const garrisonItems = [
      {
        label: '💬 Chat',
        type: 'submenu',
        submenu: [
          {
            label: '🌍 Global Chat',
            type: 'normal',
            click: () => this.openGarrisonChat('global')
          },
          {
            label: '🎮 Game Chat',
            type: 'normal',
            click: () => this.openGarrisonChat('game')
          },
          {
            label: '👥 Clan Chat',
            type: 'normal',
            click: () => this.openGarrisonChat('clan')
          }
        ]
      },
      {
        label: '👥 Friends',
        type: 'submenu',
        submenu: [
          {
            label: '👤 View Friends',
            type: 'normal',
            click: () => this.openFriendsList()
          },
          {
            label: '➕ Add Friend',
            type: 'normal',
            click: () => this.addFriend()
          }
        ]
      },
      {
        label: '🔔 Notifications',
        type: 'normal',
        click: () => this.openNotifications()
      }
    ];

    // Add notification badge if there are unread messages
    if (this.garrisonStatus.unreadMessages > 0) {
      garrisonItems[0].label += ` (${this.garrisonStatus.unreadMessages})`;
    }

    return {
      label: '🏕️ Garrison',
      type: 'submenu',
      submenu: garrisonItems
    };
  }

  buildEnhancedGamesMenu(games) {
    if (games.length === 0) {
      return {
        label: '🎮 No Games Found',
        type: 'normal',
        enabled: false
      };
    }

    // Group games by type with enhanced styling
    const gamesByType = {
      warcraft1: games.filter(g => g.type === 'warcraft1'),
      warcraft2: games.filter(g => g.type === 'warcraft2'),
      warcraft3: games.filter(g => g.type === 'warcraft3'),
      w3champions: games.filter(g => g.type === 'w3champions'),
      battlenet: games.filter(g => g.type === 'battlenet')
    };

    const submenu = [];

    // Add Warcraft 1 games
    if (gamesByType.warcraft1.length > 0) {
      submenu.push({
        label: '🐉 Warcraft I: Orcs & Humans',
        type: 'submenu',
        submenu: gamesByType.warcraft1.map(game => ({
          label: `⚔️ ${game.name || `Warcraft I (${game.executable})`}`,
          type: 'normal',
          click: () => this.launchGame(game.id)
        }))
      });
    }

    // Add Warcraft 2 games
    if (gamesByType.warcraft2.length > 0) {
      submenu.push({
        label: '⚓ Warcraft II: Tides of Darkness',
        type: 'submenu',
        submenu: this.buildWarcraft2Submenu(gamesByType.warcraft2)
      });
    }

    // Add Warcraft 3 games
    if (gamesByType.warcraft3.length > 0) {
      submenu.push({
        label: '👑 Warcraft III',
        type: 'submenu',
        submenu: gamesByType.warcraft3.map(game => ({
          label: `⚔️ ${game.name || `Warcraft III (${game.executable})`}`,
          type: 'normal',
          click: () => this.launchGame(game.id)
        }))
      });
    }

    return {
      label: '🎮 Games',
      type: 'submenu',
      submenu: submenu
    };
  }

  buildWarcraft2Submenu(wc2Games) {
    const wc2Groups = this.groupSimilarGames(wc2Games);
    
    return wc2Groups.map(group => {
      if (group.games.length === 1) {
        const game = group.games[0];
        return {
          label: `⚔️ ${game.name || `Warcraft II (${game.executable})`}`,
          type: 'submenu',
          submenu: this.createEnhancedGameSubmenu(game)
        };
      } else {
        return {
          label: `⚔️ ${group.name}`,
          type: 'submenu',
          submenu: group.games.map(game => ({
            label: `🎯 ${game.name || game.executable}`,
            type: 'normal',
            click: () => this.launchGame(game.id)
          }))
        };
      }
    });
  }

  createEnhancedGameSubmenu(game) {
    return [
      {
        label: '🚀 Launch Game',
        type: 'normal',
        click: () => this.launchGame(game.id)
      },
      {
        label: '🗺️ Open Maps Folder',
        type: 'normal',
        click: () => this.openMapsFolder(game)
      },
      {
        label: '📸 Screenshot Folder',
        type: 'normal',
        click: () => this.openGameScreenshots(game)
      }
    ];
  }

  getStatusLabel() {
    const now = Date.now();
    const timeSinceActivity = Math.floor((now - this.lastActivity) / 1000 / 60); // minutes
    
    switch (this.statusIndicator) {
      case 'gaming':
        return `🎮 Gaming (${timeSinceActivity}m)`;
      case 'online':
        return `🟢 Online`;
      case 'offline':
        return `🔴 Offline`;
      case 'notification':
        return `🔔 ${this.notificationCount} notifications`;
      default:
        return `⚪ Idle (${timeSinceActivity}m)`;
    }
  }

  // Garrison Integration Methods
  openGarrison() {
    if (this.garrisonWindow && !this.garrisonWindow.isDestroyed()) {
      this.garrisonWindow.show();
      this.garrisonWindow.focus();
    } else {
      this.createGarrisonWindow();
    }
  }

  createGarrisonWindow() {
    this.garrisonWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      title: 'WC Arena Garrison',
      icon: path.join(__dirname, '..', 'assets', 'icon.png'),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false
      },
      show: false
    });

    const serverUrl = this.store.get('serverUrl', 'http://127.0.0.1:3001');
    this.garrisonWindow.loadURL(`${serverUrl}/views/garrison.html`);

    this.garrisonWindow.once('ready-to-show', () => {
      this.garrisonWindow.show();
    });

    this.garrisonWindow.on('closed', () => {
      this.garrisonWindow = null;
    });
  }

  openGarrisonChat(chatType) {
    const serverUrl = this.store.get('serverUrl', 'http://127.0.0.1:3001');
    shell.openExternal(`${serverUrl}/views/garrison.html?chat=${chatType}`);
  }

  openFriendsList() {
    const serverUrl = this.store.get('serverUrl', 'http://127.0.0.1:3001');
    shell.openExternal(`${serverUrl}/views/friends.html`);
  }

  addFriend() {
    // Open friend request dialog
    dialog.showMessageBox(this.windowManager.mainWindow, {
      type: 'info',
      title: 'Add Friend',
      message: 'Friend functionality is available in the web interface.',
      detail: 'Please use the web interface to add friends.'
    });
  }

  openNotifications() {
    const serverUrl = this.store.get('serverUrl', 'http://127.0.0.1:3001');
    shell.openExternal(`${serverUrl}/views/notifications.html`);
  }

  openStatistics() {
    const serverUrl = this.store.get('serverUrl', 'http://127.0.0.1:3001');
    shell.openExternal(`${serverUrl}/views/statistics.html`);
  }

  openScreenshotStats() {
    const serverUrl = this.store.get('serverUrl', 'http://127.0.0.1:3001');
    shell.openExternal(`${serverUrl}/views/screenshot-stats.html`);
  }

  // Status Monitoring
  startStatusMonitoring() {
    setInterval(() => {
      this.updateStatus();
      this.updateTrayIcon();
    }, 30000); // Update every 30 seconds
  }

  updateStatus() {
    // Check if any games are running
    const runningGames = this.gameDetector.getRunningGames();
    if (runningGames.length > 0) {
      this.statusIndicator = 'gaming';
      this.lastActivity = Date.now();
    } else if (this.garrisonStatus.online) {
      this.statusIndicator = 'online';
    } else {
      this.statusIndicator = 'idle';
    }

    // Check for notifications
    if (this.garrisonStatus.notifications > 0) {
      this.statusIndicator = 'notification';
      this.notificationCount = this.garrisonStatus.notifications;
    }
  }

  updateTrayIcon() {
    if (this.tray) {
      const newIcon = this.createDynamicTrayIcon();
      this.tray.setImage(newIcon);
    }
  }

  // Update garrison status from external sources
  updateGarrisonStatus(status) {
    this.garrisonStatus = { ...this.garrisonStatus, ...status };
    this.updateStatus();
    this.updateEnhancedTrayMenu();
  }

  // Inherited methods from original TrayManager
  toggleMainWindow() {
    if (this.windowManager.mainWindow) {
      if (this.windowManager.mainWindow.isVisible()) {
        this.windowManager.mainWindow.hide();
      } else {
        this.windowManager.show();
      }
    } else {
      this.windowManager.createMainWindow();
      this.windowManager.loadMainApp();
    }
  }

  async launchGame(gameId) {
    try {
      await this.gameDetector.launchGame(gameId);
      this.statusIndicator = 'gaming';
      this.lastActivity = Date.now();
      this.updateTrayIcon();
    } catch (error) {
      console.error('Failed to launch game:', error);
      dialog.showErrorBox('Launch Error', `Failed to launch game: ${error.message}`);
    }
  }

  async findGames() {
    try {
      await this.gameDetector.findGames();
      this.updateEnhancedTrayMenu();
      dialog.showMessageBox(this.windowManager.mainWindow, {
        type: 'info',
        title: 'Game Detection',
        message: 'Game detection completed!'
      });
    } catch (error) {
      console.error('Game detection failed:', error);
      dialog.showErrorBox('Detection Error', `Game detection failed: ${error.message}`);
    }
  }

  async refreshGames() {
    await this.findGames();
  }

  openSettings() {
    if (this.windowManager.mainWindow) {
      this.windowManager.mainWindow.webContents.send('open-settings');
    }
  }

  showAbout() {
    dialog.showMessageBox(this.windowManager.mainWindow, {
      type: 'info',
      title: 'About WC Arena Core',
      message: 'WC Arena Core - Next Level Gaming Hub',
      detail: 'Version 2.0\nEnhanced with Garrison Integration\n\nA comprehensive gaming companion for Warcraft RTS games.'
    });
  }

  quitApp() {
    this.isQuitting = true;
    this.app.quit();
  }

  setQuitting(isQuitting) {
    this.isQuitting = isQuitting;
  }

  isAppQuitting() {
    return this.isQuitting;
  }

  destroy() {
    if (this.tray) {
      this.tray.destroy();
      this.tray = null;
    }
    if (this.garrisonWindow && !this.garrisonWindow.isDestroyed()) {
      this.garrisonWindow.destroy();
    }
  }

  handleWindowMinimize() {
    if (this.store.get('minimizeToTray', true)) {
      this.windowManager.mainWindow.hide();
      this.updateTooltip();
    }
  }

  handleWindowClose(event) {
    if (this.store.get('closeToTray', true) && !this.isQuitting) {
      event.preventDefault();
      this.windowManager.mainWindow.hide();
      this.updateTooltip();
    }
  }

  updateTooltip() {
    if (this.tray) {
      const status = this.getStatusLabel();
      this.tray.setToolTip(`WC Arena Core - ${status}`);
    }
  }

  openScreenshotsFolder() {
    const screenshotsPath = this.screenshotManager.getScreenshotsPath();
    shell.openPath(screenshotsPath);
  }

  openMapsFolder(game) {
    const mapsPath = this.getMapsFolder(game);
    if (mapsPath && fs.existsSync(mapsPath)) {
      shell.openPath(mapsPath);
    } else {
      dialog.showMessageBox(this.windowManager.mainWindow, {
        type: 'warning',
        title: 'Maps Folder',
        message: 'Maps folder not found',
        detail: `Could not locate maps folder for ${game.name || game.executable}`
      });
    }
  }

  openGameScreenshots(game) {
    const gameScreenshotsPath = path.join(this.screenshotManager.getScreenshotsPath(), game.type);
    if (fs.existsSync(gameScreenshotsPath)) {
      shell.openPath(gameScreenshotsPath);
    } else {
      dialog.showMessageBox(this.windowManager.mainWindow, {
        type: 'info',
        title: 'Screenshots',
        message: 'No screenshots found',
        detail: `No screenshots found for ${game.name || game.executable}`
      });
    }
  }

  getMapsFolder(game) {
    const gamePath = path.dirname(game.path);
    
    // Common maps folder locations
    const possiblePaths = [
      path.join(gamePath, 'maps'),
      path.join(gamePath, 'Maps'),
      path.join(gamePath, 'data', 'maps'),
      path.join(gamePath, 'Data', 'Maps'),
      path.join(gamePath, '..', 'maps'),
      path.join(gamePath, '..', 'Maps')
    ];
    
    for (const mapsPath of possiblePaths) {
      if (fs.existsSync(mapsPath)) {
        return mapsPath;
      }
    }
    
    return null;
  }

  groupSimilarGames(games) {
    const groups = {};
    
    games.forEach(game => {
      const key = game.name || game.executable;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(game);
    });
    
    return Object.entries(groups).map(([name, games]) => ({
      name,
      games
    }));
  }
}

module.exports = { EnhancedTrayManager }; 