const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose safe APIs to the renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  // Authentication methods (updated for simplified auth manager)
  auth: {
    login: (provider) => ipcRenderer.invoke('auth:login', provider),
    logout: () => ipcRenderer.invoke('auth:logout'),
    getUser: () => ipcRenderer.invoke('auth:get-user'),
    isAuthenticated: () => ipcRenderer.invoke('auth:is-authenticated'),
    getCurrentUser: () => ipcRenderer.invoke('auth:get-current-user'),
    getToken: () => ipcRenderer.invoke('auth:get-token')
  },

  // Navigation methods
  navigation: {
    loadPage: (page) => ipcRenderer.invoke('navigation:load-page', page)
  },

  // Configuration methods
  config: {
    get: () => ipcRenderer.invoke('config:get'),
    update: (config) => ipcRenderer.invoke('config:update', config)
  },

  // Game detection and launching
  games: {
    find: () => ipcRenderer.invoke('games:find'),
    getDetected: () => ipcRenderer.invoke('games:get-detected'),
    launch: (gameId) => ipcRenderer.invoke('games:launch', gameId),
    findAll: () => ipcRenderer.invoke('games:find-all'),
    addManual: (game) => ipcRenderer.invoke('games:add-manual', game),
    removeManual: (gameId) => ipcRenderer.invoke('games:remove-manual', gameId),
    getByType: (type) => ipcRenderer.invoke('games:get-by-type', type),
    getManual: () => ipcRenderer.invoke('games:get-manual'),
    updateManual: (gameId, game) => ipcRenderer.invoke('games:update-manual', gameId, game),
    openMapsFolder: (gameType) => ipcRenderer.invoke('games:open-maps-folder', gameType)
  },

  // Screenshot management
  screenshots: {
    getPath: () => ipcRenderer.invoke('screenshots:get-path'),
    openFolder: () => ipcRenderer.invoke('screenshots:open-folder'),
    take: () => ipcRenderer.invoke('screenshots:take'),
    getRecent: (limit) => ipcRenderer.invoke('screenshots:get-recent', limit),
    getStats: () => ipcRenderer.invoke('screenshots:get-stats'),
    getAnalysisTools: () => ipcRenderer.invoke('screenshots:get-analysis-tools'),
    analyzeImage: (imagePath) => ipcRenderer.invoke('screenshots:analyze-image', imagePath)
  },

  // Match management
  matches: {
    getRecent: (limit) => ipcRenderer.invoke('matches:get-recent', limit),
    getStats: () => ipcRenderer.invoke('matches:get-stats'),
    processManual: (matchData) => ipcRenderer.invoke('matches:process-manual', matchData)
  },

  // Dialog methods
  dialog: {
    openFile: (options) => ipcRenderer.invoke('dialog:open-file', options)
  },

  // Settings methods
  settings: {
    get: () => ipcRenderer.invoke('settings:get'),
    set: (settings) => ipcRenderer.invoke('settings:set', settings)
  },

  // Window management
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    close: () => ipcRenderer.invoke('window:close')
  },

  // App information
  app: {
    getInfo: () => ipcRenderer.invoke('app:get-info')
  },

  // Event listeners for IPC messages from main process
  onOAuthSuccess: (callback) => {
    ipcRenderer.on('oauth-success', (event, data) => callback(data));
  },

  onOAuthError: (callback) => {
    ipcRenderer.on('oauth-error', (event, data) => callback(data));
  },

  onOAuthTimeout: (callback) => {
    ipcRenderer.on('oauth-timeout', (event, data) => callback(data));
  },

  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // Check if running in Electron
  isElectron: true,

  // Platform information
  platform: process.platform,

  // Version information
  versions: {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron
  },

  // Generic invoke method for any IPC calls
  invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
  
  // Generic event listener
  on: (channel, callback) => {
    ipcRenderer.on(channel, (event, ...args) => callback(...args));
  }
});

// Initialize electron-specific features when DOM is ready
window.addEventListener('DOMContentLoaded', () => {
  // Add electron class to body for CSS targeting
  document.body.classList.add('electron-app');
  
  // Add platform-specific class
  document.body.classList.add(`platform-${process.platform}`);
  
  // Override console methods to include Electron prefix
  const originalLog = console.log;
  const originalError = console.error;
  const originalWarn = console.warn;
  
  console.log = (...args) => originalLog('[Electron Renderer]', ...args);
  console.error = (...args) => originalError('[Electron Renderer]', ...args);
  console.warn = (...args) => originalWarn('[Electron Renderer]', ...args);
  
  console.log('Electron preload script loaded successfully');
});

// Handle uncaught errors in the renderer process
window.addEventListener('error', (event) => {
  console.error('Uncaught error in renderer:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection in renderer:', event.reason);
}); 