/**
 * PlatformManager.js
 * Handles platform detection and feature availability across desktop and web versions
 */

class PlatformManager {
  constructor() {
    this.isElectron = this._detectElectron();
    this.platform = this._detectPlatform();
    this.features = this._initializeFeatures();
    
    // Add platform classes to body
    this._addPlatformClasses();
    
    console.log('🖥️ Platform Manager Initialized:', {
      isElectron: this.isElectron,
      platform: this.platform,
      features: this.features
    });
  }

  /**
   * Detect if running in Electron
   */
  _detectElectron() {
    return !!(window.electronAPI && window.electronAPI.isElectron);
  }

  /**
   * Detect platform (win32, darwin, linux, or web)
   */
  _detectPlatform() {
    if (this.isElectron) {
      return window.electronAPI.platform;
    }
    return 'web';
  }

  /**
   * Initialize feature availability based on platform
   */
  _initializeFeatures() {
    const baseFeatures = {
      gameDetection: false,
      screenshotCapture: false,
      matchTracking: false,
      systemTray: false,
      nativeDialogs: false,
      autoLaunch: false
    };

    if (!this.isElectron) {
      return baseFeatures;
    }

    // Enable features for desktop app
    return {
      ...baseFeatures,
      gameDetection: true,
      screenshotCapture: true,
      matchTracking: true,
      systemTray: true,
      nativeDialogs: true,
      autoLaunch: true
    };
  }

  /**
   * Add platform-specific classes to body
   */
  _addPlatformClasses() {
    document.body.classList.add(this.isElectron ? 'electron-app' : 'web-app');
    document.body.classList.add(`platform-${this.platform}`);
  }

  /**
   * Check if a specific feature is available
   */
  hasFeature(featureName) {
    return !!this.features[featureName];
  }

  /**
   * Get all available features
   */
  getFeatures() {
    return { ...this.features };
  }

  /**
   * Check if running on desktop app
   */
  isDesktopApp() {
    return this.isElectron;
  }

  /**
   * Check if running on web
   */
  isWebApp() {
    return !this.isElectron;
  }

  /**
   * Get current platform
   */
  getPlatform() {
    return this.platform;
  }

  /**
   * Check if running on Windows
   */
  isWindows() {
    return this.platform === 'win32';
  }

  /**
   * Check if running on macOS
   */
  isMacOS() {
    return this.platform === 'darwin';
  }

  /**
   * Check if running on Linux
   */
  isLinux() {
    return this.platform === 'linux';
  }

  /**
   * Get appropriate API for the current platform
   */
  getAPI() {
    if (this.isElectron) {
      return window.electronAPI;
    }
    return window.webAPI; // Fallback to web API
  }
}

// Create and export singleton instance
const platformManager = new PlatformManager();
export default platformManager; 