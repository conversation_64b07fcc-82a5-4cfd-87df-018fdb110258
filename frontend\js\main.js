// CRITICAL DEBUGGING: Main.js loading and execution
console.log('🚀 MAIN.JS LOADING: main.js script is executing');
console.log('🚀 Current page URL:', window.location.href);
console.log('🚀 Document ready state:', document.readyState);
console.log('🚀 Available navbar container:', !!document.getElementById('navbar-container'));

// Load the application core first to ensure ApiClient is available
async function loadApplicationCore() {
  try {
    console.log('📦 Loading application core...');
    const appModule = await import('./app.js');
    
    if (appModule.default && typeof appModule.default === 'function') {
      const app = new appModule.default();
      await app.init();
      console.log('✅ Application core loaded successfully');
    } else if (appModule.default && typeof appModule.default === 'object') {
      // Singleton instance
      console.log('✅ Application core (singleton) loaded successfully');
    } else {
      throw new Error('Invalid application module export');
    }
  } catch (error) {
    console.error('❌ Failed to load application core:', error);
    console.log('🔄 Continuing with fallback initialization...');
    
    // Fallback: Load critical modules individually
    try {
      await loadCriticalModulesFallback();
    } catch (fallbackError) {
      console.error('❌ Fallback initialization also failed:', fallbackError);
    }
  }
}

async function loadCriticalModulesFallback() {
  console.log('🔄 Loading critical modules with fallback method...');
  
  // Load ApiClient
  try {
    const apiModule = await import('./modules/ApiClient.js');
    if (apiModule.default) {
      window.apiClient = apiModule.default;
      console.log('✅ ApiClient loaded via fallback');
    }
  } catch (error) {
    console.error('❌ Failed to load ApiClient via fallback:', error);
  }
  
  // Load AuthManager
  try {
    const authModule = await import('./modules/AuthManager.js');
    if (authModule.default) {
      window.authManager = new authModule.default();
      console.log('✅ AuthManager loaded via fallback');
    }
  } catch (error) {
    console.error('❌ Failed to load AuthManager via fallback:', error);
  }
  
  // Load UIManager
  try {
    const uiModule = await import('./modules/UIManager.js');
    if (uiModule.default) {
      window.uiManager = new uiModule.default();
      console.log('✅ UIManager loaded via fallback');
    }
  } catch (error) {
    console.error('❌ Failed to load UIManager via fallback:', error);
  }
}

// Load application core immediately
// Temporarily disabled due to export issues
// loadApplicationCore();

// Detect if we're in an Electron iframe (used by both API bridge and auth handler)
// Check BEFORE any URL manipulation happens
const currentUrl = window.location.href;
const urlParams = new URLSearchParams(window.location.search);
const isInIframe = window.parent !== window;

// Flag to prevent multiple auth requests
let authRequested = false;

// Function to detect Electron environment (can be called during navigation)
function detectElectronEnvironment() {
  const hasElectronParam = urlParams.get('electron') === 'true';
  const hasElectronApp = currentUrl.includes('electronApp=') || currentUrl.includes('WC_Arena_Companion');
  const hasElectronStorage = localStorage.getItem('authToken') && localStorage.getItem('authToken').length > 200; // JWT tokens are long
  let parentHasElectronAPI = false;
  try {
    parentHasElectronAPI = isInIframe && window.parent && window.parent.electronAPI !== undefined;
  } catch (e) {
    // Cross-origin iframe may prevent access
    parentHasElectronAPI = false;
  }

  const isElectronIframe = isInIframe && (hasElectronParam || hasElectronApp || hasElectronStorage || parentHasElectronAPI);
  const isElectronDirect = !isInIframe && (hasElectronParam || hasElectronApp || hasElectronStorage);
  const isElectronMode = isElectronIframe || isElectronDirect;
  
  return { isElectronIframe, isElectronDirect, isElectronMode };
}

// Initial detection
const { isElectronIframe, isElectronDirect, isElectronMode } = detectElectronEnvironment();

// Global variables for Electron detection

// Function to update Electron mode flags during navigation
function updateElectronModeFlags() {
  const { isElectronIframe: newIsElectronIframe, isElectronDirect: newIsElectronDirect, isElectronMode: newIsElectronMode } = detectElectronEnvironment();
  
  // Update global flags
  window.isElectronMode = newIsElectronMode;
  
  // If we're in Electron mode, ensure the auth flag is set
  if (newIsElectronMode && !window.electronAuthEstablished) {
    console.log('🖥️ Electron mode detected during navigation - setting up auth blocking');
    window.electronAuthEstablished = false;
  }
  
  console.log('🔍 Electron detection updated during navigation:', {
    isElectronIframe: newIsElectronIframe,
    isElectronDirect: newIsElectronDirect,
    isElectronMode: newIsElectronMode
  });
}

// Log the detection results
console.log('🔍 Initial Electron detection:', {
  isInIframe: isInIframe,
  hasElectronParam: urlParams.get('electron') === 'true',
  hasElectronApp: currentUrl.includes('electronApp=') || currentUrl.includes('WC_Arena_Companion'),
  hasElectronStorage: localStorage.getItem('authToken') && localStorage.getItem('authToken').length > 200,
  currentUrl: currentUrl,
  isElectronIframe: isElectronIframe,
  isElectronDirect: isElectronDirect,
  finalResult: isElectronMode
});

// Electron API Bridge function
function initElectronAPIBridge() {
  if (!isElectronMode) {
    console.log('🔍 Not in Electron environment, skipping API bridge');
    return;
  }
  
  console.log('🖥️ Detected Electron environment - setting up API bridge');
  
  class ElectronAPIBridge {
    constructor() {
      this.isElectronIframe = true;
      this.requestId = 0;
      this.pendingRequests = new Map();
      this.electronAPI = null;
      
      this.setupMessageListener();
      this.createProxyAPI();
      console.log('🔌 Electron API Bridge initialized successfully');
    }
   
    setupMessageListener() {
      window.addEventListener('message', (event) => {
        if (event.data.type === 'ELECTRON_API_RESPONSE') {
          const { requestId, success, result, error } = event.data;
          const pendingRequest = this.pendingRequests.get(requestId);
          
          if (pendingRequest) {
            this.pendingRequests.delete(requestId);
            
            if (success) {
              pendingRequest.resolve(result);
            } else {
              pendingRequest.reject(new Error(error));
            }
          }
        } else if (event.data.type === 'ELECTRON_EVENT') {
          // Handle event notifications from parent window
          const { channel, data } = event.data;
          const listeners = this.eventListeners.get(channel);
          
          if (listeners) {
            listeners.forEach(callback => {
              try {
                callback(data);
              } catch (error) {
                console.error(`Error in event listener for channel ${channel}:`, error);
              }
            });
          }
        }
      });
    }
    
    async callAPI(api, method, ...args) {
      return new Promise((resolve, reject) => {
        const requestId = ++this.requestId;
        
        // Store the request
        this.pendingRequests.set(requestId, { resolve, reject });
        
        // Send the API call to parent window
        window.parent.postMessage({
          type: 'ELECTRON_API_CALL',
          requestId: requestId,
          api: api,
          method: method,
          args: args
        }, '*');
        
        // Add timeout
        setTimeout(() => {
          if (this.pendingRequests.has(requestId)) {
            this.pendingRequests.delete(requestId);
            reject(new Error(`API call timeout: ${api}.${method}`));
          }
        }, 30000); // 30 second timeout
      });
    }
    
    createProxyAPI() {
      // Store event listeners
      this.eventListeners = new Map();
      
      this.electronAPI = {
        // Core Electron API methods
        invoke: (channel, ...args) => this.callAPI('core', 'invoke', channel, ...args),
        on: (channel, callback) => {
          // Store the listener locally
          if (!this.eventListeners.has(channel)) {
            this.eventListeners.set(channel, []);
          }
          this.eventListeners.get(channel).push(callback);
          
          // Register with parent window
          window.parent.postMessage({
            type: 'ELECTRON_EVENT_LISTENER',
            action: 'add',
            channel: channel
          }, '*');
          
          return this.electronAPI; // Return for chaining
        },
        once: (channel, callback) => {
          const onceCallback = (...args) => {
            callback(...args);
            this.electronAPI.removeListener(channel, onceCallback);
          };
          return this.electronAPI.on(channel, onceCallback);
        },
        removeListener: (channel, callback) => {
          const listeners = this.eventListeners.get(channel);
          if (listeners) {
            const index = listeners.indexOf(callback);
            if (index > -1) {
              listeners.splice(index, 1);
            }
          }
          
          // Unregister with parent window
          window.parent.postMessage({
            type: 'ELECTRON_EVENT_LISTENER',
            action: 'remove',
            channel: channel
          }, '*');
          
          return this.electronAPI; // Return for chaining
        },
        removeAllListeners: (channel) => {
          this.eventListeners.delete(channel);
          
          // Unregister with parent window
          window.parent.postMessage({
            type: 'ELECTRON_EVENT_LISTENER',
            action: 'removeAll',
            channel: channel
          }, '*');
          
          return this.electronAPI; // Return for chaining
        },
        
        // Games API
        games: {
          findAll: () => this.callAPI('games', 'findAll'),
          getDetected: () => this.callAPI('games', 'getDetected'),
          launch: (gameId) => this.callAPI('games', 'launch', gameId),
          addManual: (gameData) => this.callAPI('games', 'addManual', gameData),
          removeManual: (gameId) => this.callAPI('games', 'removeManual', gameId),
          getByType: (gameType) => this.callAPI('games', 'getByType', gameType),
          getManual: () => this.callAPI('games', 'getManual'),
          updateManual: (gameId, gameData) => this.callAPI('games', 'updateManual', gameId, gameData),
          openMapsFolder: (gameId) => this.callAPI('games', 'openMapsFolder', gameId)
        },
        
        // Screenshots API
        screenshots: {
          openFolder: () => this.callAPI('screenshots', 'openFolder'),
          take: () => this.callAPI('screenshots', 'take'),
          getRecent: (limit) => this.callAPI('screenshots', 'getRecent', limit),
          getStats: () => this.callAPI('screenshots', 'getStats'),
          getAnalysisTools: () => this.callAPI('screenshots', 'getAnalysisTools'),
          analyzeImage: (imagePath) => this.callAPI('screenshots', 'analyzeImage', imagePath)
        },
        
        // Matches API
        matches: {
          getRecent: (limit) => this.callAPI('matches', 'getRecent', limit),
          getStats: () => this.callAPI('matches', 'getStats'),
          processManual: (data) => this.callAPI('matches', 'processManual', data)
        },
        
        // Dialog API
        dialog: {
          openFile: (options) => this.callAPI('dialog', 'openFile', options)
        },
        
        // Settings API
        settings: {
          get: () => this.callAPI('settings', 'get'),
          set: (settings) => this.callAPI('settings', 'set', settings)
        },
        
        // Arena Core API
        arenaCore: {
          requestData: () => this.callAPI('arenaCore', 'requestData')
        },
        
        // Utility properties
        isElectron: true,
        platform: 'win32'
      };
      
      // Expose to window immediately
      window.electronAPI = this.electronAPI;
      console.log('✅ Electron API proxy created and attached to window');
      console.log('🔍 Available APIs:', Object.keys(this.electronAPI));
      console.log('🔍 Core Electron methods available:', {
        invoke: typeof this.electronAPI.invoke,
        on: typeof this.electronAPI.on,
        once: typeof this.electronAPI.once,
        removeListener: typeof this.electronAPI.removeListener,
        removeAllListeners: typeof this.electronAPI.removeAllListeners
      });
      console.log('🔍 Games API available:', !!this.electronAPI.games);
      console.log('🔍 Screenshots API available:', !!this.electronAPI.screenshots);
      console.log('🔍 Matches API available:', !!this.electronAPI.matches);
    }
  }
  
  // Initialize the bridge
  new ElectronAPIBridge();
  
  // Set up authentication listener for ApiClient
  window.addEventListener('electronAuthReceived', (event) => {
    console.log('🔐 Main.js: Received Electron authentication, refreshing ApiClient');
    if (window.apiClient && window.apiClient.refreshAuthToken) {
      window.apiClient.refreshAuthToken();
    }
  });
}

// Initialize the bridge immediately if in Electron iframe
if (isElectronIframe) {
  initElectronAPIBridge();
  
  // Set up global authentication listener
  window.addEventListener('electronAuthReceived', (event) => {
    console.log('🔐 Main.js: Received Electron authentication, refreshing ApiClient');
    if (window.apiClient && window.apiClient.refreshAuthToken) {
      window.apiClient.refreshAuthToken();
    }
  });
} else if (window.parent !== window) {
  // Fallback: Try to detect Electron by attempting communication with parent
  console.log('🔍 Initial detection failed, trying fallback Electron detection...');
  
  setTimeout(() => {
    // Request authentication from parent - if it responds, we're likely in Electron
    if (window.parent !== window && !authRequested) {
      authRequested = true;
      window.parent.postMessage({ type: 'REQUEST_AUTH_TOKEN' }, '*');
      
      // Listen for response for a short time
      const fallbackListener = (event) => {
        if (event.data.type === 'AUTH_TOKEN_RESPONSE' || event.data.type === 'ELECTRON_AUTH') {
          console.log('✅ Fallback detection: Found Electron parent, initializing API bridge');
          window.removeEventListener('message', fallbackListener);
          initElectronAPIBridge();
        }
      };
      
      window.addEventListener('message', fallbackListener);
      
      // Remove listener after 2 seconds if no response
      setTimeout(() => {
        window.removeEventListener('message', fallbackListener);
      }, 2000);
    }
  }, 500);
}

// Helper function to extract JWT token from URL parameters
function extractJWTFromURL() {
  const authToken = urlParams.get('authToken');
  
  console.log('🔍 Checking URL for authToken:', { 
    hasToken: !!authToken, 
    tokenLength: authToken ? authToken.length : 0,
    url: window.location.href 
  });
  
  if (authToken) {
    console.log('🔑 JWT token found in URL, storing for API requests');
    localStorage.setItem('authToken', authToken);
    
    // Set cookie for backend requests (no secure flag for localhost)
    const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    const cookieSecure = isLocalhost ? '' : 'secure;';
    document.cookie = `authToken=${authToken}; path=/; ${cookieSecure} samesite=strict`;
    
    console.log('🍪 Cookie set for authToken, length:', authToken.length);
    
    // Clean up URL but preserve important Electron parameters
    urlParams.delete('authToken');
    // Keep electron, electronApp, and user parameters for proper iframe detection
    const newUrl = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
    window.history.replaceState({}, document.title, newUrl);
    
    return authToken;
  }
  
  return null;
}

// Initialize JWT token extraction immediately
const urlToken = extractJWTFromURL();
if (urlToken) {
  console.log('✅ JWT token extracted from URL and stored');
  
  // Force a user data refresh once the token is available
  setTimeout(async () => {
    if (typeof window.loadUser === 'function') {
      console.log('🔄 Refreshing user data with extracted JWT token...');
      await window.loadUser();
    }
  }, 100);
}

// Helper function to make authenticated API requests
function getAuthHeaders() {
  const authToken = localStorage.getItem('authToken');
  const headers = {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  };
  
  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`;
    console.log('🔑 Adding Authorization header to request, token length:', authToken.length);
  } else {
    console.log('⚠️ No authToken found in localStorage');
  }
  
  return headers;
}

// Make getAuthHeaders available globally for modules
window.getAuthHeaders = getAuthHeaders;

// Helper function to make authenticated fetch requests using ApiClient
async function authenticatedFetch(url, options = {}) {
  // Check if logout is in progress
  if (window.isLoggingOut) {
    console.log('🛑 API call blocked during logout:', url);
    throw new Error('API calls blocked during logout');
  }
  
  // Check if we're in Electron mode and authentication hasn't been established yet
  if (window.isElectronMode && !window.electronAuthEstablished) {
    console.log('🛑 API call blocked in Electron mode - authentication not established:', url);
    throw new Error('API calls blocked in Electron mode - authentication not established');
  }
  
  try {
    // Check if ApiClient is available
    if (!window.apiClient) {
      console.warn('⚠️ ApiClient not available, falling back to direct fetch');
      // Fallback to direct fetch with auth headers
      const defaultOptions = {
        credentials: 'include',
        headers: getAuthHeaders()
      };
      
      const mergedOptions = {
        ...defaultOptions,
        ...options,
        headers: {
          ...defaultOptions.headers,
          ...(options.headers || {})
        }
      };
      
      const response = await fetch(url, mergedOptions);
      
      // Handle 401/403 errors
      if ((response.status === 401 || response.status === 403) && !window.isLoggingOut) {
        console.log('🚨 Authentication error detected, clearing auth state');
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
        
        const currentPath = window.location.pathname;
        if (!currentPath.includes('/views/login.html')) {
          console.log('🔄 Redirecting to login due to auth error');
          window.location.href = '/views/login.html';
        }
      }
      
      return response;
    }
    
    // Use ApiClient for all API requests
    const api = window.apiClient;
    
    // Extract the endpoint from the full URL
    const endpoint = url.replace(window.location.origin, '');
    
    // Determine the HTTP method
    const method = options.method || 'GET';
    
    let response;
    switch (method.toUpperCase()) {
      case 'GET':
        response = await api.get(endpoint);
        break;
      case 'POST':
        response = await api.post(endpoint, options.body ? JSON.parse(options.body) : {});
        break;
      case 'PUT':
        response = await api.put(endpoint, options.body ? JSON.parse(options.body) : {});
        break;
      case 'DELETE':
        response = await api.delete(endpoint);
        break;
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
    
    // ApiClient handles 401/403 errors automatically
    return response;
  } catch (error) {
    // If we're logging out, suppress network errors
    if (window.isLoggingOut) {
      console.log('🔇 Network error suppressed during logout:', error.message);
      throw new Error('Network request cancelled during logout');
    }
    throw error;
  }
}

// Make authenticatedFetch available globally for modules
window.authenticatedFetch = authenticatedFetch;

// Load avatar utilities
async function loadAvatarUtils() {
  try {
    const script = document.createElement('script');
    script.src = '/js/utils/avatarUtils.js';
    script.onload = () => {
      console.log('✅ Avatar utilities loaded');
    };
    script.onerror = () => {
      console.error('❌ Failed to load avatar utilities');
    };
    document.head.appendChild(script);
  } catch (error) {
    console.error('❌ Error loading avatar utilities:', error);
  }
}

async function loadComponent(componentName) {
  try {
    console.log(`📦 Loading component: ${componentName}`);
    
    // FOR NAVBAR: Force fresh load by clearing cache (temporary fix for support icons issue)
    if (componentName === 'navbar') {
      console.log('🧹 Clearing navbar cache to ensure fresh content');
      localStorage.removeItem('navbar_html_cache');
      localStorage.removeItem('navbar_html_timestamp');
    }
    
    // Cache navbar HTML to avoid re-fetching every page
    if (componentName === 'navbar') {
      const cacheKey = 'navbar_html_cache';
      const cacheTimestampKey = 'navbar_html_timestamp';
      const cacheExpiry = 30 * 60 * 1000; // 30 minutes
      
      const cachedHtml = localStorage.getItem(cacheKey);
      const cacheTimestamp = localStorage.getItem(cacheTimestampKey);
      const now = Date.now();
      
      // Use cached HTML if available and not expired
      if (cachedHtml && cacheTimestamp && (now - parseInt(cacheTimestamp)) < cacheExpiry) {
        console.log(`✅ Using cached navbar HTML`);
        const container = document.getElementById(`${componentName}-container`);
        if (container) {
          container.innerHTML = cachedHtml;
          await loadNavbarScript();
        }
        return;
      }
    }
    
    console.log(`🔍 Fetching component: /components/${componentName}.html`);
    const response = await fetch(`/components/${componentName}.html`);
    if (!response.ok) {
      throw new Error(`Failed to load ${componentName}: ${response.status}`);
    }
    const html = await response.text();
    console.log(`✅ Component HTML loaded: ${componentName}, length: ${html.length}`);
    
    // Cache navbar HTML
    if (componentName === 'navbar') {
      localStorage.setItem('navbar_html_cache', html);
      localStorage.setItem('navbar_html_timestamp', Date.now().toString());
    }
    
    const container = document.getElementById(`${componentName}-container`);
    if (container) {
      container.innerHTML = html;
      console.log(`✅ Component loaded: ${componentName}`);
      
      // Initialize navbar functionality after HTML is loaded
      if (componentName === 'navbar') {
        console.log('🔄 NAVBAR COMPONENT: HTML loaded, starting script loading...');
        console.log('🔍 NAVBAR COMPONENT: Navbar container content length:', 
          document.getElementById('navbar-container')?.innerHTML?.length || 0
        );
        
        try {
          await loadNavbarScript();
          console.log('✅ NAVBAR COMPONENT: Script loading completed successfully');
          
          // Dispatch custom event when navbar is ready
          window.dispatchEvent(new CustomEvent('navbarReady', {
            detail: { componentName: 'navbar' }
          }));
          console.log('📢 Dispatched navbarReady event');
          
          // Set global flag
          window.navbarReady = true;
          
        } catch (error) {
          console.error('❌ NAVBAR COMPONENT: Script loading failed:', error);
          console.error('❌ This means logout will not work properly!');
        }
      }
    }
  } catch (error) {
    console.error(`❌ Error loading component ${componentName}:`, error);
  }
}

// Load music script
function loadMusicScript() {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    if (window.initializeMusic || document.querySelector('script[src="/js/music.js"]')) {
      console.log('✅ Music script already loaded');
      resolve();
      return;
    }
    
    console.log('🎵 Loading music script...');
    const script = document.createElement('script');
    script.src = '/js/music.js';
    script.onload = () => {
      console.log('✅ Music script loaded');
      resolve();
    };
    script.onerror = (error) => {
      console.error('❌ Error loading music script:', error);
      reject(error);
    };
    document.head.appendChild(script);
  });
}

// Load playerDetails script
function loadPlayerDetailsScript() {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    if (window.openPlayerDetailsModal || document.querySelector('script[src="/js/playerDetails.js"]')) {
      console.log('✅ PlayerDetails script already loaded');
      resolve();
      return;
    }
    
    console.log('📊 Loading playerDetails script...');
    const script = document.createElement('script');
    script.src = '/js/playerDetails.js';
    script.onload = () => {
      console.log('✅ PlayerDetails script loaded');
      resolve();
    };
    script.onerror = (error) => {
      console.error('❌ Error loading playerDetails script:', error);
      reject(error);
    };
    document.head.appendChild(script);
  });
}

// Load navbar script
function loadNavbarScript() {
  console.log('🔄 NAVBAR LOADING: Starting loadNavbarScript()...');
  console.log('🔍 NAVBAR DEBUG: Current state check:', {
    hasModernNavbar: !!window.modernNavbar,
    hasExistingScript: !!document.querySelector('script[src="/js/navbar-modern.js"]'),
    hasInitFunction: typeof window.initModernNavbar === 'function'
  });
  
  return new Promise((resolve, reject) => {
    // Check if navbar script is already loaded AND initialized
    if (window.modernNavbar || document.querySelector('script[src="/js/navbar-modern.js"]')) {
      console.log('✅ NAVBAR LOADING: Script already loaded');
      
      // Check if navbar is already initialized to prevent duplicate handlers
      if (window.navbarInitialized) {
        console.log('⚠️ NAVBAR LOADING: Navbar already initialized, skipping re-initialization');
        resolve();
        return;
      }
      
      if (typeof window.initModernNavbar === 'function') {
        console.log('🚀 NAVBAR LOADING: Calling existing initModernNavbar()...');
        try {
          window.initModernNavbar();
          window.navbarInitialized = true; // Mark as initialized
          console.log('✅ NAVBAR LOADING: initModernNavbar() completed successfully');
          resolve();
        } catch (error) {
          console.error('❌ NAVBAR LOADING: Error in initModernNavbar():', error);
          reject(error);
        }
      } else {
        console.warn('⚠️ NAVBAR LOADING: initModernNavbar function not found!');
        resolve();
      }
      return;
    }
    
    console.log('🚀 NAVBAR LOADING: Loading new navbar script...');
    const script = document.createElement('script');
    script.src = '/js/navbar-modern.js';
    script.onload = () => {
      console.log('✅ NAVBAR LOADING: Script file loaded successfully');
      console.log('🔍 NAVBAR LOADING: Post-load state check:', {
        hasModernNavbar: !!window.modernNavbar,
        hasInitFunction: typeof window.initModernNavbar === 'function',
        hasModernNavbarClass: typeof window.ModernNavbar === 'function'
      });
      
      // Initialize the navbar
      if (typeof window.initModernNavbar === 'function') {
        console.log('🚀 NAVBAR LOADING: Calling new initModernNavbar()...');
        try {
          window.initModernNavbar();
          window.navbarInitialized = true; // Mark as initialized
          console.log('✅ NAVBAR LOADING: New navbar initialized successfully');
          resolve();
        } catch (error) {
          console.error('❌ NAVBAR LOADING: Error initializing new navbar:', error);
          reject(error);
        }
      } else {
        console.error('❌ NAVBAR LOADING: initModernNavbar function not available after script load!');
        console.log('🔍 Available window properties containing "navbar" or "init":', 
          Object.keys(window).filter(key => 
            key.toLowerCase().includes('navbar') || key.toLowerCase().includes('init')
          )
        );
        reject(new Error('initModernNavbar function not found'));
      }
    };
    script.onerror = (error) => {
      console.error('❌ NAVBAR LOADING: Error loading navbar script:', error);
      console.error('❌ Script source that failed:', script.src);
      reject(error);
    };
    document.head.appendChild(script);
    console.log('📡 NAVBAR LOADING: Script element added to head, waiting for load...');
  });
}

// Simplified navigation loading function (NEW UNIFIED SYSTEM)
async function loadNavigation() {
  console.log('🔄 Loading unified navigation...');

  try {
    // Check if navigation is already loaded
    if (window.unifiedNavigation && window.unifiedNavigation.isInitialized) {
      console.log('✅ Navigation already loaded and initialized');
      return window.unifiedNavigation;
    }

    // Load the unified navigation script if not already loaded
    if (!document.querySelector('script[src="/js/unified-navigation.js"]')) {
      console.log('📦 Loading unified navigation script...');

      const script = document.createElement('script');
      script.src = '/js/unified-navigation.js';

      await new Promise((resolve, reject) => {
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });

      console.log('✅ Unified navigation script loaded');
    }

    // Initialize navigation
    if (typeof window.initUnifiedNavigation === 'function') {
      const navigation = await window.initUnifiedNavigation();
      if (navigation) {
        console.log('✅ Unified navigation initialized successfully');
        return navigation;
      } else {
        console.warn('⚠️ Navigation initialization returned null');
        return null;
      }
    } else {
      console.error('❌ initUnifiedNavigation function not found');
      return null;
    }

  } catch (error) {
    console.error('❌ Error loading unified navigation:', error);
    return null;
  }
}

// Simplified navbar loading function (replaces complex loadNavbar)
async function loadNavbar() {
  console.log('🔄 loadNavbar called - using unified navigation system');

  try {
    // Load navbar HTML first
    const navbarContainer = document.getElementById('navbar-container');
    if (!navbarContainer) {
      console.warn('⚠️ Navbar container not found');
      return;
    }

    // Check if navbar HTML is already loaded
    if (navbarContainer.innerHTML.trim() === '') {
      console.log('📦 Loading navbar HTML...');

      const response = await fetch('/components/navbar.html');
      if (response.ok) {
        const html = await response.text();
        navbarContainer.innerHTML = html;
        console.log('✅ Navbar HTML loaded');
      } else {
        console.error('❌ Failed to load navbar HTML');
        return;
      }
    }

    // Load and initialize unified navigation
    const navigation = await loadNavigation();
    if (navigation) {
      console.log('✅ Navbar loaded and initialized successfully');
    } else {
      console.warn('⚠️ Navigation initialization failed');
    }

  } catch (error) {
    console.error('❌ Error in loadNavbar:', error);
  }
}

// Load game tabs script
function loadGameTabsScript() {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    if (window.GameTabs || document.querySelector('script[src="/js/game-tabs.js"]')) {
      console.log('✅ Game tabs script already loaded');
      resolve();
      return;
    }
    
    console.log('🎮 Loading game tabs script...');
    const script = document.createElement('script');
    script.src = '/js/game-tabs.js';
    script.onload = () => {
      console.log('✅ Game tabs script loaded');
      resolve();
    };
    script.onerror = (error) => {
      console.error('❌ Error loading game tabs script:', error);
      reject(error);
    };
    document.head.appendChild(script);
  });
}

// Load chat system
function loadChatSystem() {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    if (window.chatManager || document.querySelector('script[src="/js/chat-init.js"]')) {
      console.log('✅ Chat system already loaded');
      if (window.initializeChat) {
        window.initializeChat();
      }
      resolve();
      return;
    }
    
    console.log('💬 Loading chat system...');
    
    // Load CSS first
    if (!document.querySelector('link[href="/css/chat-unified.css"]')) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = '/css/chat-unified.css';
      link.onload = () => {
        console.log('✅ Chat CSS loaded successfully');
      };
      link.onerror = () => {
        console.error('❌ Failed to load chat CSS, trying fallback...');
        // Try alternative path
        const fallbackLink = document.createElement('link');
        fallbackLink.rel = 'stylesheet';
        fallbackLink.href = '/css/chat.css';
        document.head.appendChild(fallbackLink);
      };
      document.head.appendChild(link);
      console.log('✅ Chat CSS loading initiated');
    } else {
      console.log('✅ Chat CSS already loaded');
    }
    
    // Load JavaScript module
    const script = document.createElement('script');
    script.type = 'module';
    script.src = '/js/chat-init.js';
    script.onload = () => {
      console.log('✅ Chat system loaded');
      resolve();
    };
    script.onerror = (error) => {
      console.error('❌ Error loading chat system:', error);
      reject(error);
    };
    document.head.appendChild(script);
  });
}

// Global cleanup system to prevent memory leaks
function setupGlobalCleanup() {
  console.log('🧹 Setting up global cleanup system...');
  
  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    console.log('🧹 Page unloading, cleaning up resources...');
    
    // Cleanup music system
    if (window.musicManager && window.musicManager.cleanup) {
      window.musicManager.cleanup();
    }
    
    // Cleanup chat system
    if (window.chatManager && window.chatManager.deactivateChat) {
      window.chatManager.deactivateChat();
    }
    
    // Cleanup Warcraft app
    if (window.warcraftApp && window.warcraftApp.cleanup) {
      window.warcraftApp.cleanup();
    }
    
    // Cleanup other managers
    if (window.contentManager && window.contentManager.destroy) {
      window.contentManager.destroy();
    }
    
    if (window.mapsSystem && window.mapsSystem.cleanup) {
      window.mapsSystem.cleanup();
    }
    
    console.log('✅ Global cleanup completed');
  });
  
  // Cleanup on page visibility change (when tab becomes hidden)
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      console.log('🧹 Page hidden, performing cleanup...');
      
      // Save music state
      if (window.musicManager && window.musicManager.saveState) {
        window.musicManager.saveState();
      }
    }
  });
  
  console.log('✅ Global cleanup system initialized');
}

// Detect if we're running in Electron environment
function isElectronEnvironment() {
  return !!(window.electronAPI || 
            navigator.userAgent.toLowerCase().indexOf('electron') > -1 ||
            document.body.classList.contains('electron-app') ||
            new URLSearchParams(window.location.search).get('electron') === 'true');
}

// Auto-claim transfer token if present in URL (for Electron app integration)
async function handleElectronTransfer() {
    const transferToken = urlParams.get('transferToken');
    const isElectron = urlParams.get('electron') === 'true';
    
    if (transferToken && isElectron) {
        console.log('🔄 Transfer token detected, claiming session...');
        
        try {
            const response = await fetch('/auth/claim-transfer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({ transferToken })
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ Session transfer successful for user:', result.user.username);
                
                // Store JWT token in localStorage for future API requests
                if (result.authToken) {
                    localStorage.setItem('authToken', result.authToken);
                    console.log('🔑 JWT token stored in localStorage');
                }
                
                // Clean up the URL without refreshing the page
                const newUrl = new URL(window.location);
                newUrl.searchParams.delete('transferToken');
                window.history.replaceState({}, document.title, newUrl.toString());
                
                // Wait and verify session is established
                console.log('⏳ Waiting for session to be established...');
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Verify session is working by making a test request
                let sessionWorking = false;
                let attempts = 0;
                while (!sessionWorking && attempts < 3) {
                    try {
                        const testResponse = await authenticatedFetch('/api/me');
                        
                        if (testResponse.ok) {
                            sessionWorking = true;
                            console.log('✅ Session verified as working');
                        } else {
                            attempts++;
                            console.log(`⚠️ Session not ready yet (attempt ${attempts}/3), waiting...`);
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }
                    } catch (error) {
                        attempts++;
                        console.log(`⚠️ Session test failed (attempt ${attempts}/3):`, error);
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
                
                if (!sessionWorking) {
                    console.error('❌ Session establishment failed after 3 attempts');
                    console.log('🔄 Forcing page reload to establish session...');
                    window.location.reload();
                    return true;
                }
                
                // Don't force reload - let the session establish naturally
                console.log('✅ Session transfer complete, session established successfully');
                
                // Dispatch custom event for other components to handle
                window.dispatchEvent(new CustomEvent('electronTransferComplete', { 
                    detail: { user: result.user, authToken: result.authToken }
                }));
                
                // Force refresh user data
                if (window.userManager) {
                    await window.userManager.getCurrentUser();
                }
                
                // Show success message
                if (window.showNotification) {
                    window.showNotification('Welcome back! Successfully logged in from Electron app.', 'success');
                }
                
                return true;
            } else {
                console.error('❌ Session transfer failed:', result.error);
                
                // Clean up the URL even on failure
                const newUrl = new URL(window.location);
                newUrl.searchParams.delete('transferToken');
                window.history.replaceState({}, document.title, newUrl.toString());
                
                if (window.showNotification) {
                    window.showNotification('Session transfer failed. Please login manually.', 'error');
                }
                
                return false;
            }
            
        } catch (error) {
            console.error('❌ Transfer token claim error:', error);
            
            // Clean up the URL on error
            const newUrl = new URL(window.location);
            newUrl.searchParams.delete('transferToken');
            window.history.replaceState({}, document.title, newUrl.toString());
            
            if (window.showNotification) {
                window.showNotification('Session transfer failed. Please login manually.', 'error');
            }
            
            return false;
        }
    }
    
    return false;
}

window.addEventListener('DOMContentLoaded', async () => {
  console.log('🚀 DOM Content Loaded - Starting component loading...');
  
  // Load navbar FIRST to ensure it's always available
  console.log('🔄 Loading navbar immediately...');
  console.log('🔍 Navbar container exists:', !!document.getElementById('navbar-container'));
  console.log('🔍 Navbar container content before loading:', document.getElementById('navbar-container')?.innerHTML?.length || 0);
  
  try {
    await loadComponent('navbar');
    console.log('✅ Navbar loaded successfully');
    console.log('🔍 Navbar container content after loading:', document.getElementById('navbar-container')?.innerHTML?.length || 0);
    console.log('🔍 Navbar container visible:', document.getElementById('navbar-container')?.offsetHeight || 0);
  } catch (error) {
    console.error('❌ Failed to load navbar:', error);
  }
  
  // Check for transfer token
  console.log('🔍 Checking for Electron transfer token...');
  try {
    const transferred = await handleElectronTransfer();
    if (transferred) {
      console.log('🎉 Electron session transfer completed - proceeding with authenticated flow');
    }
  } catch (error) {
    console.error('❌ Failed to handle Electron transfer:', error);
  }
  
  // Setup global cleanup system
  try {
    setupGlobalCleanup();
  } catch (error) {
    console.error('❌ Failed to setup global cleanup:', error);
  }
  
  // Load other scripts
  try {
    await loadMusicScript();
  } catch (error) {
    console.error('❌ Failed to load music script:', error);
  }
  
  try {
    await loadPlayerDetailsScript();
  } catch (error) {
    console.error('❌ Failed to load playerDetails script:', error);
  }
  
  try {
    await loadGameTabsScript();
  } catch (error) {
    console.error('❌ Failed to load game tabs script:', error);
  }
  
  // Load footer
  try {
    await loadComponent('footer');
  } catch (error) {
    console.error('❌ Failed to load footer:', error);
  }
  
  console.log('📦 All components loaded');
  
  // OPTIMIZATION: Only auto-load user data if no lightweight function is being used
  // This prevents duplicate API calls when pages use updateNavbarProfile()
  console.log('👤 Checking if lightweight profile update is being used...');
  const currentPath = window.location.pathname;
  const pagesUsingLightweight = [
    '/views/hero.html',
    '/views/ladder.html', 
    '/views/index.html',
    '/views/tournaments.html',
    '/views/content.html',
    '/views/forum.html',
    '/views/campaigns.html'
  ];
  
  const isUsingLightweight = pagesUsingLightweight.some(page => currentPath.includes(page));
  
  if (isUsingLightweight) {
    console.log('✅ Page uses lightweight profile update - skipping main.js auto-load');
  } else {
    console.log('👤 Auto-loading user data (page not using lightweight function)...');
    setTimeout(async () => {
      if (typeof window.loadUser === 'function') {
        try {
          await window.loadUser();
          console.log('✅ User data auto-loaded successfully');
          
          // Initialize chat system after user data is loaded
          if (!window.location.pathname.includes('login')) {
            try {
              await loadChatSystem();
              console.log('✅ Chat system initialized');
            } catch (error) {
              console.error('❌ Failed to initialize chat system:', error);
            }
          }
        } catch (error) {
          console.error('❌ Failed to auto-load user data:', error);
        }
      } else {
        console.warn('⚠️ loadUser function not available');
      }
    }, 100);
  }
});

// Export loadUser function for use by navbar
window.loadUser = loadUser;

// Debug function to check authentication state
window.debugAuth = function() {
  const authToken = localStorage.getItem('authToken');
  const currentUser = window.currentUser;
  
  console.log('🔍 Authentication Debug Status:');
  console.log('- JWT Token:', authToken ? `Present (${authToken.length} chars)` : 'Not found');
  console.log('- Current User:', currentUser ? currentUser.username : 'Not loaded');
  console.log('- URL:', window.location.href);
  console.log('- Cookies:', document.cookie);
  console.log('- Is Electron:', isElectronEnvironment());
  console.log('- Is iframe:', window.parent !== window);
  
  if (authToken) {
    console.log('🔑 First 20 chars of token:', authToken.substring(0, 20) + '...');
  }
  
  return {
    hasToken: !!authToken,
    tokenLength: authToken ? authToken.length : 0,
    hasUser: !!currentUser,
    username: currentUser ? currentUser.username : null
  };
};

/**
 * Lightweight function to update navbar profile image and username
 * Only loads minimal data needed for navbar display
 */
async function updateNavbarProfile() {
  try {
    console.log('🔄 Updating navbar profile...');
    
    // In Electron mode, we need to be more patient with navbar loading
    const isElectronMode = isElectronEnvironment() || window.parent !== window;
    console.log('🔍 Environment detected:', { isElectronMode });
    
    // In Electron mode, wait for transfer token processing to complete
    if (isElectronMode) {
      const transferToken = new URLSearchParams(window.location.search).get('transferToken');
      if (transferToken) {
        console.log('⏳ Transfer token detected, waiting for processing...');
        
        // Wait for transfer to complete via event or timeout
        await new Promise(resolve => {
          const handleTransferComplete = () => {
            console.log('📢 Received electronTransferComplete event');
            window.removeEventListener('electronTransferComplete', handleTransferComplete);
            resolve();
          };
          
          window.addEventListener('electronTransferComplete', handleTransferComplete);
          
          // Fallback timeout
          setTimeout(() => {
            console.log('⏰ Transfer timeout, proceeding anyway');
            window.removeEventListener('electronTransferComplete', handleTransferComplete);
            resolve();
          }, 5000);
        });
      }
      
      // Wait for app to be ready (ApiClient and AuthManager)
      if (!window._appInstance || !window.apiClient) {
        console.log('⏳ Waiting for app to be ready...');
        await new Promise(resolve => {
          const checkAppReady = () => {
            if (window._appInstance && window.apiClient) {
              console.log('✅ App ready, proceeding with navbar update');
              resolve();
            } else {
              console.log('⏳ Waiting for app to be ready...');
              setTimeout(checkAppReady, 100);
            }
          };
          checkAppReady();
        });
      }
    }
    
    // Wait for navbar to be fully ready
    await waitForNavbarToBeReady();
    
    // ELECTRON OPTIMIZATION: Check if user data is already available from AuthManager
    if (isElectronMode && window._appInstance) {
      const authManager = window._appInstance.getComponent('auth');
      if (authManager && authManager.getUser()) {
        const user = authManager.getUser();
        console.log('✅ Using existing user data from AuthManager:', user.username);
        
        // Check if navbar is already properly updated
        const usernameElement = document.getElementById('navbar-username');

        if (usernameElement && usernameElement.textContent === user.username) {
          console.log('✅ Navbar already properly updated, skipping duplicate update');
          return;
        }
        
        updateNavbarWithUser(user);
        return;
      }
    }
    
    // Check if we have a JWT token
    const authToken = localStorage.getItem('authToken');
    if (!authToken) {
      console.log('⚠️ No auth token found');
      return;
    }

    // OPTIMIZATION: Simple cache to prevent duplicate API calls within 30 seconds
    const cacheKey = 'navbar_profile_cache';
    const cacheTimestampKey = 'navbar_profile_timestamp';
    const cacheExpiry = 30 * 1000; // 30 seconds
    
    const cachedData = localStorage.getItem(cacheKey);
    const cacheTimestamp = localStorage.getItem(cacheTimestampKey);
    const now = Date.now();
    
    // Use cached data if available and not expired
    if (cachedData && cacheTimestamp && (now - parseInt(cacheTimestamp)) < cacheExpiry) {
      console.log('✅ Using cached navbar profile data');
      const user = JSON.parse(cachedData);
      updateNavbarWithUser(user);
      return;
    }

    // Make a lightweight API call to get just navbar data
    // Use authenticatedFetch to handle Electron mode properly
    let response;
    let retryCount = 0;
    const maxRetries = 3;
    
    while (retryCount < maxRetries) {
      try {
        console.log(`📡 Making API request (attempt ${retryCount + 1}/${maxRetries})...`);
        response = await authenticatedFetch('/api/me', {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });
        
        if (response && response.ok) {
          console.log('✅ API request successful');
          break;
        } else {
          console.log(`⚠️ API request failed (attempt ${retryCount + 1}):`, response?.status);
          retryCount++;
          if (retryCount < maxRetries) {
            console.log(`⏳ Retrying in 1 second...`);
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      } catch (error) {
        console.error(`❌ API request error (attempt ${retryCount + 1}):`, error);
        retryCount++;
        if (retryCount < maxRetries) {
          console.log(`⏳ Retrying in 1 second...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }
    
    if (!response || !response.ok) {
      console.log('⚠️ All API attempts failed');
      return;
    }

    // Handle both Response objects and direct data
    let user;
    if (response && response.json) {
      // It's a Response object, extract JSON
      user = await response.json();
    } else {
      // It's already the data (from ApiClient)
      user = response;
    }
    
    console.log('🔍 Lightweight API response:', {
      responseType: typeof response,
      hasJson: !!(response && response.json),
      userData: user,
      username: user?.username,
      avatar: user?.avatar
    });
    
    // Cache the data for future use
    localStorage.setItem(cacheKey, JSON.stringify(user));
    localStorage.setItem(cacheTimestampKey, now.toString());
    
    // Update navbar with minimal data
    updateNavbarWithUser(user);
    
  } catch (error) {
    console.error('❌ Error updating navbar profile:', error);
  }
}

// Expose the function globally for backward compatibility
window.updateNavbarProfile = updateNavbarProfile;

/**
 * Simplified navbar profile update (UNIFIED SYSTEM)
 */
window.updateNavbarProfileUnified = async function() {
  console.log('👤 Updating navbar profile (unified system)...');

  try {
    // Use unified navigation if available
    if (window.unifiedNavigation && window.unifiedNavigation.isInitialized) {
      console.log('🔄 Refreshing user data via unified navigation...');
      await window.unifiedNavigation.refreshUserData();
      return;
    }

    // Fallback to legacy system if unified navigation not available
    console.log('⚠️ Unified navigation not available, using fallback...');
    await updateNavbarProfile();

  } catch (error) {
    console.error('❌ Error updating navbar profile (unified):', error);
  }
};

/**
 * Update navbar with user data
 */
function updateNavbarWithUser(user) {
  console.log('👤 Updating navbar with user:', user.username);
  
  // Update usernames
  const profileUsername = document.getElementById('navbar-username');
  const profileUsernameMobile = document.getElementById('navbar-username-mobile');
  
  console.log('🔍 Navbar elements found:', {
    profileUsername: !!profileUsername,
    profileUsernameMobile: !!profileUsernameMobile,
    username: user.username
  });
  
  if (profileUsername) {
    profileUsername.textContent = user.username || 'User';
    console.log('✅ Updated desktop username');
  } else {
    console.warn('⚠️ Desktop username element not found');
  }
  
  if (profileUsernameMobile) {
    profileUsernameMobile.textContent = user.username || 'User';
    console.log('✅ Updated mobile username');
  } else {
    console.warn('⚠️ Mobile username element not found');
  }
  
  // Update profile images
  const profileImage = document.getElementById('profile-image');
  const profileImageMobile = document.getElementById('profile-image-mobile');
  
  const avatarUrl = user.avatar || '/assets/img/ranks/emblem.png';
  console.log('👤 Setting navbar avatar:', avatarUrl);
  
  console.log('🔍 Profile image elements found:', {
    profileImage: !!profileImage,
    profileImageMobile: !!profileImageMobile
  });
  
  if (profileImage) {
    profileImage.src = avatarUrl;
    console.log('✅ Updated desktop profile image');
  } else {
    console.warn('⚠️ Desktop profile image element not found');
  }
  
  if (profileImageMobile) {
    profileImageMobile.src = avatarUrl;
    console.log('✅ Updated mobile profile image');
  } else {
    console.warn('⚠️ Mobile profile image element not found');
  }
  
  // Admin link is now handled on profile page only
  console.log('ℹ️ Admin link handling moved to profile page');
  
  console.log('✅ Navbar updated successfully');
}



async function loadUser() {
  // Don't load user data if logout is in progress
  if (window.isLoggingOut) {
    console.log('🛑 loadUser blocked - logout in progress');
    return;
  }
  
  try {
    // CACHE TEMPORARILY DISABLED FOR AVATAR TESTING
    // Will re-enable after avatar system is working properly
    console.log('🔄 Loading fresh user data...');
    
    // Debug: Check if we have a JWT token
    const authToken = localStorage.getItem('authToken');
    console.log('🔑 loadUser: Auth token status:', { 
      hasToken: !!authToken, 
      tokenLength: authToken ? authToken.length : 0 
    });

    // Add cache-busting timestamp to ensure fresh data
    const timestamp = Date.now();
    const apiUrl = '/api/me';
    const urlWithCacheBust = `${apiUrl}?_t=${timestamp}`;
    
    console.log('📡 Making API request to:', urlWithCacheBust);
    const res = await authenticatedFetch(urlWithCacheBust);

    if (!res) {
      // Don't handle auth errors if we're already logging out
      if (window.isLoggingOut) {
        console.log('🛑 Auth error during logout, ignoring');
        return;
      }
      
      // Check if we're in Electron mode
      const isElectronMode = isElectronEnvironment() || window.parent !== window;
      
      if (isElectronMode) {
        console.log('🖥️ Authentication failed in Electron mode - requesting parent window authentication');
        // Don't redirect to login in Electron mode, let the parent handle it
        if (window.parent !== window && !authRequested) {
          authRequested = true;
          window.parent.postMessage({ type: 'REQUEST_AUTH_TOKEN' }, '*');
        }
        return;
      }

      // Don't redirect if on login/setup pages
      const currentPath = window.location.pathname;
      const isAuthPage =
        currentPath.includes('/views/login.html') ||
        currentPath.includes('/views/setup-username.html');

      if (!isAuthPage) {
        window.location.href = '/views/login.html';
      }

      return;
    }

    // Handle both Response objects and direct data
    let user;
    if (res && res.json) {
      // It's a Response object, extract JSON
      user = await res.json();
    } else {
      // It's already the data
      user = res;
    }
    console.log('User data from /api/me:', user);
    
    // Cache temporarily disabled for avatar testing
    // sessionStorage.setItem(cacheKey, JSON.stringify(user));
    // sessionStorage.setItem(cacheTimestampKey, now.toString());
    
    // Store user data in localStorage for other parts of the app
    localStorage.setItem('user', JSON.stringify(user));

    // Note: We don't need to fetch player data separately anymore
    // The avatar is now managed entirely by the backend AvatarService
    // and is included in the user object from /api/me
    console.log('👤 Avatar system: Using database avatar from user object');

    // Redirect only on protected pages if username is not defined
    if (!user.isUsernameDefined) {
      const currentPath = window.location.pathname;
      const isAuthPage =
        currentPath.includes('/views/login.html') ||
        currentPath.includes('/views/setup-username.html') ||
        currentPath.includes('/setup-username');

      if (!isAuthPage) {
        // Use the server-side route for better consistency
        window.location.href = '/setup-username';
      }

      return;
    }

    // Update navbar if it exists
    try {
      // Use the unified updateNavbarWithUser function for consistency
      updateNavbarWithUser(user);
    } catch (error) {
      console.error('❌ Error updating navbar display:', error);
    }

    // Store user data globally
    window.currentUser = user;
  } catch (error) {
    console.error('Error loading user:', error);
    // Clear potentially corrupted cache
    sessionStorage.removeItem('user_session_cache');
    sessionStorage.removeItem('user_session_timestamp');
    
    // Check if we're in Electron mode
    const isElectronMode = isElectronEnvironment() || window.parent !== window || window.isElectronMode;
    
    if (isElectronMode) {
      console.log('🖥️ User loading error in Electron mode - requesting parent window authentication');
      // Don't redirect to login in Electron mode, let the parent handle it
      if (window.parent !== window && !authRequested) {
        authRequested = true;
        window.parent.postMessage({ type: 'REQUEST_AUTH_TOKEN' }, '*');
      }
      return;
    }
    
    // Avoid redirect loop on login page
    const currentPath = window.location.pathname;
    if (!currentPath.includes('/views/login.html')) {
      window.location.href = '/views/login.html';
    }
  }
}

/**
 * Load and display the monthly donation goal
 */
async function loadDonationGoal() {
  try {
    const response = await authenticatedFetch('/api/donations/monthly-goal');
    if (!response) {
      throw new Error('Failed to fetch donation goal');
    }
    
    // Handle both Response objects and direct data
    let data;
    if (response && response.json) {
      // It's a Response object, extract JSON
      data = await response.json();
    } else {
      // It's already the data
      data = response;
    }
    const { current = 0, goal: target = 1000 } = data;

    // Update the display
    const goalBar = document.getElementById('donation-goal-bar');
    const currentAmount = document.getElementById('donation-current-amount');
    const targetAmount = document.getElementById('donation-target-amount');
    const percentageDisplay = document.getElementById('donation-percentage');

    if (goalBar && currentAmount && targetAmount && percentageDisplay) {
      // Calculate percentage with a maximum of 100%
      const percentage = Math.min(Math.round((current / target) * 100), 100);
      
      // Update the elements
      goalBar.style.width = `${percentage}%`;
      goalBar.classList.toggle('goal-reached', percentage >= 100);
      currentAmount.textContent = formatCurrency(current);
      targetAmount.textContent = formatCurrency(target);
      percentageDisplay.textContent = percentage;
      targetAmount.textContent = formatCurrency(target);
      percentageDisplay.textContent = `${percentage}%`;
    }
  } catch (error) {
    console.error('Error loading donation goal:', error);
    // Set default values if there's an error
    const elements = {
      bar: document.getElementById('donation-goal-bar'),
      current: document.getElementById('donation-current-amount'),
      target: document.getElementById('donation-target-amount'),
      percentage: document.getElementById('donation-percentage')
    };

    if (elements.bar) elements.bar.style.width = '0%';
    if (elements.current) elements.current.textContent = formatCurrency(0);
    if (elements.target) elements.target.textContent = formatCurrency(1000);
    if (elements.percentage) elements.percentage.textContent = '0%';
  }
}

/**
 * Format currency amount
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: USD)
 * @returns {string} Formatted currency string
 */
function formatCurrency(amount, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

/**
 * Clear all navbar and user caches
 * Useful for logout, data refresh, or cache corruption
 */
function clearNavbarCaches() {
  console.log('🧹 Clearing navbar caches...');
  
  // Clear navbar HTML cache
  localStorage.removeItem('navbar_html_cache');
  localStorage.removeItem('navbar_html_timestamp');
  
  // Clear navbar profile cache
  localStorage.removeItem('navbar_profile_cache');
  localStorage.removeItem('navbar_profile_timestamp');
  
  console.log('✅ Navbar caches cleared');
}

/**
 * Force refresh user data (bypass cache)
 */
async function refreshUserData() {
  console.log('🔄 Force refreshing user data...');
  
  // Clear user caches
  sessionStorage.removeItem('user_session_cache');
  sessionStorage.removeItem('user_session_timestamp');
  sessionStorage.removeItem('players_session_cache');
  sessionStorage.removeItem('players_session_timestamp');
  
  // Reload user data
  await loadUser();
  
  console.log('✅ User data refreshed');
}

// Removed debugNavbarRankImage - use /api/me/avatar-debug endpoint instead

// Export functions for use in other files
window.loadDonationGoal = loadDonationGoal;
window.clearNavbarCaches = clearNavbarCaches;
window.refreshUserData = refreshUserData;
window.updateElectronModeFlags = updateElectronModeFlags;

/**
 * Clear navbar profile cache specifically
 * Call this when user data changes (logout, profile update, etc.)
 */
function clearNavbarProfileCache() {
  console.log('🧹 Clearing navbar profile cache...');
  localStorage.removeItem('navbar_profile_cache');
  localStorage.removeItem('navbar_profile_timestamp');
  console.log('✅ Navbar profile cache cleared');
}

// Export the cache clearing function
window.clearNavbarProfileCache = clearNavbarProfileCache;

/**
 * Wait for navbar to be fully loaded and ready
 */
async function waitForNavbar() {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    const maxAttempts = 100; // 10 seconds max wait
    
    const checkNavbar = () => {
      attempts++;
      const navbar = document.querySelector('.navbar-modern');
      const usernameElement = document.getElementById('navbar-username');
      const profileImage = document.getElementById('profile-image');
      
      console.log(`🔍 Navbar check ${attempts}:`, {
        navbar: !!navbar,
        usernameElement: !!usernameElement,
        profileImage: !!profileImage,
        usernameText: usernameElement?.textContent,
        profileSrc: profileImage?.src
      });
      
      if (navbar && usernameElement && profileImage) {
        console.log('✅ Navbar is ready');
        resolve();
      } else if (attempts >= maxAttempts) {
        console.warn('⚠️ Navbar wait timeout - proceeding anyway');
        resolve(); // Resolve anyway to prevent blocking
      } else {
        console.log(`⏳ Waiting for navbar to be ready... (attempt ${attempts}/${maxAttempts})`);
        setTimeout(checkNavbar, 100);
      }
    };
    
    checkNavbar();
  });
}

/**
 * Enhanced function to wait for navbar to be fully ready (especially for Electron mode)
 */
async function waitForNavbarToBeReady() {
  return new Promise((resolve) => {
    let attempts = 0;
    const maxAttempts = 150; // 15 seconds max wait for Electron mode
    
    const checkNavbar = () => {
      attempts++;
      
      // Check multiple conditions for navbar readiness
      const navbar = document.querySelector('.navbar-modern');
      const usernameElement = document.getElementById('navbar-username');
      const profileImage = document.getElementById('profile-image');
      const navbarScript = window.navbar;
      const navbarInitialized = window.navbarReady;
      
      console.log(`🔍 Enhanced navbar check ${attempts}:`, {
        navbar: !!navbar,
        usernameElement: !!usernameElement,
        profileImage: !!profileImage,
        navbarScript: !!navbarScript,
        navbarInitialized: !!navbarInitialized,
        usernameText: usernameElement?.textContent,
        profileSrc: profileImage?.src,
        attempts: `${attempts}/${maxAttempts}`
      });
      
      // Check if navbar is fully ready
      const isReady = navbar && 
                     usernameElement && 
                     profileImage && 
                     (navbarScript || navbarInitialized);
      
      if (isReady) {
        console.log('✅ Navbar is fully ready');
        resolve();
      } else if (attempts >= maxAttempts) {
        console.warn('⚠️ Navbar wait timeout - proceeding anyway');
        resolve(); // Resolve anyway to prevent blocking
      } else {
        // Use longer intervals for Electron mode
        const interval = window.parent !== window ? 200 : 100;
        setTimeout(checkNavbar, interval);
      }
    };
    
    checkNavbar();
  });
}

// Initialize avatar utilities when the page loads
document.addEventListener('DOMContentLoaded', async () => {
  console.log('🚀 Initializing avatar system...');
  await loadAvatarUtils();
  console.log('✅ Avatar system initialized');
});

// Electron Authentication Message Handler
// This handles authentication tokens sent from the parent Electron window
if (isElectronMode) {
  console.log('🔐 Setting up Electron authentication message handler');
  
  // Set a global flag to prevent infinite loops in Electron mode
  window.isElectronMode = true;
  console.log('🖥️ Electron mode detected - disabling infinite loops');
  
  // Add a global flag to prevent API calls until authentication is established
  window.electronAuthEstablished = false;
  console.log('🛑 API calls blocked until Electron authentication is established');
  
  // Set up navigation listener to update Electron mode flags
  window.addEventListener('popstate', () => {
    console.log('🔄 Navigation detected - updating Electron mode flags');
    updateElectronModeFlags();
  });
  
  // Also listen for pushstate events
  const originalPushState = history.pushState;
  history.pushState = function(...args) {
    originalPushState.apply(history, args);
    console.log('🔄 PushState detected - updating Electron mode flags');
    updateElectronModeFlags();
  };
  
  window.addEventListener('message', async (event) => {
    console.log('📨 Received message from parent:', event.data);
    
    if (event.data.type === 'ELECTRON_AUTH' && event.data.token) {
      console.log('🔐 Received Electron authentication token from parent window');
      
      // Reset auth request flag since we received authentication
      authRequested = false;
      
      // Store the JWT token for API requests
      localStorage.setItem('authToken', event.data.token);
      
      // Set cookie for backend requests (no secure flag for localhost)
      const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
      const cookieSecure = isLocalhost ? '' : 'secure;';
      document.cookie = `authToken=${event.data.token}; path=/; ${cookieSecure} samesite=strict`;
      
      // Set up authenticated headers for future requests
      window.electronAuth = {
        token: event.data.token,
        user: event.data.user,
        isElectron: true
      };
      
      // Mark authentication as established to allow API calls
      window.electronAuthEstablished = true;
      console.log('✅ Electron authentication established - API calls now allowed');
      
      console.log('✅ Electron authentication stored, refreshing user data...');
      
      // Refresh user data
      if (typeof window.loadUser === 'function') {
        try {
          await window.loadUser();
          console.log('✅ User data refreshed with Electron authentication');
        } catch (error) {
          console.error('❌ Failed to refresh user data:', error);
        }
      }
      
      // Remove any login-related UI elements
      const loginElements = document.querySelectorAll('.login-required, .auth-required');
      loginElements.forEach(el => el.style.display = 'none');
      
      // Show authenticated elements
      const authElements = document.querySelectorAll('.authenticated-only');
      authElements.forEach(el => el.style.display = 'block');
      
      // Dispatch custom event for other components to handle
      window.dispatchEvent(new CustomEvent('electronAuthReceived', { 
        detail: { token: event.data.token, user: event.data.user }
      }));
    }
    
    if (event.data.type === 'REQUEST_AUTH_TOKEN') {
      console.log('🔑 Parent window requesting auth token');
      
      // Send back the current token if we have one
      const token = localStorage.getItem('authToken');
      if (token) {
        event.source.postMessage({
          type: 'AUTH_TOKEN_RESPONSE',
          token: token
        }, '*');
      }
    }
  });
  
  // Send initial request for auth token if needed
  setTimeout(() => {
    if (window.parent !== window && !authRequested) {
      console.log('🔄 Requesting authentication from parent Electron window...');
      authRequested = true;
      window.parent.postMessage({ type: 'REQUEST_AUTH_TOKEN' }, '*');
    }
  }, 1000);
}

// Reset logout flag if we're on the login page
if (window.location.pathname.includes('/views/login.html')) {
  console.log('🔄 Login page detected, resetting logout flag');
  window.isLoggingOut = false;
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 MAIN.JS LOADING: main.js script is executing');
    console.log('🚀 Current page URL:', window.location.href);
    console.log('🚀 Document ready state:', document.readyState);
    console.log('🚀 Available navbar container:', !!document.getElementById('navbar-container'));
    
    // ... existing code ...
});
