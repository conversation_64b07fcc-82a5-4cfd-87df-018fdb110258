const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');

const app = express();
const PORT = 3001;
const BACKEND_URL = 'http://127.0.0.1:3000';

console.log('🚀 Starting Electron Proxy Server...');
console.log(`📡 Proxy: http://127.0.0.1:${PORT} -> ${BACKEND_URL}`);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', service: 'electron-proxy', timestamp: new Date().toISOString() });
});

// Serve static files from the frontend directory
app.use(express.static(path.join(__dirname, '..', 'frontend')));

// Proxy uploads to the main backend (IMPORTANT: This must come before other static file serving)
app.use('/uploads', createProxyMiddleware({
  target: BACKEND_URL,
  changeOrigin: true,
  logLevel: 'silent',
  onProxyReq: (proxyReq, req, res) => {
    console.log(`📁 Proxying upload ${req.method} ${req.url} -> ${BACKEND_URL}${req.url}`);
  },
  onError: (err, req, res) => {
    console.error('❌ Upload proxy error:', err.message);
    res.status(500).json({ error: 'Upload proxy error', message: err.message });
  }
}));

// Proxy all API requests to the main backend
app.use('/api', createProxyMiddleware({
  target: BACKEND_URL,
  changeOrigin: true,
  logLevel: 'silent',
  onProxyReq: (proxyReq, req, res) => {
    console.log(`🔄 Proxying ${req.method} ${req.url} -> ${BACKEND_URL}${req.url}`);
  },
  onError: (err, req, res) => {
    console.error('❌ Proxy error:', err.message);
    res.status(500).json({ error: 'Proxy error', message: err.message });
  }
}));

// Proxy auth routes
app.use('/auth', createProxyMiddleware({
  target: BACKEND_URL,
  changeOrigin: true,
  logLevel: 'silent',
  onProxyReq: (proxyReq, req, res) => {
    console.log(`🔄 Proxying ${req.method} ${req.url} -> ${BACKEND_URL}${req.url}`);
  }
}));

// Proxy socket.io connections
app.use('/socket.io', createProxyMiddleware({
  target: BACKEND_URL,
  changeOrigin: true,
  ws: true,
  logLevel: 'silent'
}));

// Handle all other routes by serving the frontend
app.get('*', (req, res) => {
  // For API routes that might not be caught by the proxy
  if (req.path.startsWith('/api/')) {
    console.log(`🔄 Direct proxy for ${req.method} ${req.url} -> ${BACKEND_URL}${req.url}`);
    return createProxyMiddleware({
      target: BACKEND_URL,
      changeOrigin: true,
      logLevel: 'silent'
    })(req, res);
  }
  
  // Serve frontend files
  res.sendFile(path.join(__dirname, '..', 'frontend', 'views', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`✅ Electron Proxy Server running on http://127.0.0.1:${PORT}`);
  console.log(`🔗 All requests will be proxied to ${BACKEND_URL}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Electron Proxy Server...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down Electron Proxy Server...');
  process.exit(0);
}); 