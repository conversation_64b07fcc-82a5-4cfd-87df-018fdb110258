/**
 * MapsGrid.js - Maps Grid Display and Data Management
 * 
 * Extracted from the 72KB maps.js monster.
 * <PERSON>les maps grid rendering, pagination, search, filtering, and hero stats display.
 * 
 * Responsibilities:
 * - Maps data loading and caching
 * - Grid rendering and display
 * - Pagination system
 * - Search and filtering functionality
 * - Hero stats and game statistics
 */

export class MapsGrid {
  constructor() {
    this.mapsCache = new Map();
    this.currentMaps = [];
    this.isLoading = false;
    this.loadingStates = new Set();
    this.statsLoaded = false;
  }

  /**
   * Initialize the maps grid system
   */
  init() {
    console.log('🗃️ Initializing Maps Grid...');
    this.setupGlobalFunctions();
    this.initLazyLoading();
    console.log('✅ Maps Grid initialized');
  }

  /**
   * Setup global functions for backward compatibility
   */
  setupGlobalFunctions() {
    window.mapsGrid = this;
    window.viewMapDetails = (mapId) => this.viewMapDetails(mapId);
    window.downloadMap = (mapId) => this.downloadMap(mapId);
    window.deleteMap = (mapId) => this.deleteMap(mapId);
    
    console.log('🌐 Maps grid functions registered');
  }

  /**
   * Load maps data from API
   */
  async loadMaps(apiUrl = null) {
    // Skip loading if current game is war1 - WC1 has its own manager
    const currentGame = this.getCurrentGame();
    if (currentGame === 'war1') {
      console.log('🗡️ Skipping MapsGrid for WC1 - handled by WC1Manager');
      return;
    }
    
    if (this.isLoading) {
      console.log('⏳ Already loading maps, skipping...');
      return;
    }

    this.isLoading = true;
    this.showLoading();

    try {
      console.log('🔄 Loading maps...');

      // Use provided API URL or build default
      let url;
      if (apiUrl) {
        url = apiUrl;
        console.log(`🌐 Using provided API URL: ${url}`);
      } else {
        // Fallback to default behavior
        const coreState = window.mapsCore?.getState() || {};
        const currentPage = coreState.currentPage || 1;
        const currentTab = coreState.currentTab || 'all';

        const params = new URLSearchParams({
          page: currentPage.toString(),
          sortBy: 'name',  // Default alphabetical sort (numbers first, then letters)
          lean: 'true'  // Request minimal data for grid view
        });

        // Add search term if present
        const searchInput = document.getElementById('search-input');
        if (searchInput?.value.trim()) {
          params.append('search', searchInput.value.trim());
        }

        // Determine API endpoint based on current game
        let apiEndpoint = '/api/war2maps'; // default
        
        if (currentGame === 'war3') {
          apiEndpoint = '/api/war3maps';
        } else if (currentGame === 'war3') {
          apiEndpoint = '/api/war3maps';
        }
        
        url = `${apiEndpoint}?${params}`;
        console.log(`🔍 DEBUG: Loading maps from: ${url} for game: ${currentGame}`);
      }

      const response = await fetch(url, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('📊 Maps API Response:', {
        mapsCount: data.maps?.length || 0,
        totalPages: data.pagination?.totalPages || 0,
        currentPage: data.pagination?.currentPage || 1,
        total: data.pagination?.totalMaps || 0
      });
      
      // Handle pagination response format
      let maps = [];
      let paginationInfo = { page: 1, pages: 1, total: 0 };
      
      if (data.success && data.maps && data.pagination) {
        // War3 API response format: {success: true, maps: [...], pagination: {...}}
        maps = data.maps;
        paginationInfo = {
          page: data.pagination.currentPage || 1,
          pages: data.pagination.totalPages || 1,
          total: data.pagination.totalMaps || 0
        };
      } else if (data.success && data.data && data.pagination) {
        // Alternative API response format: {success: true, data: [...], pagination: {...}}
        maps = data.data;
        paginationInfo = {
          page: data.pagination.currentPage || 1,
          pages: data.pagination.totalPages || 1,
          total: data.pagination.totalMaps || 0
        };
      } else if (data.maps && data.pagination) {
        // Legacy pagination format
        maps = data.maps;
        paginationInfo = data.pagination;
      } else if (Array.isArray(data)) {
        // Direct array format (legacy)
        maps = data;
        paginationInfo = { page: 1, pages: 1, total: data.length };
      } else if (data.maps) {
        // Maps array without pagination
        maps = data.maps;
        paginationInfo = { page: 1, pages: 1, total: data.maps.length };
      } else if (data.data && Array.isArray(data.data)) {
        // Just data array without pagination
        maps = data.data;
        paginationInfo = { page: 1, pages: 1, total: data.data.length };
      } else {
        console.warn('⚠️ Unexpected API response format:', data);
        throw new Error(data.error || 'Invalid response format');
      }
      
      this.currentMaps = maps;
      
      // Update core state with pagination info
      if (window.mapsCore) {
        window.mapsCore.updateState({
          totalPages: paginationInfo.pages,
          currentPage: paginationInfo.page
        });
      }

      // Cache the maps
      this.currentMaps.forEach(map => {
        this.mapsCache.set(map._id, map);
      });

      this.renderMaps(this.currentMaps);
      this.renderPagination(paginationInfo.pages, paginationInfo.page, paginationInfo.total);

      console.log(`✅ Loaded ${this.currentMaps.length} maps (Page ${paginationInfo.page} of ${paginationInfo.pages})`);

    } catch (error) {
      console.error('❌ Failed to load maps:', error);
      this.showError(`Failed to load maps: ${error.message}`);
    } finally {
      this.isLoading = false;
      this.hideLoading();
    }

    // Load hero stats only once per session
    if (!this.statsLoaded) {
      await this.updateHeroStats();
      this.statsLoaded = true;
    }
  }

  /**
   * Initialize intersection observer for lazy loading thumbnails
   */
  initLazyLoading() {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            const dataSrc = img.getAttribute('data-src');
            if (dataSrc) {
              img.src = dataSrc;
              img.removeAttribute('data-src');
              img.classList.remove('lazy-load');
              observer.unobserve(img);
            }
          }
        });
      }, {
        rootMargin: '50px 0px', // Start loading 50px before entering viewport
        threshold: 0.01
      });

      // Observe all lazy load images
      document.querySelectorAll('img.lazy-load').forEach(img => {
        imageObserver.observe(img);
      });

      this.imageObserver = imageObserver;
    }
  }

  /**
   * Render maps in grid format
   */
  renderMaps(maps) {
    // Get the correct grid container based on current game
    const currentGame = this.getCurrentGame();
    
    // Skip rendering for WC1 - handled by WC1Manager
    if (currentGame === 'war1') {
      console.log('🗡️ Skipping MapsGrid rendering for WC1 - handled by WC1Manager');
      return;
    }
    
    let gridId = 'maps-grid'; // default for war2
    
    if (currentGame === 'war3') {
      gridId = 'war3-maps-grid';
    }
    
    const mapsGrid = document.getElementById(gridId);
    if (!mapsGrid) {
      console.warn(`Maps grid container not found: ${gridId}`);
      return;
    }

    if (!maps || maps.length === 0) {
      mapsGrid.innerHTML = this.getNoMapsHTML();
      return;
    }

    console.log(`🎨 Rendering ${maps.length} maps in ${gridId}`);

    const mapsHTML = maps.map(map => this.renderMapCard(map)).join('');
    mapsGrid.innerHTML = mapsHTML;

    // Setup event listeners for the new cards
    this.setupMapCardListeners();
    
    // Initialize lazy loading for newly rendered images
    setTimeout(() => {
      this.initLazyLoading();
    }, 100);
  }

  /**
   * Render a single map card
   */
  renderMapCard(map) {
    const currentGame = this.getCurrentGame();
    
    // Handle War3 maps differently
    if (currentGame === 'war3') {
      return this.renderWar3MapCard(map);
    }
    
    // War2 map card rendering with interactive ratings
    const mapId = map._id || map.id;
    const mapName = map.name || 'Unknown Map';
    const mapSize = map.size || map.mapSize || 'Unknown';
    const playerCount = this.getPlayerCountDisplay(map);
    const thumbnailPath = this.getThumbnailPath(map);
    const strategicData = this.getStrategicData(map);
    
    // Check if user is logged in for interactive stars
    const isLoggedIn = window.mapsCore?.currentUser;
    const userRating = map.userRating || 0;
    const averageRating = map.averageRating || 0;
    
    // Generate star rating HTML
    let starsHTML;
    if (isLoggedIn && window.mapDetails && typeof window.mapDetails.generateInteractiveStarRating === 'function') {
      // Interactive stars for logged-in users - show average rating initially
      starsHTML = window.mapDetails.generateInteractiveStarRating(averageRating, 'small', mapId);
    } else {
      // Static stars for guests
      starsHTML = this.generateStarRating(averageRating);
    }
    
    return `
      <div class="map-card" data-map-id="${mapId}">
        <div class="map-thumbnail-container">
          <img 
            src="${thumbnailPath}" 
            alt="${mapName}" 
            class="map-thumbnail lazy-load"
            loading="lazy"
            style="aspect-ratio: 16/9; object-fit: cover;"
          />
          <div class="map-overlay-info">
            <div class="map-quick-stats">
              <span class="stat-item">
                <i class="fas fa-users"></i>
                ${playerCount}
              </span>
              <span class="stat-item">
                <i class="fas fa-expand-arrows-alt"></i>
                ${mapSize}
              </span>
            </div>
          </div>
        </div>
        
        <div class="map-info">
          <h3 class="map-title">${mapName}</h3>
          <div class="map-meta">
            <span class="map-author">
              <i class="fas fa-user"></i>
              ${map.creator || map.author || 'Unknown'}
            </span>
            <div class="map-rating-container">
              ${starsHTML}
              <span class="average-rating-text">${averageRating.toFixed(1)}</span>
            </div>
          </div>
          
          ${strategicData}
          
          <div class="map-actions">
            <button class="btn-view-details" data-map-id="${mapId}">
              <i class="fas fa-eye"></i>
              View Details
            </button>
            <button class="btn-download" data-map-id="${mapId}">
              <i class="fas fa-download"></i>
              Download
            </button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Render War3 map card with strategic data
   */
  renderWar3MapCard(map) {
    const mapId = map._id || map.id;
    const mapName = map.name || 'Unknown Map';
    const mapSize = map.mapSize || 'Unknown';
    const playerCount = map.players || 0;
    const author = map.author || 'Unknown';
    const tileset = map.tileset || 'Unknown';
    
    // Use extracted thumbnail (not overlay)
    const thumbnailPath = map.thumbnailPath || `/uploads/war3images/${map.filename?.replace('.w3x', '.png')}` || '/uploads/thumbnails/default-map.png';
    
    // Strategic data counts
    const goldmines = map.goldmines || 0;
    const neutralStructures = map.neutralStructures || 0;
    const creepUnits = map.creepUnits || 0;
    const startingLocations = map.startingLocations || 0;
    
    // Check if user is logged in for interactive stars
    const isLoggedIn = window.mapsCore?.currentUser;
    const userRating = map.userRating || 0;
    const averageRating = map.averageRating || 0;
    
    // Generate star rating HTML
    let starsHTML;
    if (isLoggedIn && window.mapDetails && typeof window.mapDetails.generateInteractiveStarRating === 'function') {
      // Interactive stars for logged-in users - show average rating initially
      starsHTML = window.mapDetails.generateInteractiveStarRating(averageRating, 'small', mapId);
    } else {
      // Static stars for guests
      starsHTML = this.generateStarRating(averageRating);
    }

    return `
      <div class="map-card war3-map-card" data-map-id="${mapId}">
        <div class="map-thumbnail-container">
          <img 
            src="${thumbnailPath}" 
            alt="${mapName}" 
               class="map-thumbnail lazy-load"
            loading="lazy"
            data-map-id="${mapId}"
          />
          <div class="map-overlay-info">
            <div class="map-quick-stats">
              <span class="stat-item">
                <i class="fas fa-users"></i>
                ${playerCount}P
              </span>
              <span class="stat-item">
                <i class="fas fa-expand-arrows-alt"></i>
                ${mapSize}
              </span>
            </div>
            <div class="fullscreen-icon" title="View Fullscreen">
              <i class="fas fa-expand"></i>
          </div>
          </div>
          <div class="war3-tileset-badge">${tileset}</div>
        </div>
        
        <div class="map-info">
          <h3 class="map-title">${mapName}</h3>
          <div class="map-meta">
            <span class="map-author">
              <i class="fas fa-user"></i>
              ${author}
            </span>
            <div class="map-rating-container">
              ${starsHTML}
              <span class="average-rating-text">${averageRating.toFixed(1)}</span>
            </div>
          </div>
          
          <div class="strategic-items">
            <div class="strategic-item" title="Goldmines">
              <i class="strategic-icon fas fa-coins"></i>
              <span>${goldmines}</span>
            </div>
            <div class="strategic-item" title="Neutral Structures">
              <i class="strategic-icon fas fa-building"></i>
              <span>${neutralStructures}</span>
            </div>
            <div class="strategic-item" title="Creep Units">
              <i class="strategic-icon fas fa-skull"></i>
              <span>${creepUnits}</span>
              </div>
            <div class="strategic-item" title="Starting Locations">
              <i class="strategic-icon fas fa-flag"></i>
              <span>${startingLocations}</span>
            </div>
          </div>
          
          <div class="map-actions">
            <button class="btn-view-details" data-map-id="${mapId}">
              <i class="fas fa-eye"></i>
              View Details
            </button>
            <button class="btn-download" data-map-id="${mapId}">
              <i class="fas fa-download"></i>
              Download
            </button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Format file size in human readable format
   */
  formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
  }

  /**
   * Setup event listeners for map cards
   */
  setupMapCardListeners() {
    const mapCards = document.querySelectorAll('.map-card');
    
    mapCards.forEach(card => {
      // View details functionality
      const viewDetailsBtn = card.querySelector('.btn-view-details');
      if (viewDetailsBtn) {
        viewDetailsBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          const mapId = viewDetailsBtn.dataset.mapId || card.dataset.mapId;
          console.log('🔍 View details clicked for map:', mapId);
          this.viewMapDetails(mapId);
        });
      }

      // Download functionality  
      const downloadBtn = card.querySelector('.btn-download');
      if (downloadBtn) {
        downloadBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          const mapId = downloadBtn.dataset.mapId || card.dataset.mapId;
          console.log('⬇️ Download clicked for map:', mapId);
          this.downloadMap(mapId);
        });
      }

      // Delete functionality (admin only)
      const deleteBtn = card.querySelector('.delete-btn');
      if (deleteBtn) {
        deleteBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          const mapId = card.dataset.mapId;
          console.log('🗑️ Delete clicked for map:', mapId);
          this.deleteMap(mapId);
        });
      }

      // Fixed fullscreen functionality
      const fullscreenBtn = card.querySelector('.fullscreen-icon');
      const mapImage = card.querySelector('.map-thumbnail');
      
      if (fullscreenBtn && mapImage) {
        // Remove any existing listeners by cloning
        const newFullscreenBtn = fullscreenBtn.cloneNode(true);
        fullscreenBtn.parentNode.replaceChild(newFullscreenBtn, fullscreenBtn);
        
        newFullscreenBtn.addEventListener('click', async (e) => {
          e.stopPropagation();
          e.preventDefault();
          console.log('🖼️ Grid fullscreen button clicked for map:', card.dataset.mapId);
          
          const mapId = mapImage.dataset.mapId || card.dataset.mapId;
          const currentGame = this.getCurrentGame();
          let mapData = this.getCachedMap(mapId);
          
          // If cached data doesn't have strategic info, fetch full data based on game type
          if (mapData && (!mapData.strategicData && !mapData.strategicAnalysis)) {
            console.log('📊 Fetching full map data for overlays...');
            try {
              let apiEndpoint;
              if (currentGame === 'war3') {
                apiEndpoint = `/api/war3maps/${mapId}`;
              } else {
                apiEndpoint = `/api/war2maps/${mapId}`;
              }
              
              const response = await fetch(apiEndpoint);
              if (response.ok) {
                const fullMapData = await response.json();
                mapData = fullMapData.success ? fullMapData.data : fullMapData;
                console.log('✅ Full map data loaded for overlays');
              }
            } catch (error) {
              console.warn('⚠️ Could not fetch full map data:', error);
            }
          }
          
          console.log('📊 Map data for fullscreen:', mapData ? 'Found' : 'Not found');
          
          // For WC3 maps, use the WC3-specific fullscreen modal
          if (currentGame === 'war3') {
            this.showWC3FullscreenImage(mapImage.src, mapImage.alt, mapData);
          } else {
            this.showFullscreenImage(mapImage.src, mapImage.alt, mapData);
          }
        });
        
        console.log('✅ Grid fullscreen button initialized for map:', card.dataset.mapId);
      }

      // Thumbnail click functionality for fullscreen
      if (mapImage) {
        mapImage.addEventListener('click', async (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('🖼️ Grid thumbnail clicked for map:', card.dataset.mapId);
          
          const mapId = mapImage.dataset.mapId || card.dataset.mapId;
          const currentGame = this.getCurrentGame();
          let mapData = this.getCachedMap(mapId);
          
          // If cached data doesn't have strategic info, fetch full data based on game type
          if (mapData && (!mapData.strategicData && !mapData.strategicAnalysis)) {
            console.log('📊 Fetching full map data for overlays...');
            try {
              let apiEndpoint;
              if (currentGame === 'war3') {
                apiEndpoint = `/api/war3maps/${mapId}`;
              } else {
                apiEndpoint = `/api/war2maps/${mapId}`;
              }
              
              const response = await fetch(apiEndpoint);
              if (response.ok) {
                const fullMapData = await response.json();
                mapData = fullMapData.success ? fullMapData.data : fullMapData;
                console.log('✅ Full map data loaded for overlays');
              }
            } catch (error) {
              console.warn('⚠️ Could not fetch full map data:', error);
            }
          }
          
          console.log('📊 Map data for fullscreen:', mapData ? 'Found' : 'Not found');
          
          // For WC3 maps, use the WC3-specific fullscreen modal
          if (currentGame === 'war3') {
            this.showWC3FullscreenImage(mapImage.src, mapImage.alt, mapData);
          } else {
            this.showFullscreenImage(mapImage.src, mapImage.alt, mapData);
          }
        });
      }

      // Star rating functionality
      const stars = card.querySelectorAll('.rating-star');
      stars.forEach(star => {
        star.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          const rating = parseInt(star.dataset.rating);
          const mapId = card.dataset.mapId;
          console.log(`⭐ Grid star rating clicked: ${rating} for map ${mapId}`);
          
          // Call the global rating handler if it exists
          if (window.mapDetails && typeof window.mapDetails.handleStarClick === 'function') {
            window.mapDetails.handleStarClick(rating, mapId);
          } else {
            console.warn('⚠️ Map details rating handler not available');
          }
        });
      });
      
      // Setup interactive star rating for logged-in users
      const interactiveStars = card.querySelector('.interactive-stars');
      if (interactiveStars) {
        // Determine game type for this card
        const currentGame = this.getCurrentGame();
        const gameType = currentGame === 'war3' ? 'war3' : 'war2';
        
        // Star click handlers
        interactiveStars.addEventListener('click', (e) => {
          if (e.target.classList.contains('rating-star')) {
            e.stopPropagation();
            const rating = parseInt(e.target.dataset.rating);
            const mapId = card.dataset.mapId;
            console.log(`⭐ ${gameType.toUpperCase()} Star clicked: ${rating} for map ${mapId}`);
            
            // Use MapDetails rating functionality if available
            if (window.mapDetails && typeof window.mapDetails.handleStarClick === 'function') {
              window.mapDetails.handleStarClick(rating, mapId, gameType);
            } else {
              console.warn('MapDetails not available for rating');
            }
          }
        });

        // Star hover effects
        interactiveStars.addEventListener('mouseover', (e) => {
          if (e.target.classList.contains('rating-star')) {
            const rating = parseInt(e.target.dataset.rating);
            this.highlightStarsOnHover(interactiveStars, rating);
          }
        });

        interactiveStars.addEventListener('mouseout', () => {
          this.resetStarHighlight(interactiveStars);
        });
      }
    });

    console.log(`✅ Event listeners setup for ${mapCards.length} map cards`);
  }

  /**
   * Highlight stars on hover for map cards
   */
  highlightStarsOnHover(starsContainer, rating) {
    const stars = starsContainer.querySelectorAll('.rating-star');
    stars.forEach((star, index) => {
      const starRating = index + 1;
      if (starRating <= rating) {
        star.className = 'fas fa-star rating-star';
        star.style.color = '#ffd700';
        star.style.transform = 'scale(1.1)';
      } else {
        star.className = 'far fa-star rating-star';
        star.style.color = '#6b7280';
        star.style.transform = 'scale(1)';
      }
    });
  }

  /**
   * Reset star highlight for map cards
   */
  resetStarHighlight(starsContainer) {
    const stars = starsContainer.querySelectorAll('.rating-star');
    stars.forEach((star, index) => {
      // Reset to original state - this would need to be enhanced to remember user's rating
      star.style.transform = 'scale(1)';
    });
  }
  
  /**
   * Show fullscreen image with enhanced modal (matches MapDetails implementation)
   */
  showFullscreenImage(src, alt, mapData = null) {
    console.log('🖼️ Opening fullscreen image with enhanced modal');
    
    // Remove any existing fullscreen modal
    const existingModal = document.getElementById('fullscreen-modal-enhanced');
    if (existingModal) {
      existingModal.remove();
    }

    // Create fullscreen modal (same as MapDetails)
    const modal = document.createElement('div');
    modal.id = 'fullscreen-modal-enhanced';
    modal.className = 'modal';

    // Create image container
    const imageContainer = document.createElement('div');
    imageContainer.className = 'fullscreen-image-container';

    // Create close button
    const closeButton = document.createElement('span');
    closeButton.className = 'close-modal';
    closeButton.id = 'close-fullscreen-enhanced';
    closeButton.innerHTML = '×';

    // Create image
    const img = document.createElement('img');
    img.className = 'fullscreen-image';
    img.alt = alt;
    img.src = src;

    // Assemble the modal
    imageContainer.appendChild(img);
    modal.appendChild(closeButton);
    modal.appendChild(imageContainer);
    document.body.appendChild(modal);

    // Add overlays if map data is available
    if (mapData) {
      const createOverlays = (attempts = 0) => {
        const maxAttempts = 20; // Max 2 seconds of retries
        
        // Wait for layout to stabilize and ensure image dimensions are available
        setTimeout(() => {
          // Check if modal is still open and image has proper dimensions
          const modalStillOpen = modal.style.display !== 'none' && modal.parentNode;
          if (!modalStillOpen) {
            console.log('🚫 Modal closed, skipping overlay creation');
            return;
          }
          
          if (img.offsetWidth > 0 && img.offsetHeight > 0 && img.complete) {
            console.log('✅ Creating overlays with dimensions:', img.offsetWidth, 'x', img.offsetHeight);
            this.createEnhancedOverlays(imageContainer, img, mapData);
          } else if (attempts < maxAttempts) {
            // Retry if image dimensions aren't ready yet
            console.log(`🔄 Retrying overlay creation (attempt ${attempts + 1}/${maxAttempts})`);
            createOverlays(attempts + 1);
          } else {
            console.warn('❌ Failed to create overlays after max attempts');
          }
        }, attempts === 0 ? 250 : 100); // Longer initial delay, shorter retries
      };
      
      if (img.complete && img.naturalHeight !== 0) {
        createOverlays();
      } else {
        img.onload = createOverlays;
      }
    }

    // Show modal with animation
    setTimeout(() => {
      modal.classList.add('show');
    }, 10);

    // Close modal functions
    const closeModal = () => {
      modal.classList.remove('show');
      setTimeout(() => {
        if (modal && modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
      }, 300);
    };

    // Event listeners for closing
    closeButton.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });

    // Escape key listener
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        closeModal();
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);

    console.log('✅ Enhanced fullscreen modal opened with overlays');
  }

  /**
   * Show WC3 fullscreen image with proper scaling
   */
  showWC3FullscreenImage(src, alt, mapData = null) {
    console.log('🖼️ Opening WC3 fullscreen image');
    
    // Remove any existing fullscreen modal
    const existingModal = document.getElementById('war3-fullscreen-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // Create WC3 fullscreen modal with canvas overlay system
    const modal = document.createElement('div');
    modal.id = 'war3-fullscreen-modal';
    modal.className = 'modal war3-fullscreen-modal';

    modal.innerHTML = `
      <div class="war3-fullscreen-container">
        <div class="war3-canvas-container">
          <canvas id="war3-fullscreen-canvas" class="war3-map-canvas"></canvas>
          <div class="overlay-controls">
            <label class="overlay-toggle">
              <input type="checkbox" id="fullscreen-overlay-toggle" checked>
              <span class="toggle-slider"></span>
              <span class="toggle-label">Strategic Overlay</span>
            </label>
        </div>
      </div>
        <div class="war3-title-overlay">
          <h2>${alt}</h2>
          <p>Warcraft III Map - Strategic View</p>
        </div>
      </div>
      <button id="close-war3-fullscreen" title="Close Fullscreen">×</button>
    `;

    document.body.appendChild(modal);

    // Initialize War3 overlay system for fullscreen
    if (mapData) {
      this.initializeWar3FullscreenOverlay(mapData);
    }

    // Show modal with animation
    setTimeout(() => {
      modal.classList.add('show');
    }, 10);

    // Close modal functions
    const closeButton = modal.querySelector('#close-war3-fullscreen');
    const closeModal = () => {
      modal.classList.remove('show');
      setTimeout(() => {
        if (modal && modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
      }, 300);
    };

    // Event listeners for closing
    if (closeButton) {
      closeButton.addEventListener('click', closeModal);
    }
    
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });

    // Escape key listener
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        closeModal();
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);

    console.log('✅ WC3 fullscreen modal opened with overlay system');
  }

  /**
   * Initialize War3 overlay system for fullscreen
   */
  async initializeWar3FullscreenOverlay(mapData) {
    try {
      console.log('🎨 Initializing War3 fullscreen overlay system...');
      
      // Initialize overlay renderer if not already done
      if (!window.war3OverlayRenderer) {
        window.war3OverlayRenderer = new War3OverlayRenderer();
      }
      
      const renderer = window.war3OverlayRenderer;
      const canvasId = 'war3-fullscreen-canvas';
      
      // Wait a bit for DOM to be ready
      setTimeout(async () => {
        try {
          // Initial render with overlay enabled
          await renderer.renderOverlay(canvasId, mapData, true);
          
          // Add hover tooltips
          renderer.addHoverTooltips(canvasId, mapData);
          
          // Set up overlay toggle
          const overlayToggle = document.getElementById('fullscreen-overlay-toggle');
          if (overlayToggle) {
            overlayToggle.addEventListener('change', async (e) => {
              const showOverlay = e.target.checked;
              console.log(`🎨 Fullscreen overlay toggle: ${showOverlay ? 'ON' : 'OFF'}`);
              await renderer.renderOverlay(canvasId, mapData, showOverlay);
              
              // Re-add tooltips after re-render
              if (showOverlay) {
                renderer.addHoverTooltips(canvasId, mapData);
              }
            });
          }
          
          console.log('✅ War3 fullscreen overlay system initialized');
        } catch (error) {
          console.error('❌ Error in delayed fullscreen overlay initialization:', error);
        }
      }, 100);
      
    } catch (error) {
      console.error('❌ Error initializing War3 fullscreen overlay:', error);
    }
  }

  /**
   * Create enhanced overlays for fullscreen (same as MapDetails)
   */
  createEnhancedOverlays(container, mapImage, map) {
    if (!container || !mapImage) {
      console.warn('❌ Missing container or image for overlays');
      return;
    }

    const strategicData = map.strategicData || map.strategicAnalysis || {};
    
    // Remove existing overlays
    const existingOverlays = container.querySelectorAll('.enhanced-strategic-overlays');
    existingOverlays.forEach(overlay => overlay.remove());

    // Create overlay container
    const overlayContainer = document.createElement('div');
    overlayContainer.className = 'enhanced-strategic-overlays';
    container.appendChild(overlayContainer);

    // Enhanced image dimension calculation for different contexts
    let imageWidth, imageHeight;
    const imageRect = mapImage.getBoundingClientRect();
    
    // Determine display context and calculate proper dimensions
    const isFullscreen = container.classList.contains('fullscreen-image-container');
    const isMapDetails = container.classList.contains('map-image-container');
    
    if (isFullscreen) {
      // For fullscreen: use natural dimensions scaled to container
      const naturalAspect = mapImage.naturalWidth / mapImage.naturalHeight;
      const containerWidth = imageRect.width || mapImage.offsetWidth;
      const containerHeight = imageRect.height || mapImage.offsetHeight;
      
      // Calculate effective display dimensions respecting aspect ratio
      if (containerWidth / containerHeight > naturalAspect) {
        // Height is limiting factor
        imageHeight = containerHeight;
        imageWidth = containerHeight * naturalAspect;
      } else {
        // Width is limiting factor  
        imageWidth = containerWidth;
        imageHeight = containerWidth / naturalAspect;
      }
      
      console.log('🖼️ Fullscreen dimensions calculated:', {
        container: { width: containerWidth, height: containerHeight },
        natural: { width: mapImage.naturalWidth, height: mapImage.naturalHeight, aspect: naturalAspect },
        calculated: { width: imageWidth, height: imageHeight }
      });
    } else {
      // For map details or other contexts: use offset dimensions
      imageWidth = mapImage.offsetWidth || imageRect.width;
      imageHeight = mapImage.offsetHeight || imageRect.height;
      
      console.log('📱 Standard dimensions used:', { imageWidth, imageHeight });
    }
    
          // Get actual map dimensions from strategic data, with fallbacks
      // Handle both new object format {width: 64, height: 64} and old string format "64x64"
      let mapWidth, mapHeight;
      
      if (strategicData.mapSize?.width) {
        // New object format
        mapWidth = strategicData.mapSize.width;
        mapHeight = strategicData.mapSize.height;
      } else if (typeof strategicData.mapSize === 'string') {
        // Old string format "64x64"
        const dimensions = strategicData.mapSize.split('x').map(d => parseInt(d));
        mapWidth = dimensions[0] || 128;
        mapHeight = dimensions[1] || 128;
      } else {
        // Fallbacks
        mapWidth = map.dimensions?.width || (strategicData.totalTiles ? Math.sqrt(strategicData.totalTiles) : 128);
        mapHeight = map.dimensions?.height || (strategicData.totalTiles ? Math.sqrt(strategicData.totalTiles) : 128);
      }
    
    console.log('📍 War2 Enhanced overlay positioning Debug:', { 
      imageWidth, 
      imageHeight, 
      mapWidth, 
      mapHeight,
      context: isFullscreen ? 'fullscreen' : isMapDetails ? 'details' : 'other',
      mapDimensions: map.dimensions,
      mapSizeFromStrategic: strategicData.mapSize,
      totalTiles: strategicData.totalTiles,
      goldmineCount: strategicData.goldmines?.length || 0,
      startingPositionCount: strategicData.startingPositions?.length || 0
    });
    
    // Calculate actual coordinate bounds from the data (similar to War3 system)
    const coordinateBounds = this.calculateWar2CoordinateBounds(strategicData);
    console.log('🗺️ War2 Calculated coordinate bounds:', coordinateBounds);
    
    // Debug coordinate ranges in the strategic data
    if (strategicData.goldmines && strategicData.goldmines.length > 0) {
      const goldmineCoords = strategicData.goldmines.map(gm => ({ x: gm.x, y: gm.y }));
      const xCoords = goldmineCoords.map(c => c.x);
      const yCoords = goldmineCoords.map(c => c.y);
      console.log('🔍 War2 Goldmine coordinate analysis:', {
        count: goldmineCoords.length,
        xRange: { min: Math.min(...xCoords), max: Math.max(...xCoords) },
        yRange: { min: Math.min(...yCoords), max: Math.max(...yCoords) },
        sample: goldmineCoords.slice(0, 3)
      });
    }
    
    if (strategicData.startingPositions && strategicData.startingPositions.length > 0) {
      const positionCoords = strategicData.startingPositions.map(sp => ({ x: sp.x, y: sp.y }));
      const xCoords = positionCoords.map(c => c.x);
      const yCoords = positionCoords.map(c => c.y);
      console.log('🔍 War2 Starting position coordinate analysis:', {
        count: positionCoords.length,
        xRange: { min: Math.min(...xCoords), max: Math.max(...xCoords) },
        yRange: { min: Math.min(...yCoords), max: Math.max(...yCoords) },
        sample: positionCoords.slice(0, 3)
      });
    }

    // Only proceed if we have valid dimensions
    if (!imageWidth || !imageHeight || imageWidth === 0 || imageHeight === 0) {
      console.warn('❌ Invalid image dimensions, skipping overlay creation');
      return;
    }

          // Create goldmine overlays
      if (strategicData.goldmines && Array.isArray(strategicData.goldmines)) {
        strategicData.goldmines.forEach((goldmine, index) => {
          this.createGoldmineOverlay(overlayContainer, goldmine, index, imageWidth, imageHeight, mapWidth, mapHeight, isFullscreen, coordinateBounds);
        });
      }

      // Create starting position overlays  
      if (strategicData.startingPositions && Array.isArray(strategicData.startingPositions)) {
        strategicData.startingPositions.forEach((position, index) => {
          this.createStartingPositionOverlay(overlayContainer, position, index, imageWidth, imageHeight, mapWidth, mapHeight, isFullscreen, coordinateBounds);
        });
            }

      // IMPORTANT: For War2 maps, we DON'T create any additional tooltip areas 
      // beyond the overlays themselves, since the new system attaches tooltips directly to overlays.
      // This prevents white circle tooltip areas from appearing.

      console.log(`✅ Created War2 overlays with integrated tooltips: ${strategicData.goldmines?.length || 0} goldmines, ${strategicData.startingPositions?.length || 0} positions`);
  }

  /**
   * Show War2-specific tooltip
   */
  showWar2Tooltip(event, data, type) {
    console.log('🎯 MapsGrid Creating War2 tooltip:', type, data);
    this.hideWar2Tooltip(); // Remove any existing tooltip

    const tooltip = document.createElement('div');
    tooltip.className = 'war2-tooltip';
    
    // Use much higher z-index for fullscreen mode
    const isInFullscreen = document.querySelector('#fullscreen-modal-enhanced.show') !== null;
    const zIndex = isInFullscreen ? 100000 : 10000;
    
    tooltip.style.cssText = `
      position: fixed;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      pointer-events: none;
      z-index: ${zIndex};
      max-width: 200px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
      font-family: Arial, sans-serif;
    `;
    
    console.log('🎯 MapsGrid Tooltip z-index set to:', zIndex, 'Fullscreen detected:', isInFullscreen);

    let tooltipContent = '';
    if (type === 'goldmine') {
      const goldFormatted = data.gold ? data.gold.toLocaleString() : '0';
      tooltipContent = `
        <div style="font-weight: bold; color: #FFD700;">${data.name}</div>
        <div style="margin-top: 4px;">Gold: ${goldFormatted}</div>
        <div>Category: ${data.category}</div>
        <div style="color: #ccc; font-size: 10px; margin-top: 4px;">Location: ${data.location}</div>
      `;
    } else if (type === 'player') {
      tooltipContent = `
        <div style="font-weight: bold; color: #87CEEB;">${data.name}</div>
        <div style="margin-top: 4px;">Race: ${data.race}</div>
        <div style="color: #ccc; font-size: 10px; margin-top: 4px;">Location: ${data.location}</div>
      `;
    }

    tooltip.innerHTML = tooltipContent;
    document.body.appendChild(tooltip);

    // Position tooltip near cursor but avoid screen edges
    const x = event.clientX + 15;
    const y = event.clientY - 40;
    
    tooltip.style.left = x + 'px';
    tooltip.style.top = y + 'px';
    
    console.log('🎯 MapsGrid Tooltip positioned at:', x, y, 'Content:', tooltipContent.replace(/<[^>]*>/g, '').trim());

    // Store reference for cleanup
    this._war2Tooltip = tooltip;
  }

  /**
   * Hide War2-specific tooltip
   */
  hideWar2Tooltip() {
    if (this._war2Tooltip) {
      this._war2Tooltip.remove();
      this._war2Tooltip = null;
    }
  }

  /**
   * Calculate coordinate bounds from War2 strategic data
   */
  calculateWar2CoordinateBounds(strategicData) {
    const allCoordinates = [];

    // Collect all coordinates from goldmines and starting positions
    if (strategicData.goldmines) {
      strategicData.goldmines.forEach(item => {
        if (item.x !== undefined && item.y !== undefined) {
          allCoordinates.push({ x: item.x, y: item.y });
        }
      });
    }

    if (strategicData.startingPositions) {
      strategicData.startingPositions.forEach(item => {
        if (item.x !== undefined && item.y !== undefined) {
          allCoordinates.push({ x: item.x, y: item.y });
        }
      });
    }

    if (allCoordinates.length === 0) {
      // Fallback to traditional map size if no coordinates found
      return {
        minX: 0,
        maxX: 128,
        minY: 0,
        maxY: 128
      };
    }

    const xs = allCoordinates.map(c => c.x);
    const ys = allCoordinates.map(c => c.y);

    return {
      minX: Math.min(...xs),
      maxX: Math.max(...xs),
      minY: Math.min(...ys),
      maxY: Math.max(...ys)
    };
  }

  /**
   * Transform War2 coordinates to screen percentages using actual coordinate bounds
   */
  transformWar2Coordinates(worldX, worldY, coordinateBounds) {
    // Add 10% padding to bounds to ensure everything fits
    const xRange = coordinateBounds.maxX - coordinateBounds.minX;
    const yRange = coordinateBounds.maxY - coordinateBounds.minY;
    const xPadding = xRange * 0.1;
    const yPadding = yRange * 0.1;
    
    const paddedBounds = {
      minX: coordinateBounds.minX - xPadding,
      maxX: coordinateBounds.maxX + xPadding,
      minY: coordinateBounds.minY - yPadding,
      maxY: coordinateBounds.maxY + yPadding
    };

    // Convert to 0-1 range using actual coordinate bounds
    const normalizedX = (worldX - paddedBounds.minX) / (paddedBounds.maxX - paddedBounds.minX);
    const normalizedY = (worldY - paddedBounds.minY) / (paddedBounds.maxY - paddedBounds.minY); // War2 Y=0 is TOP, no flip needed!
    
    // Convert to percentages
    const leftPercent = normalizedX * 100;
    const topPercent = normalizedY * 100;
    
    console.log(`🗺️ War2 Transform: world(${worldX}, ${worldY}) → normalized(${normalizedX.toFixed(3)}, ${normalizedY.toFixed(3)}) → percent(${leftPercent.toFixed(1)}%, ${topPercent.toFixed(1)}%)`);
    
    return { leftPercent, topPercent };
  }

  /**
   * Create goldmine overlay element (same as MapDetails)
   */
  createGoldmineOverlay(container, goldmine, index, imageWidth, imageHeight, mapWidth = 128, mapHeight = 128, isFullscreen = false, coordinateBounds = null) {
    const overlay = document.createElement('div');
    overlay.className = 'enhanced-goldmine-overlay';
    
    // Debug War2 coordinate system
    console.log(`🔍 War2 Goldmine ${index + 1} Debug:`, {
      coordinates: { x: goldmine.x, y: goldmine.y },
      mapDimensions: { width: mapWidth, height: mapHeight },
      imageDimensions: { width: imageWidth, height: imageHeight },
      isFullscreen: isFullscreen,
      hasCoordinateBounds: !!coordinateBounds,
      coordinateBounds: coordinateBounds
    });
    
    // War2 coordinate transformation - appears to use 0-128 range, not negative coords
    let leftPercent, topPercent;
    
    // Debug the coordinate ranges we're working with
    console.log(`🔍 War2 Goldmine coords: (${goldmine.x}, ${goldmine.y}), bounds: X(${coordinateBounds?.minX}, ${coordinateBounds?.maxX}) Y(${coordinateBounds?.minY}, ${coordinateBounds?.maxY})`);
    
    if (coordinateBounds && (coordinateBounds.maxX > 128 || coordinateBounds.minX < 0)) {
      // Use bounds-based transformation for maps with extended coordinate ranges
      const transformed = this.transformWar2Coordinates(goldmine.x, goldmine.y, coordinateBounds);
      leftPercent = transformed.leftPercent;
      topPercent = transformed.topPercent;
      console.log(`📍 War2 Goldmine bounds-based (${isFullscreen ? 'fullscreen' : 'detail'}): leftPercent=${leftPercent.toFixed(1)}%, topPercent=${topPercent.toFixed(1)}%`);
    } else {
      // Use actual map dimensions instead of assuming 128x128
      leftPercent = (goldmine.x / mapWidth) * 100;
      topPercent = (goldmine.y / mapHeight) * 100; // War2 Y=0 is TOP, no flip needed!
      console.log(`📍 War2 Goldmine standard (${isFullscreen ? 'fullscreen' : 'detail'}): leftPercent=${leftPercent.toFixed(1)}%, topPercent=${topPercent.toFixed(1)}% [using mapSize ${mapWidth}x${mapHeight}] - NO Y-FLIP`);
    }
    
    // Adjust positioning for fullscreen mode using actual image position
    if (isFullscreen) {
      const imageElement = container.querySelector('.fullscreen-image');
      if (imageElement) {
        const containerRect = container.getBoundingClientRect();
        const imageRect = imageElement.getBoundingClientRect();
        
        // Calculate the actual image position within container
        const imageOffsetX = imageRect.left - containerRect.left;
        const imageOffsetY = imageRect.top - containerRect.top;
        
        // Convert map coordinates to pixels within the actual image
        const leftPx = (leftPercent / 100) * imageRect.width;
        const topPx = (topPercent / 100) * imageRect.height;
        
        // Add the image's offset position within the container
        const absoluteLeftPx = leftPx + imageOffsetX;
        const absoluteTopPx = topPx + imageOffsetY;
        
        // Convert to percentage of container size
        leftPercent = (absoluteLeftPx / containerRect.width) * 100;
        topPercent = (absoluteTopPx / containerRect.height) * 100;
        
        console.log('🎯 War2 Fullscreen overlay adjustment (refined):', {
          originalCoords: { x: goldmine.x, y: goldmine.y },
          originalPercent: { left: (goldmine.x / mapWidth) * 100, top: (goldmine.y / mapHeight) * 100 },
          imageRect: { left: imageRect.left, top: imageRect.top, width: imageRect.width, height: imageRect.height },
          containerRect: { left: containerRect.left, top: containerRect.top, width: containerRect.width, height: containerRect.height },
          imageOffset: { x: imageOffsetX, y: imageOffsetY },
          adjustedPercent: { left: leftPercent, top: topPercent },
          mapDimensions: { width: mapWidth, height: mapHeight }
        });
      }
    }
    
    // Handle different field names for gold amount
    const goldAmount = goldmine.amount || goldmine.gold || goldmine.goldAmount || 0;
    const goldColor = this.getGoldmineColor(goldAmount);
    
    // Calculate size based on image size for better scaling
    const baseSize = isFullscreen ? 
      Math.max(16, Math.min(imageWidth, imageHeight) * 0.02) : 
      Math.max(20, Math.min(imageWidth, imageHeight) * 0.04); // Larger for details view
    
    console.log('🎯 MapsGrid Goldmine overlay size calculated:', { baseSize, isFullscreen, imageWidth, imageHeight });
    
    // Style the overlay with vibrant colors
    overlay.style.cssText = `
      position: absolute;
      left: ${leftPercent}%;
      top: ${topPercent}%;
      width: ${baseSize}px;
      height: ${baseSize}px;
      background-color: ${goldColor} !important;
      border: 3px solid #000000 !important;
      box-shadow: 0 0 15px ${goldColor} !important;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      cursor: pointer;
      z-index: 1000;
      transition: all 0.2s ease;
    `;

    // Add hover effects with War2-specific tooltip data
    overlay.addEventListener('mouseenter', (e) => {
      console.log('🏆 MapsGrid Goldmine hover ENTERED:', goldmine.x, goldmine.y, 'fullscreen:', isFullscreen);
      const goldAmount = goldmine.amount || goldmine.gold || goldmine.goldAmount || 0;
      const tooltipData = {
        name: `Goldmine`,
        gold: goldAmount,
        location: `(${goldmine.x}, ${goldmine.y})`,
        category: this.getGoldmineCategory(goldAmount)
      };
      this.showWar2Tooltip(e, tooltipData, 'goldmine');
      overlay.style.transform = 'translate(-50%, -50%) scale(1.6)';
      overlay.style.boxShadow = `0 0 25px ${goldColor}`;
    });

    overlay.addEventListener('mouseleave', () => {
      console.log('🏆 MapsGrid Goldmine hover LEFT:', goldmine.x, goldmine.y, 'fullscreen:', isFullscreen);
      this.hideWar2Tooltip();
      overlay.style.transform = 'translate(-50%, -50%) scale(1)';
      overlay.style.boxShadow = `0 0 15px ${goldColor}`;
    });

    container.appendChild(overlay);
  }

  /**
   * Create starting position overlay element (same as MapDetails)
   */
  createStartingPositionOverlay(container, position, index, imageWidth, imageHeight, mapWidth = 128, mapHeight = 128, isFullscreen = false, coordinateBounds = null) {
    const overlay = document.createElement('div');
    overlay.className = 'enhanced-starting-position-overlay';
    
    // Debug War2 coordinate system
    console.log(`🔍 War2 Starting Position ${index + 1} Debug:`, {
      coordinates: { x: position.x, y: position.y },
      mapDimensions: { width: mapWidth, height: mapHeight },
      imageDimensions: { width: imageWidth, height: imageHeight },
      isFullscreen: isFullscreen,
      hasCoordinateBounds: !!coordinateBounds,
      coordinateBounds: coordinateBounds
    });
    
    // War2 coordinate transformation - appears to use 0-128 range, not negative coords
    let leftPercent, topPercent;
    
    // Debug the coordinate ranges we're working with
    console.log(`🔍 War2 Position coords: (${position.x}, ${position.y}), bounds: X(${coordinateBounds?.minX}, ${coordinateBounds?.maxX}) Y(${coordinateBounds?.minY}, ${coordinateBounds?.maxY})`);
    
    if (coordinateBounds && (coordinateBounds.maxX > 128 || coordinateBounds.minX < 0)) {
      // Use bounds-based transformation for maps with extended coordinate ranges
      const transformed = this.transformWar2Coordinates(position.x, position.y, coordinateBounds);
      leftPercent = transformed.leftPercent;
      topPercent = transformed.topPercent;
      console.log(`📍 War2 Position bounds-based (${isFullscreen ? 'fullscreen' : 'detail'}): leftPercent=${leftPercent.toFixed(1)}%, topPercent=${topPercent.toFixed(1)}%`);
    } else {
      // Use actual map dimensions instead of assuming 128x128
      leftPercent = (position.x / mapWidth) * 100;
      topPercent = (position.y / mapHeight) * 100; // War2 Y=0 is TOP, no flip needed!
      console.log(`📍 War2 Position standard (${isFullscreen ? 'fullscreen' : 'detail'}): leftPercent=${leftPercent.toFixed(1)}%, topPercent=${topPercent.toFixed(1)}% [using mapSize ${mapWidth}x${mapHeight}] - NO Y-FLIP`);
    }
    
    // Adjust positioning for fullscreen mode using actual image position
    if (isFullscreen) {
      const imageElement = container.querySelector('.fullscreen-image');
      if (imageElement) {
        const containerRect = container.getBoundingClientRect();
        const imageRect = imageElement.getBoundingClientRect();
        
        // Calculate the actual image position within container
        const imageOffsetX = imageRect.left - containerRect.left;
        const imageOffsetY = imageRect.top - containerRect.top;
        
        // Convert map coordinates to pixels within the actual image
        const leftPx = (leftPercent / 100) * imageRect.width;
        const topPx = (topPercent / 100) * imageRect.height;
        
        // Add the image's offset position within the container
        const absoluteLeftPx = leftPx + imageOffsetX;
        const absoluteTopPx = topPx + imageOffsetY;
        
        // Convert to percentage of container size
        leftPercent = (absoluteLeftPx / containerRect.width) * 100;
        topPercent = (absoluteTopPx / containerRect.height) * 100;
        
        console.log('🎯 Fullscreen position overlay adjustment (refined):', {
          originalPercent: { left: (position.x / mapWidth) * 100, top: (position.y / mapHeight) * 100 },
          imageRect: { left: imageRect.left, top: imageRect.top, width: imageRect.width, height: imageRect.height },
          containerRect: { left: containerRect.left, top: containerRect.top, width: containerRect.width, height: containerRect.height },
          imageOffset: { x: imageOffsetX, y: imageOffsetY },
          adjustedPercent: { left: leftPercent, top: topPercent }
        });
      }
    }
    
    const playerColor = this.getPlayerColor(position.race || 'unknown');
    
    // Calculate size based on image size for better scaling
    const baseSize = isFullscreen ? 
      Math.max(18, Math.min(imageWidth, imageHeight) * 0.025) : 
      Math.max(24, Math.min(imageWidth, imageHeight) * 0.05); // Larger for details view
    const fontSize = Math.max(10, baseSize * 0.6);
    
    console.log('🎯 MapsGrid Player position overlay size calculated:', { baseSize, fontSize, isFullscreen, imageWidth, imageHeight });
    
    // Style the overlay with vibrant colors
    overlay.style.cssText = `
      position: absolute;
      left: ${leftPercent}%;
      top: ${topPercent}%;
      width: ${baseSize}px;
      height: ${baseSize}px;
      background-color: ${playerColor} !important;
      border: 3px solid #000000 !important;
      box-shadow: 0 0 15px ${playerColor} !important;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      cursor: pointer;
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: ${fontSize}px;
      color: #FFFFFF !important;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9) !important;
      transition: all 0.2s ease;
    `;
    
    overlay.textContent = (index + 1).toString();
    
    // Add hover effects with War2-specific tooltip data
    overlay.addEventListener('mouseenter', (e) => {
      const tooltipData = {
        name: `Player ${index + 1} Start`,
        race: this.formatRace(position.race || 'unknown'),
        location: `(${position.x}, ${position.y})`,
        player: index + 1
      };
      this.showWar2Tooltip(e, tooltipData, 'player');
      overlay.style.transform = 'translate(-50%, -50%) scale(1.4)';
      overlay.style.boxShadow = `0 0 25px ${playerColor}`;
    });

    overlay.addEventListener('mouseleave', () => {
      this.hideWar2Tooltip();
      overlay.style.transform = 'translate(-50%, -50%) scale(1)';
      overlay.style.boxShadow = `0 0 15px ${playerColor}`;
    });

    container.appendChild(overlay);
  }

  /**
   * Get goldmine color based on amount (same as MapDetails)
   */
  getGoldmineColor(amount) {
    if (amount >= 300000) return '#FF0080'; // Bright magenta for very high
    if (amount >= 200000) return '#FFD700'; // Gold for high  
    if (amount >= 100000) return '#FF4500'; // Orange-red for medium-high
    if (amount >= 50000) return '#00FFFF';  // Cyan for medium
    if (amount >= 25000) return '#FF69B4';  // Hot pink for low
    return '#FF0080'; // Default to bright magenta
  }

  /**
   * Get player color based on race (same as MapDetails)
   */
  getPlayerColor(race) {
    const raceColors = {
      'human': '#0080FF',    // Bright blue
      'orc': '#FF0040',      // Bright red  
      'unknown': '#00FF80',  // Bright green
      'neutral': '#00FF80'   // Bright green
    };
    
    return raceColors[race] || '#00FF80'; // Default to bright green
  }

  /**
   * Show enhanced tooltip (same as MapDetails)
   */
  showEnhancedTooltip(event, data, type) {
    this.hideEnhancedTooltip(); // Remove any existing tooltip

    const tooltip = document.createElement('div');
    tooltip.id = 'enhanced-tooltip';
    tooltip.className = 'enhanced-tooltip';

    let content = '';
    if (type === 'goldmine') {
      const goldAmount = data.amount || data.gold || data.goldAmount || 0;
      const category = this.getGoldmineCategory(goldAmount);
      const formattedAmount = goldAmount.toLocaleString();
      
      content = `
        <div class="tooltip-header goldmine-header">
          <i class="fas fa-coins"></i> Goldmine #${data.index || '?'}
        </div>
        <div class="tooltip-content">
          <div class="tooltip-stat">
            <span class="stat-label">Gold Amount:</span>
            <span class="stat-value">${formattedAmount}</span>
          </div>
          <div class="tooltip-stat">
            <span class="stat-label">Category:</span>
            <span class="stat-value ${category.toLowerCase()}">${category}</span>
          </div>
          <div class="tooltip-stat">
            <span class="stat-label">Position:</span>
            <span class="stat-value">(${data.x}, ${data.y})</span>
          </div>
        </div>
      `;
    } else if (type === 'player') {
      const race = this.formatRace(data.race || 'Unknown');
      
      content = `
        <div class="tooltip-header player-header">
          <i class="fas fa-flag"></i> Player ${data.player || '?'}
        </div>
        <div class="tooltip-content">
          <div class="tooltip-stat">
            <span class="stat-label">Race:</span>
            <span class="stat-value">${race}</span>
          </div>
          <div class="tooltip-stat">
            <span class="stat-label">Position:</span>
            <span class="stat-value">(${data.x}, ${data.y})</span>
          </div>
        </div>
      `;
    }

    tooltip.innerHTML = content;
    document.body.appendChild(tooltip);

    // Position tooltip near cursor but keep it on screen
    const rect = tooltip.getBoundingClientRect();
    const x = Math.min(event.clientX + 15, window.innerWidth - rect.width - 10);
    const y = Math.max(event.clientY - rect.height - 10, 10);
    
    tooltip.style.left = `${x}px`;
    tooltip.style.top = `${y}px`;

    // Add show class for animation
    setTimeout(() => tooltip.classList.add('show'), 10);
  }

  /**
   * Hide enhanced tooltip (same as MapDetails)
   */
  hideEnhancedTooltip() {
    const tooltip = document.getElementById('enhanced-tooltip');
    if (tooltip) {
      tooltip.remove();
    }
  }

  /**
   * Get goldmine category (same as MapDetails)
   */
  getGoldmineCategory(amount) {
    if (amount >= 300000) return 'Very High';
    if (amount >= 200000) return 'High';
    if (amount >= 100000) return 'Medium-High';
    if (amount >= 50000) return 'Medium';
    if (amount >= 25000) return 'Low';
    return 'Very Low';
  }

  /**
   * Format race name (same as MapDetails)
   */
  formatRace(race) {
    const raceNames = {
      'human': 'Human',
      'orc': 'Orc',
      'unknown': 'Unknown',
      'neutral': 'Neutral'
    };
    return raceNames[race?.toLowerCase()] || 'Unknown';
  }

  /**
   * Render pagination controls
   */
  renderPagination(totalPages, currentPage, totalMaps = 0) {
    // Get the correct pagination container based on current game
    const currentGame = this.getCurrentGame();
    let paginationId = 'pagination'; // default for war2
    
    if (currentGame === 'war3') {
      paginationId = 'war3-pagination';
    }
    
    const paginationContainer = document.getElementById(paginationId);
    if (!paginationContainer) {
      console.warn(`Pagination container not found: ${paginationId}`);
      return;
    }

    // Clear existing pagination
    paginationContainer.innerHTML = '';

    if (totalPages <= 1) {
      return; // No pagination needed
    }

    console.log(`📄 Rendering pagination: ${currentPage}/${totalPages} (${totalMaps} total maps) in ${paginationId}`);

    const paginationHTML = this.generatePaginationHTML(totalPages, currentPage, totalMaps);
    paginationContainer.innerHTML = paginationHTML;
  }

  /**
   * Generate pagination HTML
   */
  generatePaginationHTML(totalPages, currentPage, totalMaps) {
    let paginationHTML = `
      <div class="pagination-controls-modern">
        <div class="pagination-info">
          <span class="pagination-text">
            Page ${currentPage} of ${totalPages}
            ${totalMaps > 0 ? ` • ${totalMaps} maps total` : ''}
          </span>
        </div>
        <div class="pagination-buttons">
    `;
    
    // Previous button
    if (currentPage > 1) {
      paginationHTML += `
        <button class="pagination-btn prev-btn" 
                onclick="mapsGrid.goToPage(${currentPage - 1})"
                title="Previous Page">
          <i class="fas fa-chevron-left"></i>
          <span>Previous</span>
        </button>
      `;
    }

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
      paginationHTML += `
        <button class="pagination-btn page-btn" 
                onclick="mapsGrid.goToPage(1)">1</button>
      `;
      if (startPage > 2) {
        paginationHTML += '<span class="pagination-ellipsis">...</span>';
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
        <button class="pagination-btn page-btn ${i === currentPage ? 'active' : ''}" 
                onclick="mapsGrid.goToPage(${i})"
                ${i === currentPage ? 'disabled' : ''}>${i}</button>
      `;
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        paginationHTML += '<span class="pagination-ellipsis">...</span>';
      }
      paginationHTML += `
        <button class="pagination-btn page-btn" 
                onclick="mapsGrid.goToPage(${totalPages})">${totalPages}</button>
      `;
    }

    // Next button
    if (currentPage < totalPages) {
      paginationHTML += `
        <button class="pagination-btn next-btn" 
                onclick="mapsGrid.goToPage(${currentPage + 1})"
                title="Next Page">
          <span>Next</span>
          <i class="fas fa-chevron-right"></i>
        </button>
      `;
    }

    paginationHTML += `
        </div>
      </div>
    `;
    
    return paginationHTML;
  }

  /**
   * Update hero stats display
   */
  async updateHeroStats() {
    try {
      const response = await fetch('/api/war2maps/stats', {
        credentials: 'include'
      });

      if (!response.ok) {
        console.warn('Failed to load maps stats');
        return;
      }

      const stats = await response.json();
      
      // Update stats in the UI
      this.updateStatsDisplay(stats);
      
    } catch (error) {
      console.error('❌ Failed to update hero stats:', error);
    }
  }

  /**
   * Update stats display in UI
   */
  updateStatsDisplay(stats) {
    const elements = {
      totalMaps: document.getElementById('current-game-maps'), // Maps Available counter
      currentGameMaps: document.getElementById('current-game-maps'),
      recentUploads: document.getElementById('recent-uploads'),
      topDownloaded: document.getElementById('top-downloaded')
    };

    if (elements.totalMaps) {
      elements.totalMaps.textContent = stats.totalMaps || 0;
      console.log(`📊 Updated Maps Available: ${stats.totalMaps || 0}`);
    } else {
      console.warn('❌ Maps Available counter element not found');
    }

    if (elements.currentGameMaps) {
      elements.currentGameMaps.textContent = stats.currentGameMaps || stats.totalMaps || 0;
    }

    if (elements.recentUploads) {
      elements.recentUploads.textContent = stats.recentUploads || 0;
    }

    if (elements.topDownloaded) {
      elements.topDownloaded.textContent = stats.topDownloaded || 0;
    }

    console.log('📊 Stats display updated:', stats);
  }

  /**
   * Update game stats for specific game
   */
  async updateGameStats(game) {
    console.log(`🎮 Updating stats for game: ${game}`);
    
    const currentGameMapsEl = document.getElementById('current-game-maps');
    const recentUploadsEl = document.getElementById('recent-uploads');

    // Handle all games consistently via API calls
    try {
      // Try to fetch stats from respective API
      const apiUrl = `/api/${game}maps/stats`;
      console.log(`🔍 DEBUG: Fetching stats from: ${apiUrl} for game: ${game}`);
      const response = await fetch(apiUrl, { credentials: 'include' });
      if (response.ok) {
        const stats = await response.json();
        console.log(`📊 ${game.toUpperCase()} stats from API:`, stats);
        if (currentGameMapsEl) {
          // Use currentGameMaps first, then totalMaps as fallback
          currentGameMapsEl.textContent = stats.currentGameMaps || stats.totalMaps || 0;
        }
        if (recentUploadsEl) {
          recentUploadsEl.textContent = stats.recentUploads || 0;
        }
      } else {
        console.log(`📊 API failed for ${game.toUpperCase()}, showing 0`);
        // Show 0 for games with failed APIs
        if (currentGameMapsEl) {
          currentGameMapsEl.textContent = '0';
        }
        if (recentUploadsEl) {
          recentUploadsEl.textContent = '0';
        }
      }
    } catch (error) {
      console.error(`❌ Error fetching ${game.toUpperCase()} stats:`, error);
      // Fallback to 0
      if (currentGameMapsEl) {
        currentGameMapsEl.textContent = '0';
      }
      if (recentUploadsEl) {
        recentUploadsEl.textContent = '0';
      }
    }
    
    console.log(`✅ Stats updated for ${game.toUpperCase()}`);
  }

  /**
   * View map details (delegated to MapDetails module)
   */
  viewMapDetails(mapId) {
    if (window.mapDetails && typeof window.mapDetails.viewMapDetails === 'function') {
      window.mapDetails.viewMapDetails(mapId);
    } else {
      console.warn('Map details module not available');
    }
  }

  /**
   * Download map functionality
   */
  async downloadMap(mapId) {
    try {
      console.log(`📥 Downloading map: ${mapId}`);
      
      // Get current game context to use correct API endpoint
      const currentGame = this.getCurrentGame();
      const apiEndpoint = currentGame === 'war3' ? 'war3maps' : 'war2maps';
      
      const response = await fetch(`/api/${apiEndpoint}/${mapId}/download`, {
        method: 'GET',
        credentials: 'include'
      });

      if (!response.ok) {
        let errorMessage = 'Failed to download map';
        try {
          const data = await response.json();
          errorMessage = data.message || data.error || errorMessage;
          console.error('❌ Download error details:', data);
        } catch (parseError) {
          console.error('❌ Could not parse error response:', parseError);
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      // Get filename from response headers
      const contentDisposition = response.headers.get('content-disposition');
      let filename = currentGame === 'war3' ? 'map.w3x' : 'map.pud';
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      console.log(`✅ Map downloaded: ${filename}`);
      
    } catch (error) {
      console.error('❌ Failed to download map:', error);
      this.showError(`Failed to download map: ${error.message}`);
    }
  }

  /**
   * Delete map functionality
   */
  async deleteMap(mapId) {
    if (!confirm('Are you sure you want to delete this map? This action cannot be undone.')) {
      return;
    }

    try {
      console.log(`🗑️ Deleting map: ${mapId}`);
      
      // Get current game context to use correct API endpoint
      const currentGame = this.getCurrentGame();
      const apiEndpoint = currentGame === 'war3' ? 'war3maps' : 'war2maps';
      
      // Debug: Log current user and map info
      const map = this.getCachedMap(mapId);
      const currentUser = window.mapsCore?.currentUser;
      console.log('🔍 Debug info:', {
        mapId,
        currentGame,
        apiEndpoint,
        currentUser: currentUser ? {
          id: currentUser.id,
          _id: currentUser._id,
          username: currentUser.username,
          role: currentUser.role
        } : null,
        map: map ? {
          uploadedBy: map.uploadedBy,
          name: map.name
        } : 'Map not in cache'
      });
      
      const response = await fetch(`/api/${apiEndpoint}/${mapId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!response.ok) {
        let errorMessage = 'Failed to delete map';
        try {
          const data = await response.json();
          errorMessage = data.error || errorMessage;
          console.error('❌ Delete error details:', {
            status: response.status,
            statusText: response.statusText,
            errorData: data
          });
        } catch (parseError) {
          console.error('❌ Could not parse error response:', parseError);
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      // Remove from cache
      this.mapsCache.delete(mapId);
      
      // Reload maps
      await this.loadMaps();
      
      this.showSuccess('Map deleted successfully');
      console.log(`✅ Map deleted: ${mapId}`);
      
    } catch (error) {
      console.error('❌ Failed to delete map:', error);
      this.showError(`Failed to delete map: ${error.message}`);
    }
  }

  /**
   * Utility methods
   */
  formatPlayerCount(playerCount) {
    if (!playerCount) return 'Unknown';
    if (playerCount.min === playerCount.max) {
      return playerCount.min.toString();
    }
    return `${playerCount.min}-${playerCount.max}`;
  }

  calculateAverageRating(ratings) {
    if (!ratings || ratings.length === 0) return 0;
    const sum = ratings.reduce((total, rating) => total + rating.rating, 0);
    return sum / ratings.length;
  }

  generateStarRating(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    let html = '';
    
    // Full stars
    for (let i = 0; i < fullStars; i++) {
      html += '<i class="fas fa-star"></i>';
    }
    
    // Half star
    if (hasHalfStar) {
      html += '<i class="fas fa-star-half-alt"></i>';
    }
    
    // Empty stars
    for (let i = 0; i < emptyStars; i++) {
      html += '<i class="far fa-star"></i>';
    }

    return html;
  }

  canDeleteMap(map) {
    const user = window.mapsCore?.currentUser;
    if (!user) return false;
    
    // Admin can delete any map, users can delete their own maps
    // Handle both populated and unpopulated uploadedBy field
    let uploadedByStr = '';
    if (map.uploadedBy) {
      if (typeof map.uploadedBy === 'object' && map.uploadedBy._id) {
        // Populated uploadedBy object
        uploadedByStr = map.uploadedBy._id.toString();
      } else {
        // Direct ObjectId or string
        uploadedByStr = map.uploadedBy.toString();
      }
    }
    
    const userIdStr = user.id ? user.id.toString() : '';
    
    return user.role === 'admin' || uploadedByStr === userIdStr;
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  truncateText(text, maxLength) {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }

  capitalizeFirst(str) {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  formatDate(dateString) {
    if (!dateString) return 'Unknown';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }

  /**
   * Get no maps HTML with context-aware messaging
   */
  getNoMapsHTML() {
    const currentGame = this.getCurrentGame();
    const searchTerm = document.getElementById('search-input')?.value.trim();
    const currentTab = window.mapsCore?.state?.currentTab || 'all';
    
    let gameDisplayName = 'Warcraft II';
    if (currentGame === 'war1') gameDisplayName = 'Warcraft I';
    else if (currentGame === 'war3') gameDisplayName = 'Warcraft III';
    
    let message = 'No maps found';
    let description = 'No maps match your current criteria.';
    
    if (currentGame !== 'war2') {
      message = `No ${gameDisplayName} Maps Available`;
      description = `There are currently no ${gameDisplayName} maps in our database. Most of our maps are for Warcraft II. Try switching to WC II for a full selection of maps.`;
    } else if (searchTerm) {
      message = 'No Search Results';
      description = `No maps found matching "${searchTerm}". Try adjusting your search terms or browse all maps.`;
    } else if (currentTab === 'land') {
      message = 'No Land Maps Found';
      description = 'No land maps match your current criteria. Try browsing all maps or check the sea maps filter.';
    } else if (currentTab === 'sea') {
      message = 'No Sea Maps Found';
      description = 'No sea/naval maps match your current criteria. Try browsing all maps or check the land maps filter.';
    }
    
    const suggestions = this.getNoMapsSuggestions(currentGame, searchTerm, currentTab);

    return `
      <div class="no-maps">
        <i class="fas fa-map"></i>
        <h3>${message}</h3>
        <p>${description}</p>
        ${suggestions ? `
          <div class="no-maps-suggestions">
            <h4>Suggestions:</h4>
            ${suggestions}
          </div>
        ` : ''}
      </div>
    `;
  }

  /**
   * Get suggestions for when no maps are found
   */
  getNoMapsSuggestions(currentGame, searchTerm, currentTab) {
    const suggestions = [];
    
    if (currentGame !== 'war2') {
      suggestions.push('<button class="btn btn-primary" onclick="window.gameTabsManager?.switchGame(\'war2\')">Browse Warcraft II Maps</button>');
    }
    
    if (searchTerm) {
      suggestions.push('<button class="btn btn-secondary" onclick="document.getElementById(\'search-input\').value = \'\'; mapsCore.handleSearch()">Clear Search</button>');
    }
    
    if (currentTab !== 'all') {
      suggestions.push('<button class="btn btn-secondary" onclick="mapsCore.switchTab(\'all\')">Show All Maps</button>');
    }
    
    return suggestions.length > 0 ? `
      <div class="suggestions-buttons">
        ${suggestions.join('')}
      </div>
    ` : null;
  }

  /**
   * Get current game from game tabs or default to war2
   */
  getCurrentGame() {
    if (window.gameTabsManager && typeof window.gameTabsManager.getCurrentGame === 'function') {
      return window.gameTabsManager.getCurrentGame();
    }
    return localStorage.getItem('selectedGame') || 'war2';
  }

  /**
   * Show/hide loading states
   */
  showLoading() {
    // Get the correct grid container based on current game
    const currentGame = this.getCurrentGame();
    
    // Skip loading for WC1 - handled by WC1Manager
    if (currentGame === 'war1') {
      console.log('🗡️ Skipping MapsGrid loading for WC1 - handled by WC1Manager');
      return;
    }
    
    let gridId = 'maps-grid'; // default for war2
    
    if (currentGame === 'war3') {
      gridId = 'war3-maps-grid';
    }
    
    const mapsGrid = document.getElementById(gridId);
    if (mapsGrid) {
      const loadingText = currentGame === 'war3' ? 'Loading War3Net maps...' : 
                         'Loading epic battlegrounds...';
      
      mapsGrid.innerHTML = `
        <div class="maps-loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>${loadingText}</p>
        </div>
      `;
    }
  }

  hideLoading() {
    // Loading state will be replaced by rendered maps
  }

  /**
   * Show error message
   */
  showError(message) {
    if (typeof window.NotificationUtils !== 'undefined') {
      window.NotificationUtils.error(message, 5000);
    } else {
      alert(`Error: ${message}`);
    }
  }

  /**
   * Show success message
   */
  showSuccess(message) {
    if (typeof window.NotificationUtils !== 'undefined') {
      window.NotificationUtils.success(message, 3000);
    } else {
      console.log(`Success: ${message}`);
    }
  }

  /**
   * Get cached map by ID
   */
  getCachedMap(mapId) {
    return this.mapsCache.get(mapId);
  }

  /**
   * Cleanup function to disconnect observers and clear caches
   */
  cleanup() {
    console.log('🧹 Cleaning up Maps Grid...');
    
    this.mapsCache.clear();
    this.currentMaps = [];
    this.isLoading = false;
    this.loadingStates.clear();

    // Disconnect intersection observer
    if (this.imageObserver) {
      this.imageObserver.disconnect();
      this.imageObserver = null;
    }

    // Clear maps cache periodically to prevent memory leaks
    if (this.mapsCache.size > 100) {
      this.mapsCache.clear();
      console.log('🧹 Cleared maps cache to prevent memory issues');
    }

    // Remove event listeners
    if (this.globalEventHandlers) {
      this.globalEventHandlers.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
      this.globalEventHandlers.clear();
    }

    console.log('✅ Maps Grid cleanup complete');
  }

  /**
   * Go to specific page
   */
  goToPage(page) {
    if (window.mapsCore) {
      window.mapsCore.updateState({ currentPage: page });
      
      // Use appropriate reload method based on current game
      const currentGame = this.getCurrentGame();
      if (currentGame === 'war3') {
        window.mapsCore.triggerWC3MapsReload();
      } else {
        window.mapsCore.triggerMapsReload();
      }
    } else {
      console.warn('MapsCore not available for pagination');
    }
  }

  /**
   * Get player count display for War2 maps
   */
  getPlayerCountDisplay(map) {
    // Check multiple sources for player count
    
    // 1. Strategic data player count (most accurate - should be a number)
    if (map.strategicData?.playerCount && typeof map.strategicData.playerCount === 'number') {
      return `${map.strategicData.playerCount}`;
    }
    
    // 2. Direct player count property (can be object with min/max)
    if (map.playerCount) {
      if (typeof map.playerCount === 'number') {
        return `${map.playerCount}`;
      } else if (typeof map.playerCount === 'object' && map.playerCount.min && map.playerCount.max) {
        if (map.playerCount.min === map.playerCount.max) {
          return `${map.playerCount.min}`;
        } else {
          return `${map.playerCount.min}-${map.playerCount.max}`;
        }
      }
    }
    
    // 3. Players property (can be number or object)
    if (map.players) {
      if (typeof map.players === 'number') {
        return `${map.players}`;
      } else if (typeof map.players === 'object' && map.players.min && map.players.max) {
        if (map.players.min === map.players.max) {
          return `${map.players.min}`;
        } else {
          return `${map.players.min}-${map.players.max}`;
        }
      }
    }
    
    // 4. Strategic analysis data
    if (map.strategicAnalysis?.playerCount && typeof map.strategicAnalysis.playerCount === 'number') {
      return `${map.strategicAnalysis.playerCount}`;
    }
    
    // 5. Count starting positions from strategic data
    if (map.strategicData?.startingPositions?.length) {
      return `${map.strategicData.startingPositions.length}`;
    }
    
    // 6. Default fallback
    return '2';
  }

  /**
   * Get thumbnail path for War2 maps
   */
  getThumbnailPath(map) {
    if (map.thumbnailPath) {
      return map.thumbnailPath;
    }
    
    // Generate thumbnail name from map name
    const thumbnailName = `${map.name.replace(/[^a-zA-Z0-9_-]/g, '_').toUpperCase()}_strategic.png`;
    return `/uploads/thumbnails/${thumbnailName}`;
  }

  /**
   * Get strategic data display for War2 maps
   */
  getStrategicData(map) {
    if (!map.strategicAnalysis) {
      return '';
    }

    const goldmines = map.strategicAnalysis.goldmines?.length || 0;
    const waterPercentage = map.strategicAnalysis.waterPercentage || 0;
    const treePercentage = map.strategicAnalysis.treePercentage || 0;

    return `
      <div class="strategic-items">
        <div class="strategic-item" title="Goldmines">
          <i class="strategic-icon fas fa-coins"></i>
          <span>${goldmines}</span>
        </div>
        <div class="strategic-item" title="Water Coverage">
          <i class="strategic-icon fas fa-water"></i>
          <span>${waterPercentage.toFixed(0)}%</span>
        </div>
        <div class="strategic-item" title="Forest Coverage">
          <i class="strategic-icon fas fa-tree"></i>
          <span>${treePercentage.toFixed(0)}%</span>
        </div>
      </div>
    `;
  }
}

// Create and export singleton instance
export const mapsGrid = new MapsGrid(); 