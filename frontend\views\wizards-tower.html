<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Wizard's Tower - Warcraft Arena</title>
  <link rel="stylesheet" href="/css/warcraft-app-modern.css">
  <link rel="stylesheet" href="/css/black-theme.css">
  <link rel="stylesheet" href="/css/wizards-tower-enhanced.css">
  <link rel="stylesheet" href="/css/navbar-universal.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <div class="app-container" style="padding-top: calc(80px + var(--space-6));">
    <!-- Navigation -->
    <div id="navbar-container"></div>

    <!-- Main Content -->
    <div class="wizards-tower-container">
      <div class="page-header">
        <h1 class="page-title">
          <i class="fas fa-tower-observation"></i>
          Wizard's Tower
        </h1>
      </div>

      <!-- Game Selection Tabs -->
      <div class="game-tabs">
        <button class="game-tab active" data-game="wc1">
          <i class="fas fa-sword"></i>
          Warcraft I
        </button>
        <button class="game-tab" data-game="wc2">
          <i class="fas fa-shield"></i>
          Warcraft II
        </button>
        <button class="game-tab" data-game="wc3">
          <i class="fas fa-crown"></i>
          Warcraft III
        </button>

      </div>

      <!-- Sub-tabs for each game -->
      <div class="sub-tabs-container">
        <!-- WC1 Sub-tabs -->
        <div class="sub-tabs active" data-game="wc1">
          <button class="sub-tab active" data-subtab="human">
            <i class="fas fa-users"></i>
            Humans
          </button>
          <button class="sub-tab" data-subtab="orc">
            <i class="fas fa-skull"></i>
            Orcs
          </button>
          <button class="sub-tab" data-subtab="general">
            <i class="fas fa-book"></i>
            General
          </button>
          <button class="sub-tab" data-subtab="strategy">
            <i class="fas fa-chess"></i>
            Strategy
          </button>
        </div>

        <!-- WC2 Sub-tabs -->
        <div class="sub-tabs" data-game="wc2">
          <button class="sub-tab active" data-subtab="human">
            <i class="fas fa-users"></i>
            Humans
          </button>
          <button class="sub-tab" data-subtab="orc">
            <i class="fas fa-skull"></i>
            Orcs
          </button>
          <button class="sub-tab" data-subtab="general">
            <i class="fas fa-book"></i>
            General
          </button>
          <button class="sub-tab" data-subtab="strategy">
            <i class="fas fa-chess"></i>
            Strategy
          </button>
        </div>

        <!-- WC3 Sub-tabs -->
        <div class="sub-tabs" data-game="wc3">
          <button class="sub-tab active" data-subtab="human">
            <i class="fas fa-users"></i>
            Humans
          </button>
          <button class="sub-tab" data-subtab="orc">
            <i class="fas fa-skull"></i>
            Orcs
          </button>
          <button class="sub-tab" data-subtab="undead">
            <i class="fas fa-ghost"></i>
            Undead
          </button>
          <button class="sub-tab" data-subtab="nightelf">
            <i class="fas fa-tree"></i>
            Night Elves
          </button>
          <button class="sub-tab" data-subtab="neutral">
            <i class="fas fa-star"></i>
            Neutral
          </button>
          <button class="sub-tab" data-subtab="items">
            <i class="fas fa-gem"></i>
            Items
          </button>
          <button class="sub-tab" data-subtab="general">
            <i class="fas fa-book"></i>
            General
          </button>
          <button class="sub-tab" data-subtab="strategy">
            <i class="fas fa-chess"></i>
            Strategy
          </button>
        </div>
      </div>

      <!-- Content Areas -->
      <div class="wiki-content active">
        <!-- Loading indicator -->
        <div id="loading-indicator" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Loading unit data...</p>
        </div>

        <!-- WC1 Content -->
        <div class="game-content active" data-game="wc1">
          <!-- Humans Sub-content -->
          <div class="sub-content active" data-subtab="human">
            <div class="wiki-sections">
              <!-- Human Units Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-users"></i>
                  Human Units
                </h3>
                <div id="wc1-human-units" class="wiki-grid">
                  <!-- Units will be loaded here -->
                </div>
              </div>

              <!-- Human Buildings Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-building"></i>
                  Human Buildings
                </h3>
                <div id="wc1-human-buildings" class="wiki-grid">
                  <!-- Buildings will be loaded here -->
                </div>
              </div>
            </div>
          </div>

          <!-- Orcs Sub-content -->
          <div class="sub-content" data-subtab="orc">
            <div class="wiki-sections">
              <!-- Orc Units Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-skull"></i>
                  Orc Units
                </h3>
                <div id="wc1-orc-units" class="wiki-grid">
                  <!-- Units will be loaded here -->
                </div>
              </div>

              <!-- Orc Buildings Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-building"></i>
                  Orc Buildings
                </h3>
                <div id="wc1-orc-buildings" class="wiki-grid">
                  <!-- Buildings will be loaded here -->
                </div>
              </div>
            </div>
          </div>

          <!-- General Sub-content -->
          <div class="sub-content" data-subtab="general">
            <div class="wiki-sections">
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-book"></i>
                  General Information
                </h3>
                <div class="wiki-content-text">
                  <h4>About Warcraft I</h4>
                  <p>Warcraft: Orcs & Humans is the first game in the Warcraft series, released in 1994. It introduced the world to Azeroth and the conflict between humans and orcs.</p>
                  
                  <h4>Game Mechanics</h4>
                  <ul>
                    <li><strong>Resources:</strong> Gold is the primary resource</li>
                    <li><strong>Population:</strong> Limited by food supply</li>
                    <li><strong>Combat:</strong> Real-time strategy with unit management</li>
                    <li><strong>Victory:</strong> Destroy all enemy buildings</li>
                  </ul>

                  <h4>Key Features</h4>
                  <ul>
                    <li>Two playable races: Humans and Orcs</li>
                    <li>Unique units and buildings for each race</li>
                    <li>Upgrade system for units and buildings</li>
                    <li>Spellcasting units with special abilities</li>
                    <li>Siege weapons for building destruction</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Strategy Sub-content -->
          <div class="sub-content" data-subtab="strategy">
            <div class="wiki-sections">
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-chess"></i>
                  Strategy Guide
                </h3>
                <div class="wiki-content-text">
                  <h4>Early Game</h4>
                  <p>Focus on resource gathering and building your economy. Train workers and expand your base.</p>
                  
                  <h4>Mid Game</h4>
                  <p>Build military units and research upgrades. Scout the map and prepare for combat.</p>
                  
                  <h4>Late Game</h4>
                  <p>Mass your forces and launch coordinated attacks. Use siege weapons to destroy enemy buildings.</p>
                  
                  <h4>Tips & Tricks</h4>
                  <ul>
                    <li>Always keep your workers busy</li>
                    <li>Use spellcasters effectively</li>
                    <li>Upgrade your units when possible</li>
                    <li>Scout enemy positions</li>
                    <li>Protect your spellcasters in battle</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- WC2 Content -->
        <div class="game-content" data-game="wc2">
          <!-- Humans Sub-content -->
          <div class="sub-content" data-subtab="human">
            <div class="wiki-sections">
              <!-- Human Units Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-users"></i>
                  Human Units
                </h3>
                <div id="wc2-human-units" class="wiki-grid">
                  <!-- Units will be loaded here -->
                </div>
              </div>

              <!-- Human Buildings Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-building"></i>
                  Human Buildings
                </h3>
                <div id="wc2-human-buildings" class="wiki-grid">
                  <!-- Buildings will be loaded here -->
                </div>
              </div>
            </div>
          </div>

          <!-- Orcs Sub-content -->
          <div class="sub-content" data-subtab="orc">
            <div class="wiki-sections">
              <!-- Orc Units Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-skull"></i>
                  Orc Units
                </h3>
                <div id="wc2-orc-units" class="wiki-grid">
                  <!-- Units will be loaded here -->
                </div>
              </div>

              <!-- Orc Buildings Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-building"></i>
                  Orc Buildings
                </h3>
                <div id="wc2-orc-buildings" class="wiki-grid">
                  <!-- Buildings will be loaded here -->
                </div>
              </div>
            </div>
          </div>

          <!-- General Sub-content -->
          <div class="sub-content" data-subtab="general">
            <div class="wiki-sections">
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-book"></i>
                  General Information
                </h3>
                <div class="wiki-content-text">
                  <h4>About Warcraft II</h4>
                  <p>Warcraft II: Tides of Darkness is the second game in the Warcraft series, released in 1995. It expanded on the original with improved graphics, new units, and naval combat.</p>
                  
                  <h4>Game Mechanics</h4>
                  <ul>
                    <li><strong>Resources:</strong> Gold and Wood are the primary resources</li>
                    <li><strong>Population:</strong> Limited by food supply</li>
                    <li><strong>Combat:</strong> Land and naval battles</li>
                    <li><strong>Victory:</strong> Destroy all enemy buildings</li>
                  </ul>

                  <h4>Key Features</h4>
                  <ul>
                    <li>Two playable races: Humans and Orcs</li>
                    <li>Naval combat with ships and submarines</li>
                    <li>Flying units for aerial combat</li>
                    <li>Advanced spellcasting system</li>
                    <li>Improved graphics and sound</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Strategy Sub-content -->
          <div class="sub-content" data-subtab="strategy">
            <div class="wiki-sections">
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-chess"></i>
                  Strategy Guide
                </h3>
                <div class="wiki-content-text">
                  <h4>Early Game</h4>
                  <p>Focus on resource gathering with both gold and wood. Build farms for food and expand your base.</p>
                  
                  <h4>Mid Game</h4>
                  <p>Build military units and research upgrades. Use naval units to control water resources.</p>
                  
                  <h4>Late Game</h4>
                  <p>Mass your forces and launch coordinated attacks. Use flying units for scouting and harassment.</p>
                  
                  <h4>Tips & Tricks</h4>
                  <ul>
                    <li>Control water resources for naval advantage</li>
                    <li>Use flying units for scouting</li>
                    <li>Upgrade your units when possible</li>
                    <li>Build defensive towers</li>
                    <li>Use spellcasters effectively</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- WC3 Content -->
        <div class="game-content" data-game="wc3">
          <!-- Humans Sub-content -->
          <div class="sub-content" data-subtab="human">
            <div class="wiki-sections">
              <!-- Human Units Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-users"></i>
                  Human Units
                </h3>
                <div id="wc3-human-units" class="wiki-grid">
                  <!-- Units will be loaded here -->
                </div>
              </div>

              <!-- Human Buildings Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-building"></i>
                  Human Buildings
                </h3>
                <div id="wc3-human-buildings" class="wiki-grid">
                  <!-- Buildings will be loaded here -->
                </div>
              </div>
            </div>
          </div>

          <!-- Orcs Sub-content -->
          <div class="sub-content" data-subtab="orc">
            <div class="wiki-sections">
              <!-- Orc Units Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-skull"></i>
                  Orc Units
                </h3>
                <div id="wc3-orc-units" class="wiki-grid">
                  <!-- Units will be loaded here -->
                </div>
              </div>

              <!-- Orc Buildings Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-building"></i>
                  Orc Buildings
                </h3>
                <div id="wc3-orc-buildings" class="wiki-grid">
                  <!-- Buildings will be loaded here -->
                </div>
              </div>
            </div>
          </div>

          <!-- Undead Sub-content -->
          <div class="sub-content" data-subtab="undead">
            <div class="wiki-sections">
              <!-- Undead Units Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-ghost"></i>
                  Undead Units
                </h3>
                <div id="wc3-undead-units" class="wiki-grid">
                  <!-- Units will be loaded here -->
                </div>
              </div>

              <!-- Undead Buildings Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-building"></i>
                  Undead Buildings
                </h3>
                <div id="wc3-undead-buildings" class="wiki-grid">
                  <!-- Buildings will be loaded here -->
                </div>
              </div>
            </div>
          </div>

          <!-- Night Elves Sub-content -->
          <div class="sub-content" data-subtab="nightelf">
            <div class="wiki-sections">
              <!-- Night Elf Units Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-tree"></i>
                  Night Elf Units
                </h3>
                <div id="wc3-nightelf-units" class="wiki-grid">
                  <!-- Units will be loaded here -->
                </div>
              </div>

              <!-- Night Elf Buildings Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-building"></i>
                  Night Elf Buildings
                </h3>
                <div id="wc3-nightelf-buildings" class="wiki-grid">
                  <!-- Buildings will be loaded here -->
                </div>
              </div>
            </div>
          </div>

          <!-- Neutral Sub-content -->
          <div class="sub-content" data-subtab="neutral">
            <div class="wiki-sections">
              <!-- Neutral Creeps Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-paw"></i>
                  Neutral Creeps
                </h3>
                <div id="wc3-neutral-creeps" class="wiki-grid">
                  <!-- Creeps will be loaded here -->
                </div>
              </div>

              <!-- Neutral Mercenaries Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-sword"></i>
                  Neutral Mercenaries
                </h3>
                <div id="wc3-neutral-mercenaries" class="wiki-grid">
                  <!-- Mercenaries will be loaded here -->
                </div>
              </div>

              <!-- Neutral Heroes Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-crown"></i>
                  Neutral Heroes
                </h3>
                <div id="wc3-neutral-heroes" class="wiki-grid">
                  <!-- Heroes will be loaded here -->
                </div>
              </div>
            </div>
          </div>

          <!-- Items Sub-content -->
          <div class="sub-content" data-subtab="items">
            <div class="wiki-sections">
              <!-- Consumable Items Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-flask"></i>
                  Consumable Items
                </h3>
                <div id="wc3-consumable-items" class="wiki-grid">
                  <!-- Consumable items will be loaded here -->
                </div>
              </div>

              <!-- Permanent Items Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-shield"></i>
                  Permanent Items
                </h3>
                <div id="wc3-permanent-items" class="wiki-grid">
                  <!-- Permanent items will be loaded here -->
                </div>
              </div>

              <!-- Artifact Items Section -->
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-gem"></i>
                  Artifacts
                </h3>
                <div id="wc3-artifact-items" class="wiki-grid">
                  <!-- Artifacts will be loaded here -->
                </div>
              </div>
            </div>
          </div>

          <!-- General Sub-content -->
          <div class="sub-content" data-subtab="general">
            <div class="wiki-sections">
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-book"></i>
                  General Information
                </h3>
                <div class="wiki-content-text">
                  <h4>About Warcraft III</h4>
                  <p>Warcraft III: Reign of Chaos introduced heroes, creeps, and RPG elements to the RTS genre.</p>
                  
                  <h4>Key Features</h4>
                  <ul>
                    <li>Hero units with experience and levels</li>
                    <li>Four distinct races</li>
                    <li>Creep camps and neutral units</li>
                    <li>Item system</li>
                    <li>Advanced spellcasting</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Strategy Sub-content -->
          <div class="sub-content" data-subtab="strategy">
            <div class="wiki-sections">
              <div class="wiki-section">
                <h3 class="section-title">
                  <i class="fas fa-chess"></i>
                  Strategy Guide
                </h3>
                <div class="wiki-content-text">
                  <h4>Hero Management</h4>
                  <p>Level up your heroes and choose the right abilities for your strategy.</p>
                  
                  <h4>Creep Management</h4>
                  <p>Clear creep camps for experience and items while expanding your base.</p>
                  
                  <h4>Advanced Tactics</h4>
                  <ul>
                    <li>Micro-manage your heroes</li>
                    <li>Use items effectively</li>
                    <li>Control creep camps</li>
                    <li>Balance economy and military</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script type="module" src="/js/modules/ApiClient.js"></script>
  <script type="module" src="/js/modules/AuthManager.js"></script>
  <script src="/js/main.js"></script>
  <script>
    // Game Units Manager
    class GameUnitsManager {
      constructor() {
        this.currentGame = 'wc1';
        this.currentRace = 'human';
        this.currentType = 'unit';
        this.loadedData = {};
        this.init();
      }

      init() {
        this.setupEventListeners();
        this.loadInitialData();
      }

      setupEventListeners() {
        // Game tab switching
        document.querySelectorAll('.game-tab').forEach(tab => {
          tab.addEventListener('click', (e) => {
            this.switchGame(e.target.dataset.game);
          });
        });

        // Sub-tab switching
        document.querySelectorAll('.sub-tab').forEach(tab => {
          tab.addEventListener('click', (e) => {
            this.switchSubTab(e.target.dataset.subtab);
          });
        });
      }

      switchGame(game) {
        console.log(`🔄 [DEBUG] switchGame called with game: ${game}`);

        // Update active game tab
        document.querySelectorAll('.game-tab').forEach(tab => {
          tab.classList.remove('active');
        });
        const gameTab = document.querySelector(`[data-game="${game}"]`);
        if (gameTab) {
          gameTab.classList.add('active');
        }

        // Update active sub-tabs
        document.querySelectorAll('.sub-tabs').forEach(subTabs => {
          subTabs.classList.remove('active');
        });
        const subTabs = document.querySelector(`.sub-tabs[data-game="${game}"]`);
        if (subTabs) {
          subTabs.classList.add('active');
        }

        // Update active game content
        document.querySelectorAll('.game-content').forEach(content => {
          content.classList.remove('active');
          content.style.display = 'none'; // Ensure hidden
        });
        const gameContent = document.querySelector(`.game-content[data-game="${game}"]`);
        if (gameContent) {
          gameContent.classList.add('active');
          gameContent.style.display = 'block'; // Ensure visible
          console.log(`✅ [DEBUG] Activated game content for: ${game}`);

          // Debug: Check game content visibility
          setTimeout(() => {
            const rect = gameContent.getBoundingClientRect();
            console.log(`🔍 [DEBUG] Game content visibility for ${game}:`, {
              width: rect.width,
              height: rect.height,
              top: rect.top,
              left: rect.left,
              display: getComputedStyle(gameContent).display,
              visibility: getComputedStyle(gameContent).visibility
            });
          }, 100);
        } else {
          console.warn(`⚠️ [DEBUG] No game content found for: ${game}`);
        }

        this.currentGame = game;
        console.log(`🔄 [DEBUG] Current game set to: ${this.currentGame}`);

        // Reset sub-tab to first one
        const firstSubTab = document.querySelector(`.sub-tabs[data-game="${game}"] .sub-tab`);
        if (firstSubTab) {
          this.switchSubTab(firstSubTab.dataset.subtab);
        } else {
          // If no sub-tab found, just load data for current race
          this.loadGameData();
        }
      }

      switchSubTab(subTab) {
        console.log(`🔄 [DEBUG] switchSubTab called with subTab: ${subTab}`);
        
        // Update active sub-tab within the current game
        document.querySelectorAll(`.sub-tabs[data-game="${this.currentGame}"] .sub-tab`).forEach(tab => {
          tab.classList.remove('active');
        });
        const activeSubTab = document.querySelector(`.sub-tabs[data-game="${this.currentGame}"] [data-subtab="${subTab}"]`);
        if (activeSubTab) {
          activeSubTab.classList.add('active');
        }

        // Update active sub-content within the current game
        document.querySelectorAll(`.game-content[data-game="${this.currentGame}"] .sub-content`).forEach(content => {
          content.classList.remove('active');
          content.style.display = 'none'; // Ensure hidden
        });
        const activeSubContent = document.querySelector(`.game-content[data-game="${this.currentGame}"] .sub-content[data-subtab="${subTab}"]`);
        if (activeSubContent) {
          activeSubContent.classList.add('active');
          activeSubContent.style.display = 'block'; // Ensure visible
          console.log(`✅ [DEBUG] Activated sub-content for: ${subTab}`);
          
          // Debug: Check sub-content visibility
          setTimeout(() => {
            const rect = activeSubContent.getBoundingClientRect();
            console.log(`🔍 [DEBUG] Sub-content visibility for ${subTab}:`, {
              width: rect.width,
              height: rect.height,
              top: rect.top,
              left: rect.left,
              display: getComputedStyle(activeSubContent).display,
              visibility: getComputedStyle(activeSubContent).visibility
            });
          }, 100);
        } else {
          console.warn(`⚠️ [DEBUG] No sub-content found for: ${subTab}`);
        }

        this.currentRace = subTab;
        console.log(`🔄 [DEBUG] Current race set to: ${this.currentRace}`);
        this.loadGameData();
      }

      async loadInitialData() {
        await this.loadGameData();
      }

      async loadGameData() {
        console.log(`🔄 [DEBUG] loadGameData called for game: ${this.currentGame}, race: ${this.currentRace}`);
        
        // Check if this is a static content tab (not a race)
        const staticTabs = ['general', 'strategy'];
        if (staticTabs.includes(this.currentRace)) {
          console.log(`ℹ️ [DEBUG] Static content tab detected: ${this.currentRace}, skipping dynamic data loading`);
          
          // Hide loading indicator for static content
          const loadingIndicator = document.getElementById('loading-indicator');
          if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
          }
          
          // Debug: Check static content visibility
          setTimeout(() => {
            const staticContent = document.querySelector(`.sub-content[data-subtab="${this.currentRace}"]`);
            if (staticContent) {
              const rect = staticContent.getBoundingClientRect();
              console.log(`🔍 [DEBUG] Static content visibility check for ${this.currentRace}:`, {
                width: rect.width,
                height: rect.height,
                top: rect.top,
                left: rect.left,
                display: window.getComputedStyle(staticContent).display,
                visibility: window.getComputedStyle(staticContent).visibility,
                opacity: window.getComputedStyle(staticContent).opacity
              });
            }
          }, 100);
          
          return;
        }
        
        const loadingIndicator = document.getElementById('loading-indicator');
        loadingIndicator.style.display = 'flex';

        try {
          // Load units
          console.log(`🔄 [DEBUG] Loading units...`);
          await this.loadUnits();
          
          // Load buildings
          console.log(`🔄 [DEBUG] Loading buildings...`);
          await this.loadBuildings();
          
          // Load neutral units (for WC3)
          if (this.currentGame === 'wc3' && this.currentRace === 'neutral') {
            console.log(`🔄 [DEBUG] Loading neutral units...`);
            await this.loadNeutralUnits();
          }
          
          // Load items (for WC3)
          if (this.currentGame === 'wc3' && this.currentRace === 'items') {
            console.log(`🔄 [DEBUG] Loading items...`);
            await this.loadItems();
          }
          
        } catch (error) {
          console.error('❌ [DEBUG] Error loading game data:', error);
        } finally {
          loadingIndicator.style.display = 'none';
        }
      }

      async loadUnits() {
        const containerId = `${this.currentGame}-${this.currentRace}-units`;
        const container = document.getElementById(containerId);
        
        console.log(`🔍 [DEBUG] loadUnits called for: ${this.currentGame}-${this.currentRace}-units`);
        console.log(`🔍 [DEBUG] Container ID: ${containerId}`);
        console.log(`🔍 [DEBUG] Container found:`, container);
        
        if (!container) {
          console.log(`ℹ️ [DEBUG] Container not found for ID: ${containerId} - this is expected for static content tabs`);
          return;
        }

        try {
          const url = `/api/game-units/${this.currentGame}/${this.currentRace}?type=unit`;
          console.log(`🔍 [DEBUG] Fetching units from: ${url}`);
          
          const response = await fetch(url);
          console.log(`🔍 [DEBUG] Response status:`, response.status);
          console.log(`🔍 [DEBUG] Response ok:`, response.ok);
          
          const data = await response.json();
          console.log(`🔍 [DEBUG] Response data:`, data);
          
          if (data.success && data.data) {
            console.log(`✅ [DEBUG] Rendering ${data.data.length} units`);
            this.renderUnits(container, data.data);
          } else {
            console.warn(`⚠️ [DEBUG] No unit data available for ${this.currentGame}/${this.currentRace}`);
            container.innerHTML = '<p class="no-data">No unit data available for this race.</p>';
          }
        } catch (error) {
          console.error('❌ [DEBUG] Error loading units:', error);
          container.innerHTML = '<p class="error">Error loading unit data.</p>';
        }
      }

      async loadBuildings() {
        const containerId = `${this.currentGame}-${this.currentRace}-buildings`;
        const container = document.getElementById(containerId);
        
        console.log(`🔍 [DEBUG] loadBuildings called for: ${this.currentGame}-${this.currentRace}-buildings`);
        console.log(`🔍 [DEBUG] Container ID: ${containerId}`);
        console.log(`🔍 [DEBUG] Container found:`, container);
        
        if (!container) {
          console.log(`ℹ️ [DEBUG] Container not found for ID: ${containerId} - this is expected for static content tabs`);
          return;
        }

        try {
          const url = `/api/game-units/${this.currentGame}/${this.currentRace}?type=building`;
          console.log(`🔍 [DEBUG] Fetching buildings from: ${url}`);
          
          const response = await fetch(url);
          console.log(`🔍 [DEBUG] Response status:`, response.status);
          console.log(`🔍 [DEBUG] Response ok:`, response.ok);
          
          const data = await response.json();
          console.log(`🔍 [DEBUG] Response data:`, data);
          
          if (data.success && data.data) {
            console.log(`✅ [DEBUG] Rendering ${data.data.length} buildings`);
            this.renderBuildings(container, data.data);
          } else {
            console.warn(`⚠️ [DEBUG] No building data available for ${this.currentGame}/${this.currentRace}`);
            container.innerHTML = '<p class="no-data">No building data available for this race.</p>';
          }
        } catch (error) {
          console.error('❌ [DEBUG] Error loading buildings:', error);
          container.innerHTML = '<p class="error">Error loading building data.</p>';
        }
      }

      async loadNeutralUnits() {
        try {
          console.log(`🔄 [DEBUG] Loading neutral units...`);
          const containerId = `${this.currentGame}-neutral-units`;
          const container = document.getElementById(containerId);
          if (!container) {
            console.log(`ℹ️ [DEBUG] Container not found for ID: ${containerId} - this is expected for static content tabs`);
            return;
          }
          const url = `/api/game-units/${this.currentGame}/neutral?type=unit`;
          console.log(`🔍 [DEBUG] Fetching neutral units from: ${url}`);
          const response = await fetch(url);
          console.log(`🔍 [DEBUG] Response status:`, response.status);
          console.log(`🔍 [DEBUG] Response ok:`, response.ok);
          const data = await response.json();
          console.log(`🔍 [DEBUG] Response data:`, data);
          if (data.success && data.data) {
            console.log(`✅ [DEBUG] Rendering ${data.data.length} neutral units`);
            this.renderUnits(container, data.data);
          } else {
            console.warn(`⚠️ [DEBUG] No neutral unit data available for ${this.currentGame}`);
            container.innerHTML = '<p class="no-data">No neutral unit data available.</p>';
          }
        } catch (error) {
          console.error('❌ [DEBUG] Error loading neutral units:', error);
          const container = document.getElementById(`${this.currentGame}-neutral-units`);
          if (container) {
            container.innerHTML = '<p class="error">Error loading neutral unit data.</p>';
          }
        }
      }
    }
  </script>
</body>
</html>