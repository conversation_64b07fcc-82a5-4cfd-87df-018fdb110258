/**
 * AuthManager - <PERSON>les authentication state and token management
 */
export default class AuthManager {
  static instance = null;

  constructor() {
    if (AuthManager.instance) {
      return AuthManager.instance;
    }
    AuthManager.instance = this;

    this.isElectron = this.checkIsElectron();
    this.authToken = null;
    this.user = null;
    this.initialized = false;
    
    // Set up event listeners
    this.setupEventListeners();
  }

  static getInstance() {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager();
    }
    return AuthManager.instance;
  }

  static async init() {
    const instance = AuthManager.getInstance();
    if (!instance.initialized) {
      await instance.initialize();
    }
    return instance;
  }

  checkIsElectron() {
    return typeof window !== 'undefined' && 
           (window.electronAPI || 
            window.require || 
            navigator.userAgent.toLowerCase().indexOf('electron') > -1);
  }

  setupEventListeners() {
    if (this.isElectron && window.electronAPI) {
      // Listen for auth messages from Electron
      window.addEventListener('message', (event) => {
        if (event.data.type === 'ELECTRON_AUTH') {
          this.handleElectronAuth(event.data.token);
        }
      });
    }
  }

  async initialize() {
    try {
      console.log('🔐 Initializing AuthManager...');

      // Check for stored token
      const storedToken = localStorage.getItem('authToken');
      if (storedToken) {
        this.authToken = storedToken;
        console.log('✅ Found stored auth token');
      }

      // In Electron mode, wait for authentication to be established
      if (this.isElectron) {
        console.log('🖥️ Electron mode detected, waiting for authentication...');
        await this.waitForElectronAuth();
      }

      // Try to get user data
      await this.fetchUserData();

      this.initialized = true;
      console.log('✅ AuthManager initialized');
    } catch (error) {
      console.error('❌ AuthManager initialization failed:', error);
      this.initialized = true; // Still mark as initialized
    }
  }

  async waitForElectronAuth() {
    return new Promise((resolve) => {
      // If we already have a token, resolve immediately
      if (this.authToken) {
        console.log('✅ Auth token already available');
        resolve();
        return;
      }

      // Wait for Electron to provide authentication
      let attempts = 0;
      const maxAttempts = 50; // 5 seconds max wait

      const checkAuth = () => {
        attempts++;

        // Check if token was set by Electron
        const electronToken = localStorage.getItem('authToken');
        if (electronToken) {
          this.authToken = electronToken;
          console.log('✅ Electron authentication established');
          resolve();
          return;
        }

        // Check if we're in a logged-in state via cookies
        if (document.cookie.includes('connect.sid')) {
          console.log('✅ Session cookie found, proceeding with authentication');
          resolve();
          return;
        }

        if (attempts >= maxAttempts) {
          console.warn('⚠️ Electron authentication timeout, proceeding anyway');
          resolve();
          return;
        }

        console.log(`⏳ Waiting for Electron authentication... (${attempts}/${maxAttempts})`);
        setTimeout(checkAuth, 100);
      };

      checkAuth();
    });
  }

  handleElectronAuth(token) {
    if (token) {
      this.authToken = token;
      localStorage.setItem('authToken', token);
      this.fetchUserData();
    }
  }

  async fetchUserData() {
    try {
      // In Electron mode, don't block if authentication isn't established
      if (this.isElectron && !this.authToken && !document.cookie.includes('connect.sid')) {
        console.log('🛑 User data load blocked in Electron mode - authentication not established');
        return null;
      }

      const headers = {
        'Content-Type': 'application/json'
      };

      if (this.authToken) {
        headers['Authorization'] = `Bearer ${this.authToken}`;
      }

      const response = await fetch('/api/me', {
        headers,
        credentials: 'include' // Include cookies for session-based auth
      });

      if (response.ok) {
        const userData = await response.json();
        this.user = userData;
        console.log('✅ User data loaded:', userData.username);
        this.dispatchAuthEvent('login', userData);
        return userData;
      } else if (response.status === 401) {
        console.log('❌ User not authenticated');
        this.clearAuth();
        return null;
      } else {
        throw new Error(`API request failed: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Failed to fetch user data:', error);
      return null;
    }
  }

  clearAuth() {
    this.authToken = null;
    this.user = null;
    localStorage.removeItem('authToken');
    this.dispatchAuthEvent('logout', null);
  }

  dispatchAuthEvent(type, data) {
    window.dispatchEvent(new CustomEvent('authStateChange', {
      detail: { type, data, user: this.user, isAuthenticated: !!this.user }
    }));
  }

  isAuthenticated() {
    return !!this.user;
  }

  getUser() {
    return this.user;
  }

  getToken() {
    return this.authToken;
  }
}

// Auto-initialize when imported
if (typeof window !== 'undefined') {
  AuthManager.init();
} 