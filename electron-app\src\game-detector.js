const fs = require('fs').promises;
const path = require('path');
const { exec, spawn } = require('child_process');
const { promisify } = require('util');
const Store = require('electron-store');

const execAsync = promisify(exec);

class GameDetector {
  /**
   * Enhanced Game Detector for Warcraft Games
   * 
   * Improvements:
   * 1. Warcraft II GOG vs BNE: Now defaults to "Battle.net Edition" unless explicitly 
   *    detected in GOG directories (GOG Games, GOG Galaxy, etc.)
   * 2. DOS Version Detection: Comprehensive detection of DOS versions for both 
   *    Warcraft I and II including:
   *    - DOSBox configurations
   *    - Raw DOS executables (WAR.EXE, WAR2.EXE, etc.)
   *    - ScummVM installations
   *    - Various batch file launchers
   * 3. Robust Warcraft 1 DOS: Handles multiple DOS configurations:
   *    - DOSBox with configuration files
   *    - Standalone DOS executables
   *    - Batch file launchers
   *    - Non-DOSBox DOS compatibility layers
   */
  constructor() {
    this.detectedGames = [];
    this.manualGames = [];
    this.isDetectionComplete = false;
    this.statusCheckInterval = null;
    this.commonPaths = this.getCommonGamePaths();
    
    // Initialize manual games storage
    this.store = new Store({ name: 'wc-arena-companion-games' });
    this.loadManualGames();
  }

  generateGameId() {
    // Generate a unique ID for games
    return 'game_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  sortGames(games) {
    // Define sort order: Battle.net Launcher, WC1, WC2, WC3, W3Champions
    const sortOrder = {
      'battlenet': 1,
      'warcraft1': 2,
      'warcraft2': 3,
      'warcraft3': 4,
      'w3champions': 5
    };
    
    return games.sort((a, b) => {
      const orderA = sortOrder[a.type] || 999;
      const orderB = sortOrder[b.type] || 999;
      
      if (orderA !== orderB) {
        return orderA - orderB;
      }
      
      // If same type, sort alphabetically by name
      return a.name.localeCompare(b.name);
    });
  }

  // Get clean display name for games
  getDisplayName(game) {
    const type = game.type || '';
    const name = game.name || '';
    
    // Map game types to clean display names
    const displayNames = {
      'battlenet': 'Battle.net',
      'warcraft1': 'WC I',
      'warcraft2': 'WC II', 
      'warcraft3': 'WC III',
      'w3champions': 'W3C'
    };
    
    // Return clean display name if type is known
    if (displayNames[type]) {
      return displayNames[type];
    }
    
    // Fallback: try to determine from name
    const nameLower = name.toLowerCase();
    if (nameLower.includes('warcraft i') || nameLower.includes('war1') || nameLower.includes('orcs & humans')) {
      return 'WC I';
    } else if (nameLower.includes('warcraft ii') || nameLower.includes('war2') || nameLower.includes('battle.net edition')) {
      return 'WC II';
    } else if (nameLower.includes('warcraft iii') || nameLower.includes('war3') || nameLower.includes('reforged')) {
      return 'WC III';
    } else if (nameLower.includes('w3champions') || nameLower.includes('w3c')) {
      return 'W3C';
    } else if (nameLower.includes('battle.net')) {
      return 'Battle.net';
    }
    
    // Final fallback: return original name
    return name;
  }

  isDuplicateGame(newGame) {
    // Check if a similar game already exists
    return this.detectedGames.some(existingGame => {
      // Check for exact path match
      if (existingGame.path === newGame.path) {
        return true;
      }
      
      // Special handling for Warcraft I DOSBox games
      if (newGame.type === 'warcraft1' && existingGame.type === 'warcraft1') {
        // If both are DOSBox games with same gameDir, consider them duplicates
        if (newGame.gameDir && existingGame.gameDir) {
          const newDir = path.normalize(newGame.gameDir).toLowerCase();
          const existingDir = path.normalize(existingGame.gameDir).toLowerCase();
          if (newDir === existingDir) {
            return true;
          }
        }
        
        // If both are from the same installation directory
        const newParentDir = path.dirname(newGame.path);
        const existingParentDir = path.dirname(existingGame.path);
        if (path.normalize(newParentDir).toLowerCase() === path.normalize(existingParentDir).toLowerCase()) {
          // Prefer DOSBox.exe over batch files
          if (existingGame.executable === 'DOSBox.exe' && newGame.executable.endsWith('.bat')) {
            return true;
          }
        }
      }
      
      // Check for same type and similar name
      if (existingGame.type === newGame.type && 
          existingGame.name === newGame.name) {
        return true;
      }
      
      return false;
    });
  }

  getCommonGamePaths() {
    const basePaths = [
      // Windows Program Files
      'C:\\Program Files\\',
      'C:\\Program Files (x86)\\',
      'D:\\Program Files\\',
      'D:\\Program Files (x86)\\',
      'E:\\Program Files\\',
      'E:\\Program Files (x86)\\',

      // Common game directories
      'C:\\Games\\',
      'D:\\Games\\',
      'E:\\Games\\',
      'C:\\GOG Games\\',
      'D:\\GOG Games\\',
      'E:\\GOG Games\\',
      
      // Steam directories
      'C:\\Program Files\\Steam\\steamapps\\common\\',
      'C:\\Program Files (x86)\\Steam\\steamapps\\common\\',
      'D:\\Steam\\steamapps\\common\\',
      'D:\\Program Files\\Steam\\steamapps\\common\\',
      'D:\\Program Files (x86)\\Steam\\steamapps\\common\\',

      // Battle.net directories  
      'C:\\Program Files\\Battle.net\\',
      'C:\\Program Files (x86)\\Battle.net\\',
      'D:\\Battle.net\\',

      // Direct game directories (from user's system)
      'D:\\War2Combat\\',
      'D:\\Warcraft II BNE\\',
      'D:\\Warcraft II Remastered\\',
      'D:\\W3Champions\\',
      'C:\\Program Files\\W3Champions\\',
      'D:\\Program Files\\W3Champions\\',
      
      // Additional Remastered game paths
      'C:\\Program Files (x86)\\Warcraft I Remastered\\',
      'C:\\Program Files\\Warcraft I Remastered\\',
      'C:\\Program Files (x86)\\Warcraft II Remastered\\',
      'C:\\Program Files\\Warcraft II Remastered\\',
      'D:\\Warcraft I Remastered\\',
      'D:\\Warcraft II Remastered\\',
      'C:\\Program Files (x86)\\Warcraft I & II Battle.net Edition\\',
      'C:\\Program Files\\Warcraft I & II Battle.net Edition\\',
      'D:\\Warcraft I & II Battle.net Edition\\',

      // Legacy directories
      'C:\\',
      'D:\\',
      'E:\\'
    ];

    return basePaths;
  }

  async findAllGames() {
    console.log('Starting comprehensive game detection...');
    this.detectedGames = [];
    this.isDetectionComplete = false;

    try {
      // Search for each game type
      await Promise.all([
        this.findWarcraft1Games(),
        this.findWarcraft2Games(),
        this.findWarcraft3Games(),
        this.findW3ChampionsGames(),
        this.findBattleNetClient(),
        this.searchWindowsRegistry(), // Include registry search in main flow
        this.deepSearchForClassicGames(), // Additional deep search for older games
      ]);

      // Clean up and prioritize games
      this.prioritizeAndCleanGames();
      this.isDetectionComplete = true;
      
      console.log(`Game detection complete. Found ${this.detectedGames.length} games.`);
      return this.detectedGames;
    } catch (error) {
      console.error('Error during game detection:', error);
      this.isDetectionComplete = true; // Set to true even on error to prevent infinite loops
      return this.detectedGames;
    }
  }

  async findWarcraft1Games() {
    console.log('Searching for Warcraft I games...');
    
    const possiblePaths = [
      'C:\\Program Files\\Warcraft\\War.exe',
      'C:\\Program Files (x86)\\Warcraft\\War.exe',
      'C:\\Program Files\\Warcraft I\\War.exe',
      'C:\\Program Files (x86)\\Warcraft I\\War.exe',
      'C:\\Program Files\\Warcraft Orcs & Humans\\War.exe',
      'C:\\Program Files (x86)\\Warcraft Orcs & Humans\\War.exe',
      'C:\\Program Files (x86)\\Warcraft I Remastered\\x86\\Warcraft.exe'
    ];
    
    // Check for DOSBox installations
    const dosboxPaths = [
      'C:\\Program Files\\Warcraft Orcs & Humans\\dos\\DOSBox.exe',
      'C:\\Program Files (x86)\\Warcraft Orcs & Humans\\dos\\DOSBox.exe'
    ];
    
    for (const dosboxPath of dosboxPaths) {
      const dosboxExists = await this.pathExists(dosboxPath);
      if (dosboxExists) {
        const dosboxDir = path.dirname(dosboxPath);
        const gameDir = path.dirname(dosboxDir);
        const confPath = path.join(dosboxDir, 'dosbox.conf');
        
        // Check if warcraft data exists
        const warcraftData = path.join(dosboxDir, 'WARCRAFT');
        const warcraftExists = await this.pathExists(warcraftData);
        
        if (warcraftExists) {
          const game = {
            id: this.generateGameId(),
            name: 'Warcraft: Orcs & Humans (DOS)',
            type: 'warcraft1',
            path: dosboxPath,
            configPath: confPath,
            gameDir: dosboxDir,
            isRunning: false,
            executable: 'DOSBox.exe',
            args: ['-noconsole', '-conf', confPath, '-title', 'Warcraft: Orcs & Humans [Version 1.22h][DOS]'],
            lastDetected: new Date().toISOString()
          };
          
          // Check if we already have this game
          if (!this.isDuplicateGame(game)) {
            this.detectedGames.push(game);
            console.log(`Found Warcraft I DOSBox setup at: ${dosboxPath}`);
          }
        }
      }
    }
    
    // Enhanced DOS version detection - search for various DOS configurations
    await this.findDOSVersions();
    
    // Standard search for other versions
    await this.searchForGames(this.getWarcraft1Patterns(), 'warcraft1');
  }

  async findDOSVersions() {
    console.log('Searching for DOS versions of Warcraft games...');
    
    // Look for common DOS game directories
    const dosSearchPaths = [
      'C:\\DOS',
      'C:\\DOSGames',
      'C:\\Games\\DOS',
      'D:\\DOS',
      'D:\\DOSGames',
      'D:\\Games\\DOS',
      'C:\\OLDGAMES',
      'D:\\OLDGAMES'
    ];
    
    for (const searchPath of dosSearchPaths) {
      try {
        const exists = await this.pathExists(searchPath);
        if (exists) {
          await this.searchDirectoryThoroughly(searchPath);
        }
      } catch (error) {
        continue;
      }
    }
    
    // Also look for DOS versions in user directories
    const userDirs = [
      'C:\\Users\\<USER>\\Documents\\DOS Games',
      'C:\\Users\\<USER>\\Downloads\\DOS Games',
      'C:\\Users\\<USER>\\Desktop\\DOS Games'
    ];
    
    for (const userDir of userDirs) {
      try {
        const exists = await this.pathExists(userDir);
        if (exists) {
          await this.searchDirectoryThoroughly(userDir);
        }
      } catch (error) {
        continue;
      }
    }
  }

  async findWarcraft2Games() {
    const wc2Patterns = [
      { name: 'Warcraft II: Tides of Darkness', executable: 'WAR2.EXE', folder: 'warcraft ii' },
      { name: 'Warcraft II: Beyond the Dark Portal', executable: 'WAR2X.EXE', folder: 'warcraft ii' },
      { name: 'Warcraft II: Battle.net Edition', executable: 'WAR2BNE.EXE', folder: 'warcraft ii bne' },
      { name: 'Warcraft II (GOG)', executable: 'Warcraft II BNE.exe', folder: 'warcraft 2 battlenet edition' },
      { name: 'Warcraft II (Remastered)', executable: 'Warcraft I & II Battle.net Edition.exe', folder: 'warcraft i & ii battle.net edition' },
      { name: 'Warcraft II (Remastered)', executable: 'Warcraft II Remastered.exe', folder: 'warcraft ii remastered' },
      { name: 'Warcraft II: Tides of Darkness', executable: 'Warcraft II- Tides of Darkness.exe', folder: 'warcraft' },
    ];

    await this.searchForGames(wc2Patterns, 'warcraft2');
  }

  async findWarcraft3Games() {
    const wc3Patterns = [
      // Classic Warcraft 3
      { name: 'Warcraft III: Reign of Chaos', executable: 'war3.exe', folder: 'warcraft iii' },
      { name: 'Warcraft III: The Frozen Throne', executable: 'Frozen Throne.exe', folder: 'warcraft iii' },
      { name: 'Warcraft III: The Frozen Throne', executable: 'war3x.exe', folder: 'warcraft iii' },
      
      // Reforged versions
      { name: 'Warcraft III: Reforged', executable: 'Warcraft III.exe', folder: 'warcraft iii' },
      { name: 'Warcraft III: Reforged', executable: '_retail_\\x86_64\\Warcraft III.exe', folder: 'warcraft iii' },
      { name: 'Warcraft III (PTR)', executable: '_ptr_\\x86_64\\Warcraft III.exe', folder: 'warcraft iii' },
      { name: 'Warcraft III (Beta)', executable: '_beta_\\x86_64\\Warcraft III.exe', folder: 'warcraft iii' },
      
      // Additional patterns
      { name: 'Warcraft III', executable: 'warcraft3.exe', folder: 'warcraft' },
      { name: 'Warcraft III', executable: 'WarcraftIII.exe', folder: 'warcraft' },
      { name: 'Warcraft III', executable: 'Warcraft 3.exe', folder: 'warcraft' },
    ];

    await this.searchForGames(wc3Patterns, 'warcraft3');
  }

  async findW3ChampionsGames() {
    const w3cPatterns = [
      { name: 'W3Champions', executable: 'W3Champions.exe', folder: 'w3champions' },
      { name: 'W3Champions', executable: 'W3Champions.App.exe', folder: 'w3champions' },
      { name: 'W3Champions', executable: 'W3Champions Launcher.exe', folder: 'w3champions' },
      { name: 'W3Champions', executable: 'W3ChampionsLauncher.exe', folder: 'w3champions' },
    ];

    await this.searchForGames(w3cPatterns, 'w3champions');
  }

  async findBattleNetClient() {
    const battleNetPatterns = [
      { name: 'Battle.net', executable: 'Battle.net.exe', folder: 'battle.net' },
      { name: 'Battle.net Launcher', executable: 'Battle.net Launcher.exe', folder: 'battle.net' },
      { name: 'Battle.net', executable: 'BlizzardBrowser.exe', folder: 'battle.net' },
    ];

    // Also check specific Battle.net installation paths
    const specificBattleNetPaths = [
      'C:\\Program Files (x86)\\Battle.net\\',
      'C:\\Program Files\\Battle.net\\',
      'D:\\Program Files (x86)\\Battle.net\\',
      'D:\\Program Files\\Battle.net\\',
    ];

    for (const battleNetPath of specificBattleNetPaths) {
      try {
        const exists = await this.pathExists(battleNetPath);
        if (exists) {
          for (const pattern of battleNetPatterns) {
            await this.checkForExecutables(battleNetPath, pattern, 'battlenet');
          }
        }
      } catch (error) {
        continue;
      }
    }

    await this.searchForGames(battleNetPatterns, 'battlenet');
  }

  prioritizeAndCleanGames() {
    console.log('Prioritizing and cleaning detected games...');
    
    // Group games by type
    const gamesByType = {};
    this.detectedGames.forEach(game => {
      if (!gamesByType[game.type]) {
        gamesByType[game.type] = [];
      }
      gamesByType[game.type].push(game);
    });
    
    const cleanedGames = [];
    
    // Process each game type with specific rules
    for (const [type, games] of Object.entries(gamesByType)) {
      console.log(`\nProcessing ${type} games (${games.length} found):`);
      games.forEach(g => console.log(`  - ${g.name} | ${g.executable} | ${g.path}`));
      
      if (type === 'w3champions') {
        // W3CHAMPIONS: Keep only ONE installation (prefer Program Files)
        const preferred = games.find(g => g.path.includes('Program Files')) || games[0];
        if (preferred) {
          preferred.name = 'W3Champions'; // Clean name
          cleanedGames.push(preferred);
          console.log(`✅ Kept single W3Champions: ${preferred.path}`);
        }
        
      } else if (type === 'battlenet') {
        // BATTLE.NET: Keep ONLY the launcher, never the main executable
        const launcher = games.find(g => g.executable === 'Battle.net Launcher.exe');
        if (launcher) {
          launcher.name = 'Battle.net Launcher'; // Clean name
          cleanedGames.push(launcher);
          console.log(`✅ Kept Battle.net Launcher only: ${launcher.path}`);
        } else {
          console.log(`❌ No Battle.net Launcher found, skipping all Battle.net entries`);
        }
        
      } else if (type === 'warcraft1') {
        // WARCRAFT 1: Prioritize DOSBox configurations over batch files
        const dosboxGames = games.filter(g => 
          g.executable === 'DOSBox.exe' && g.configPath && g.args
        );
        const remastered = games.filter(g => 
          g.executable && g.executable.toLowerCase().includes('warcraft.exe')
        );
        const otherExecutables = games.filter(g => {
          const exe = g.executable.toLowerCase();
          return exe.includes('war.exe') && !exe.includes('dosbox.exe');
        });
        const batchFiles = games.filter(g => 
          g.executable.endsWith('.bat') && !dosboxGames.length
        );
        
        // Prioritize: DOSBox > Remastered > Other executables > Batch files (only if no DOSBox)
        const selectedGames = [
          ...dosboxGames,
          ...remastered,
          ...otherExecutables,
          ...batchFiles
        ];
        
        if (selectedGames.length > 0) {
          // Clean up names for Warcraft I games
          selectedGames.forEach(game => {
            if (game.executable === 'DOSBox.exe') {
              game.name = 'WC I (DOS)';
            } else if (game.executable && game.executable.toLowerCase().includes('warcraft.exe')) {
              game.name = 'WC I Remastered';
            } else {
              game.name = 'WC I';
            }
          });
          
          cleanedGames.push(...selectedGames);
          console.log(`✅ Kept ${selectedGames.length} Warcraft I installations`);
          if (dosboxGames.length > 0) {
            console.log(`   🎯 DOSBox configuration prioritized`);
          }
        }
        
      } else if (type === 'warcraft2') {
        // WARCRAFT 2: Keep main game executables from different installations
        const mainGames = games.filter(g => {
          const exe = g.executable.toLowerCase();
          const isMainGame = exe.includes('warcraft ii bne.exe') || 
                           exe.includes('warcraft ii.exe') ||
                           exe.includes('war2bne.exe') ||
                           exe.includes('war2.exe');
          return isMainGame;
        });
        
        if (mainGames.length > 0) {
          // Clean up names for Warcraft II games
          mainGames.forEach(game => {
            const exe = game.executable.toLowerCase();
            if (exe.includes('combat edition')) {
              game.name = 'WC II Combat Edition';
            } else if (exe.includes('battle.net edition') || exe.includes('bne')) {
              game.name = 'WC II BNE';
            } else if (exe.includes('remastered')) {
              game.name = 'WC II Remastered';
            } else {
              game.name = 'WC II';
            }
          });
          
          cleanedGames.push(...mainGames);
          console.log(`✅ Kept ${mainGames.length} Warcraft II installations`);
        }
        
      } else if (type === 'warcraft3') {
        // WARCRAFT 3: Keep all Warcraft III games (filtering already done by type)
        const mainGames = games;
        
        if (mainGames.length > 0) {
          // Clean up names for Warcraft III games
          mainGames.forEach(game => {
            const exe = game.executable.toLowerCase();
            if (exe.includes('reforged')) {
              game.name = 'WC III Reforged';
            } else {
              game.name = 'WC III';
            }
          });
          
          cleanedGames.push(...mainGames);
          console.log(`✅ Kept ${mainGames.length} Warcraft III installations`);
        }
      }
    }
    
    // Sort games in the desired order: Battle.net Launcher, WC1, WC2, WC3, W3Champions
    const sortOrder = {
      'battlenet': 1,
      'warcraft1': 2,
      'warcraft2': 3,
      'warcraft3': 4,
      'w3champions': 5
    };
    
    cleanedGames.sort((a, b) => {
      const orderA = sortOrder[a.type] || 999;
      const orderB = sortOrder[b.type] || 999;
      if (orderA !== orderB) {
        return orderA - orderB;
      }
      // Within same type, sort by name
      return a.name.localeCompare(b.name);
    });
    
    // Update game names to indicate Battle.net launching for remastered games
    cleanedGames.forEach(game => {
      const battlenetProtocol = this.getBattleNetProtocol(game);
      if (battlenetProtocol) {
        if (!game.name.includes('(via Battle.net)')) {
          game.name = game.name + ' (via Battle.net)';
        }
      }
      
      // Add display name to game object
      game.displayName = this.getDisplayName(game);
    });
    
    this.detectedGames = cleanedGames;
    console.log(`\n🎯 Final cleaned and sorted games list: ${cleanedGames.length} games remaining`);
    cleanedGames.forEach((game, index) => {
      console.log(`${index + 1}. ${game.name} (${game.type}) - ${game.path}`);
      console.log(`   DisplayName: ${game.displayName}, Executable: ${game.executable}`);
    });
    
    // Log games by type for debugging
    const gamesByTypeDebug = {};
    cleanedGames.forEach(game => {
      if (!gamesByTypeDebug[game.type]) gamesByTypeDebug[game.type] = [];
      gamesByTypeDebug[game.type].push(game);
    });
    
    console.log('\n📊 Games by type:');
    Object.entries(gamesByTypeDebug).forEach(([type, games]) => {
      console.log(`  ${type}: ${games.length} games`);
      games.forEach(game => {
        console.log(`    - ${game.name} (${game.displayName})`);
      });
    });
  }

  async deepSearchForClassicGames() {
    console.log('Performing deep search for classic Warcraft games...');
    
    // Search root directories for any folder containing "warcraft" or "war"
    const rootPaths = ['C:\\', 'D:\\', 'E:\\'];
    
    for (const rootPath of rootPaths) {
      try {
        const exists = await this.pathExists(rootPath);
        if (!exists) continue;
        
        const entries = await fs.readdir(rootPath, { withFileTypes: true });
        
        for (const entry of entries) {
          if (entry.isDirectory()) {
            const dirName = entry.name.toLowerCase();
            
            // Look for any directory that might contain Warcraft games (more specific)
            if ((dirName.includes('warcraft') && !dirName.includes('world of warcraft') && !dirName.includes('wartales')) ||
                dirName.includes('war2') ||
                dirName.includes('war3') ||
                dirName.includes('w3champions') ||
                (dirName.includes('war') && (dirName.includes('combat') || dirName.includes('bne') || dirName.includes('remastered'))) ||
                (dirName.includes('game') && dirName.length < 15) ||
                dirName.includes('classic') ||
                dirName.includes('retro') ||
                dirName.includes('dosbox') ||
                dirName.includes('remastered') ||
                (dirName.includes('blizzard') && !dirName.includes('world of warcraft')) ||
                (dirName.includes('warcraft i') || dirName.includes('warcraft 1')) ||
                (dirName.includes('warcraft ii') || dirName.includes('warcraft 2'))) {
              
              const fullPath = path.join(rootPath, entry.name);
              console.log(`Deep searching in: ${fullPath}`);
              
              // Search this directory thoroughly
              await this.searchDirectoryThoroughly(fullPath);
            }
          }
        }
      } catch (error) {
        // Continue on error
        continue;
      }
    }
  }

  async searchDirectoryThoroughly(dirPath) {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      // Look for any .exe files that might be Warcraft games
      for (const entry of entries) {
        if ((entry.isFile() && entry.name.toLowerCase().endsWith('.exe')) ||
            (entry.isFile() && entry.name.toLowerCase().endsWith('.bat'))) {
          const fileName = entry.name.toLowerCase();
          const fullPath = path.join(dirPath, entry.name);
          
          // Check if this looks like a Warcraft executable (more specific)
          if ((fileName.includes('warcraft') && !fileName.includes('wartales') && !fileName.includes('world of warcraft')) ||
              fileName.includes('w3champions') ||
              (fileName.includes('battle') && fileName.includes('net')) ||
              fileName.includes('war2') ||
              fileName.includes('war3') ||
              fileName.includes('war1') ||
              (fileName.includes('war') && (fileName.includes('bne') || fileName.includes('exe') && fileName.length < 20)) ||
              (fileName.includes('dosbox') && (dirPath.toLowerCase().includes('warcraft') || dirPath.toLowerCase().includes('war'))) ||
              (fileName.endsWith('.bat') && (fileName.includes('war') || fileName.includes('warcraft'))) ||
              // Enhanced DOS version detection - look for raw DOS executables
              (fileName === 'war.exe' && (dirPath.toLowerCase().includes('warcraft') || dirPath.toLowerCase().includes('war'))) ||
              (fileName === 'warcraft.exe' && !fileName.includes('ii') && !fileName.includes('iii')) ||
              (fileName === 'war1.exe') ||
              (fileName === 'war2.exe') ||
              (fileName === 'war2x.exe') ||
              (fileName === 'war2bne.exe') ||
              (fileName.includes('scummvm') && (dirPath.toLowerCase().includes('warcraft') || dirPath.toLowerCase().includes('war')))) {
            
            // Skip utility/setup files
            if (fileName.includes('setup') || 
                fileName.includes('installer') ||
                fileName.includes('patcher') ||
                fileName.includes('editor') ||
                fileName.includes('config') ||
                fileName.includes('loader') ||
                fileName.includes('insight') ||
                fileName.includes('observe') ||
                fileName.includes('video') ||
                fileName.includes('launcher') && !fileName.includes('w3champions') && !fileName.includes('battle.net')) {
              continue;
            }
            
            console.log(`Found potential Warcraft executable: ${fullPath}`);
            
            // Try to determine game type and add it
            let gameType = 'warcraft1';
            let gameName = 'Warcraft Game';
            
            if (fileName.includes('war1') || 
                (fileName.includes('warcraft') && !fileName.includes('ii') && !fileName.includes('iii') && !fileName.includes('2') && !fileName.includes('3')) ||
                (fileName.includes('dosbox') && dirPath.toLowerCase().includes('warcraft') && !dirPath.toLowerCase().includes('ii') && !dirPath.toLowerCase().includes('2')) ||
                (fileName.endsWith('.bat') && (fileName.includes('war1') || fileName.includes('warcraft1'))) ||
                fileName.includes('scummvm')) {
              gameType = 'warcraft1';
              if (dirPath.toLowerCase().includes('remastered')) {
                gameName = 'Warcraft: Orcs & Humans (Remastered)';
              } else if (fileName.includes('dosbox') || fileName.endsWith('.bat') || fileName.includes('scummvm')) {
                gameName = 'Warcraft: Orcs & Humans (DOS)';
              } else {
                gameName = 'Warcraft: Orcs & Humans';
              }
            } else if (fileName.includes('war2') || fileName.includes('warcraft ii') || dirPath.toLowerCase().includes('war2')) {
              gameType = 'warcraft2';
              // Check for War2Combat specifically
              if (dirPath.toLowerCase().includes('war2combat')) {
                gameName = 'Warcraft 2 Combat Edition';
              } else if (dirPath.toLowerCase().includes('remastered')) {
                gameName = 'Warcraft II: Remastered';
              } else if (fileName.includes('warcraft ii bne.exe') || fileName.includes('war2bne.exe')) {
                gameName = 'Warcraft II: Battle.net Edition';
              } else if (fileName.includes('warcraft ii.exe') || fileName.includes('war2.exe')) {
                gameName = 'Warcraft II: Tides of Darkness';
              } else if (fileName.includes('dosbox') || fileName.endsWith('.bat')) {
                gameName = 'Warcraft II (DOS)';
              } else {
                gameName = 'Warcraft II';
              }
            } else if (fileName.includes('war3') || fileName.includes('warcraft iii') || fileName.includes('frozen')) {
              gameType = 'warcraft3';
              gameName = 'Warcraft III';
            } else if (fileName.includes('w3champions')) {
              gameType = 'w3champions';
              gameName = 'W3Champions';
            } else if (fileName.includes('battle') && fileName.includes('net')) {
              gameType = 'battlenet';
              if (fileName.includes('launcher')) {
                gameName = 'Battle.net Launcher';
              } else {
                gameName = 'Battle.net';
              }
            }
            
            // Create a game entry
            let game = {
              name: gameName + ` (${entry.name})`,
              type: gameType,
              path: fullPath,
              directory: dirPath,
              executable: entry.name,
              id: this.generateGameId(),
              lastDetected: new Date().toISOString()
            };
            
            // Special handling for DOSBox games - try to find proper DOSBox configuration
            if (gameType === 'warcraft1' && entry.name.endsWith('.bat')) {
              // Check if this is in a DOSBox directory structure
              const dosDir = path.join(dirPath, 'dos');
              const dosboxExe = path.join(dosDir, 'DOSBox.exe');
              const dosboxConf = path.join(dosDir, 'dosbox.conf');
              const warcraftData = path.join(dosDir, 'WARCRAFT');
              
              try {
                if (await this.pathExists(dosboxExe) && await this.pathExists(dosboxConf) && await this.pathExists(warcraftData)) {
                  // Create proper DOSBox configuration instead of using batch file
                  game = {
                    id: this.generateGameId(),
                    name: 'Warcraft: Orcs & Humans (DOS)',
                    type: 'warcraft1',
                    path: dosboxExe,
                    configPath: dosboxConf,
                    gameDir: dosDir,
                    isRunning: false,
                    executable: 'DOSBox.exe',
                    args: ['-noconsole', '-conf', dosboxConf, '-title', 'Warcraft: Orcs & Humans [Version 1.22h][DOS]'],
                    lastDetected: new Date().toISOString()
                  };
                  console.log(`Found proper DOSBox configuration for Warcraft I at: ${dosboxExe}`);
                }
              } catch (error) {
                // If DOSBox setup fails, keep the original batch file entry
              }
            }
            
            // Check for duplicates (improved detection)
            const normalizedPath = path.normalize(game.path).toLowerCase();
            const isDuplicate = this.detectedGames.some(g => {
              const existingPath = path.normalize(g.path).toLowerCase();
              const existingName = g.name.toLowerCase();
              const newName = game.name.toLowerCase();
              
              // Same exact path
              if (existingPath === normalizedPath) return true;
              
              // Special handling for Warcraft I DOSBox - prioritize properly configured DOSBox.exe over batch files
              if (game.type === 'warcraft1' && g.type === 'warcraft1') {
                const gameDir = path.dirname(game.path);
                const existingDir = path.dirname(g.path);
                const sameDirectory = path.normalize(gameDir).toLowerCase() === path.normalize(existingDir).toLowerCase();
                
                if (sameDirectory) {
                  // If we already have a DOSBox.exe with proper config, skip batch files
                  if (g.executable === 'DOSBox.exe' && g.args && g.configPath && game.executable.endsWith('.bat')) {
                    console.log(`Skipping ${game.executable} - DOSBox.exe already configured properly`);
                    return true;
                  }
                  // If new entry is DOSBox.exe with proper config, mark existing batch file as duplicate
                  if (game.executable === 'DOSBox.exe' && game.args && game.configPath && g.executable.endsWith('.bat')) {
                    return false; // Allow the DOSBox.exe to replace the batch file
                  }
                }
              }
              
              // Same game type and similar name/executable
              if (g.type === game.type) {
                // For W3Champions - only keep one
                if (game.type === 'w3champions' && existingName.includes('w3champions') && newName.includes('w3champions')) {
                  return true;
                }
                // For Battle.net - only keep one
                if (game.type === 'battlenet' && existingName.includes('battle.net') && newName.includes('battle.net')) {
                  return true;
                }
                // For Warcraft II - avoid multiple BNE versions
                if (game.type === 'warcraft2' && 
                    existingName.includes('warcraft ii') && newName.includes('warcraft ii') &&
                    g.executable.toLowerCase() === game.executable.toLowerCase()) {
                  return true;
                }
              }
              
              return false;
            });
            
            if (!isDuplicate) {
              this.detectedGames.push(game);
              console.log(`Deep search found: ${gameName} at: ${fullPath}`);
            } else {
              console.log(`Skipping duplicate/similar: ${gameName} at: ${fullPath}`);
            }
          }
        } else if (entry.isDirectory()) {
          // Search one level deeper
          const subPath = path.join(dirPath, entry.name);
          try {
            await this.searchDirectoryThoroughly(subPath);
          } catch (error) {
            // Continue on error
          }
        }
      }
    } catch (error) {
      // Continue on error
    }
  }

  async searchForGames(patterns, gameType) {
    for (const basePath of this.commonPaths) {
      try {
        await this.searchInPath(basePath, patterns, gameType);
      } catch (error) {
        // Silently continue if path doesn't exist or can't be accessed
        continue;
      }
    }
  }

  async searchInPath(basePath, patterns, gameType) {
    try {
      const exists = await this.pathExists(basePath);
      if (!exists) return;

      const entries = await fs.readdir(basePath, { withFileTypes: true });
      
      for (const entry of entries) {
        if (entry.isDirectory()) {
          const fullPath = path.join(basePath, entry.name);
          const dirName = entry.name.toLowerCase();
          
          // Check if this directory matches any of our patterns
          for (const pattern of patterns) {
            if (dirName.includes(pattern.folder.toLowerCase()) || 
                dirName.includes('warcraft') ||
                (gameType === 'warcraft1' && (dirName.includes('war1') || dirName.includes('orc'))) ||
                (gameType === 'warcraft2' && (dirName.includes('war2') || dirName.includes('tide'))) ||
                (gameType === 'warcraft3' && (dirName.includes('war3') || dirName.includes('frozen'))) ||
                (gameType === 'w3champions' && dirName.includes('w3c')) ||
                (gameType === 'battlenet' && (dirName.includes('battle') || dirName.includes('blizzard')))) {
              await this.checkForExecutables(fullPath, pattern, gameType);
            }
          }
          
          // Also search one level deeper for common game installers
          if (dirName.includes('warcraft') || 
              dirName.includes('battle.net') ||
              dirName.includes('blizzard') ||
              dirName.includes('w3champions') ||
              dirName.includes('war1') ||
              dirName.includes('war2') ||
              dirName.includes('war3') ||
              dirName.includes('orc') ||
              dirName.includes('tide') ||
              dirName.includes('frozen') ||
              dirName.includes('reforged') ||
              dirName.includes('gog') ||
              dirName.includes('games')) {
            try {
              await this.searchInPath(fullPath, patterns, gameType);
            } catch (error) {
              // Continue on error
            }
          }
        }
      }
    } catch (error) {
      // Path doesn't exist or can't be accessed
      return;
    }
  }

  async checkForExecutables(gamePath, pattern, gameType) {
    try {
      const executablePath = path.join(gamePath, pattern.executable);
      const exists = await this.pathExists(executablePath);
      
      if (exists) {
        // Improve naming based on actual directory path for better accuracy
        let gameName = pattern.name;
        const lowerPath = gamePath.toLowerCase();
        
        if (gameType === 'warcraft2') {
          if (lowerPath.includes('war2combat')) {
            gameName = 'Warcraft 2 Combat Edition';
          } else if (lowerPath.includes('remastered')) {
            gameName = 'Warcraft II: Remastered';
          } else if (lowerPath.includes('gog games') || lowerPath.includes('gog\\warcraft') || lowerPath.includes('gog galaxy')) {
            // Only call it GOG if it's explicitly in a GOG directory
            gameName = 'Warcraft II (GOG)';
          } else if (lowerPath.includes('battlenet edition') || lowerPath.includes('bne') || pattern.executable.includes('BNE')) {
            gameName = 'Warcraft II: Battle.net Edition';
          } else if (pattern.executable.toLowerCase().includes('dosbox') || pattern.executable.toLowerCase().endsWith('.bat')) {
            // Handle DOS versions properly
            if (lowerPath.includes('warcraft ii') || lowerPath.includes('warcraft 2')) {
              gameName = 'Warcraft II: Tides of Darkness (DOS)';
            } else if (lowerPath.includes('war2x') || lowerPath.includes('dark portal')) {
              gameName = 'Warcraft II: Beyond the Dark Portal (DOS)';
            } else {
              gameName = 'Warcraft II (DOS)';
            }
          }
        } else if (gameType === 'warcraft1') {
          if (lowerPath.includes('remastered')) {
            gameName = 'Warcraft: Orcs & Humans (Remastered)';
          } else if (lowerPath.includes('gog games') || lowerPath.includes('gog\\warcraft') || lowerPath.includes('gog galaxy')) {
            gameName = 'Warcraft: Orcs & Humans (GOG)';
          } else if (pattern.executable.toLowerCase().includes('dosbox') || pattern.executable.toLowerCase().endsWith('.bat')) {
            gameName = 'Warcraft: Orcs & Humans (DOS)';
          } else {
            gameName = 'Warcraft: Orcs & Humans';
          }
        }
        
        const game = {
          name: gameName,
          type: gameType,
          path: executablePath,
          directory: gamePath,
          executable: pattern.executable,
          id: `${gameType}_${this.detectedGames.length}`,
          lastDetected: new Date().toISOString()
        };
        
        // Avoid duplicates - check by normalized paths
        const normalizedPath = path.normalize(game.path).toLowerCase();
        const isDuplicate = this.detectedGames.some(g => 
          path.normalize(g.path).toLowerCase() === normalizedPath
        );
        
        if (!isDuplicate) {
          this.detectedGames.push(game);
          console.log(`Found ${gameName} at: ${executablePath}`);
        } else {
          console.log(`Skipping duplicate: ${gameName} at: ${executablePath}`);
        }
      }
    } catch (error) {
      // Continue on error
    }
  }

  async pathExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async launchGame(gameId, updateCallback = null) {
    console.log(`Attempting to launch game with ID: ${gameId}`);
    
    // Find game in both detected and manual games
    const allGames = [...this.detectedGames, ...this.manualGames];
    const game = allGames.find(g => g.id === gameId);
    
    if (!game) {
      throw new Error(`Game with ID ${gameId} not found`);
    }
    
    console.log(`Launching ${game.name} from: ${game.path}`);
    
    try {
      // Check if this is a remastered game that should use Battle.net
      const battlenetProtocol = this.getBattleNetProtocol(game);
      
      if (battlenetProtocol) {
        console.log(`🚀 Launching ${game.name} via Battle.net protocol: ${battlenetProtocol}`);
        const { shell } = require('electron');
        
        try {
          console.log(`🚀 Launching Battle.net game: ${game.name}`);
          
          // Step 1: Always open and focus Battle.net launcher first
          await this.openAndFocusBattleNet();
          
          // Step 2: Try the game-specific Battle.net protocol
          const alternativeProtocols = this.getAlternativeBattleNetProtocols(game);
          let protocolWorked = false;
          
          for (const protocol of [battlenetProtocol, ...alternativeProtocols]) {
            try {
              console.log(`🔄 Trying Battle.net protocol: ${protocol}`);
              await shell.openExternal(protocol);
              console.log(`✅ Battle.net protocol launched successfully: ${protocol}`);
              protocolWorked = true;
              break;
            } catch (err) {
              console.log(`❌ Protocol ${protocol} failed: ${err.message}`);
              continue;
            }
          }
          
          // Step 3: Wait and update status
          setTimeout(async () => {
            await this.checkRunningGames();
            console.log(`Updated running status after launching ${game.name} via Battle.net`);
            
            if (updateCallback && typeof updateCallback === 'function') {
              try {
                await updateCallback();
              } catch (error) {
                console.error('Error in launch update callback:', error);
              }
            }
          }, 5000);
          
          if (protocolWorked) {
            return { success: true, message: `${game.name} launched via Battle.net (launcher opened and focused)` };
          } else {
            return { success: true, message: `Battle.net launcher opened - please navigate to ${game.name} manually` };
          }
          
        } catch (error) {
          console.error(`❌ Battle.net launch failed: ${error.message}`);
          console.log(`🔄 Falling back to direct executable launch for ${game.name}`);
          // Continue to direct launch fallback below
        }
      }
      
      // For non-Battle.net games, check if executable exists
      const pathExists = await this.pathExists(game.path);
      if (!pathExists) {
        throw new Error(`Game executable not found: ${game.path}`);
      }
      
      const gameDir = path.dirname(game.path);
      const fileName = path.basename(game.path);
      const fileExt = path.extname(game.path).toLowerCase();
      
      // Handle different game types
      if (game.args && game.args.length > 0) {
        // DOSBox or games with specific arguments
        console.log(`Launching ${fileName} with arguments: ${game.args.join(' ')}`);
        spawn(game.path, game.args, {
          cwd: game.gameDir || gameDir,
          detached: true,
          stdio: 'ignore'
        }).unref();
      } else if (fileExt === '.bat' || fileExt === '.cmd') {
        // For batch files, use cmd.exe to properly handle spaces and paths
        console.log(`Launching batch file: ${fileName}`);
        spawn('cmd.exe', ['/c', `"${game.path}"`], {
          cwd: gameDir,
          detached: true,
          stdio: 'ignore',
          shell: false
        }).unref();
      } else {
        // For regular executables
        spawn(game.path, [], {
          cwd: gameDir,
          detached: true,
          stdio: 'ignore'
        }).unref();
      }
      
      // Wait a moment for the process to start, then check running status
      setTimeout(async () => {
        await this.checkRunningGames();
        console.log(`Updated running status after launching ${game.name}`);
        
        // Call the update callback if provided (for tray menu refresh)
        if (updateCallback && typeof updateCallback === 'function') {
          try {
            await updateCallback();
          } catch (error) {
            console.error('Error in launch update callback:', error);
          }
        }
      }, 3000); // Increased timeout for DOSBox to start
      
      return { success: true, message: `${game.name} launched successfully` };
      
    } catch (error) {
      console.error(`Failed to launch ${game.name}:`, error);
      throw new Error(`Failed to launch ${game.name}: ${error.message}`);
    }
  }

  async testBattleNetProtocol(protocol) {
    // Test if a Battle.net protocol works
    try {
      const { shell } = require('electron');
      await shell.openExternal(protocol);
      return true;
    } catch (error) {
      console.error(`Protocol test failed for ${protocol}:`, error.message);
      return false;
    }
  }

  getAlternativeBattleNetProtocols(game) {
    // Return alternative Battle.net protocols to try if the primary one fails
    const gameName = game.name.toLowerCase();
    const alternatives = [];
    
    // Check in order: III, II, then I (to avoid substring conflicts)
    if (gameName.includes('warcraft iii') || gameName.includes('warcraft 3') || gameName.includes('reforged')) {
      alternatives.push(
        'battlenet://WC3',     // Alternative protocol
        'battlenet://reforged', // Reforged specific
        'battlenet://warcraft3' // Descriptive variant
      );
    } else if (gameName.includes('warcraft ii') || gameName.includes('warcraft 2')) {
      alternatives.push(
        'battlenet://WC2',     // Alternative protocol
        'battlenet://W2',      // Short variant
        'battlenet://warcraft2' // Descriptive variant
      );
    } else if (gameName.includes('warcraft: orcs & humans') || 
               (gameName.includes('warcraft') && (gameName.includes(' i ') || gameName.includes('warcraft i remastered') || gameName.includes('warcraft 1')))) {
      alternatives.push(
        'battlenet://WC1',     // Alternative protocol
        'battlenet://WCRFT',   // Another possible variant
        'battlenet://warcraft1' // Descriptive variant
      );
    }
    
    return alternatives;
  }

  async openAndFocusBattleNet() {
    console.log(`🔍 Opening and focusing Battle.net launcher...`);
    
    try {
      // Find Battle.net launcher from detected games
      const battleNetGame = this.detectedGames.find(g => g.type === 'battlenet');
      
      if (battleNetGame) {
        console.log(`📍 Found Battle.net launcher at: ${battleNetGame.path}`);
        
        // Launch Battle.net launcher
        const gameDir = path.dirname(battleNetGame.path);
        spawn(battleNetGame.path, [], {
          cwd: gameDir,
          detached: true,
          stdio: 'ignore'
        }).unref();
        
        console.log(`🚀 Battle.net launcher started`);
        
        // Wait a moment for it to start, then focus it
        setTimeout(async () => {
          await this.focusBattleNetWindow();
        }, 2000);
        
      } else {
        console.log(`❌ Battle.net launcher not found in detected games`);
        
        // Try common Battle.net paths as fallback
        const commonBattleNetPaths = [
          'D:\\Battle.net\\Battle.net Launcher.exe',
          'C:\\Program Files (x86)\\Battle.net\\Battle.net Launcher.exe',
          'C:\\Program Files\\Battle.net\\Battle.net Launcher.exe'
        ];
        
        for (const battleNetPath of commonBattleNetPaths) {
          try {
            const exists = await this.pathExists(battleNetPath);
            if (exists) {
              console.log(`📍 Found Battle.net at common path: ${battleNetPath}`);
              const gameDir = path.dirname(battleNetPath);
              spawn(battleNetPath, [], {
                cwd: gameDir,
                detached: true,
                stdio: 'ignore'
              }).unref();
              
              console.log(`🚀 Battle.net launcher started from common path`);
              
              setTimeout(async () => {
                await this.focusBattleNetWindow();
              }, 2000);
              
              return;
            }
          } catch (error) {
            continue;
          }
        }
        
        console.log(`❌ Could not find Battle.net launcher executable`);
      }
      
    } catch (error) {
      console.error(`❌ Failed to open Battle.net launcher: ${error.message}`);
    }
  }

  async focusBattleNetWindow() {
    console.log(`🎯 Attempting to focus Battle.net window...`);
    
    try {
      // Use PowerShell to find and focus the Battle.net window
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);
      
      // PowerShell script to find and focus Battle.net window
      const powershellScript = `
        Add-Type -TypeDefinition '
          using System;
          using System.Runtime.InteropServices;
          public class Win32 {
            [DllImport("user32.dll")]
            public static extern bool SetForegroundWindow(IntPtr hWnd);
            [DllImport("user32.dll")]
            public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);
            [DllImport("user32.dll")]
            public static extern bool IsIconic(IntPtr hWnd);
          }
        ';
        
        $battleNetProcess = Get-Process | Where-Object { 
          $_.ProcessName -like "*Battle.net*" -or 
          $_.MainWindowTitle -like "*Battle.net*" 
        } | Select-Object -First 1;
        
        if ($battleNetProcess -and $battleNetProcess.MainWindowHandle -ne 0) {
          $hwnd = $battleNetProcess.MainWindowHandle;
          if ([Win32]::IsIconic($hwnd)) {
            [Win32]::ShowWindow($hwnd, 9); # SW_RESTORE
          }
          [Win32]::SetForegroundWindow($hwnd);
          [Win32]::ShowWindow($hwnd, 5); # SW_SHOW
          Write-Output "Battle.net window focused successfully";
        } else {
          Write-Output "Battle.net window not found or has no main window";
        }
      `;
      
             const result = await execAsync(`powershell -Command "${powershellScript.replace(/"/g, '`"')}"`);
       console.log(`✅ Window focus result: ${result.stdout.trim()}`);
       
     } catch (error) {
       console.error(`❌ Failed to focus Battle.net window: ${error.message}`);
       
       // Fallback: try using Windows' tasklist and simple focus commands
       try {
         console.log(`🔄 Trying fallback window focus method...`);
         const { exec } = require('child_process');
         const { promisify } = require('util');
         const execAsyncFallback = promisify(exec);
         
         // Simple approach: use Alt+Tab simulation to bring Battle.net forward
         await execAsyncFallback('powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\'%{TAB}\')"');
         console.log(`✅ Sent Alt+Tab to focus windows`);
        
      } catch (fallbackError) {
        console.error(`❌ Fallback focus method also failed: ${fallbackError.message}`);
      }
    }
  }

  getBattleNetProtocol(game) {
    // Determine if this game should use Battle.net protocol and return the URL
    const gameName = game.name.toLowerCase();
    const gamePath = game.path.toLowerCase();
    
    console.log(`🔍 Checking Battle.net protocol for: "${game.name}"`);
    console.log(`   Game path: ${game.path}`);
    
    // Check if this game is already marked as a Battle.net game
    if (gameName.includes('(via battle.net)')) {
      console.log(`   ✅ Game is marked as Battle.net game`);
      // Determine which Battle.net protocol to use based on the game name/path
      // Order matters: Check more specific matches first (III, II, then I)
      if (gameName.includes('warcraft iii') || gameName.includes('warcraft 3') || gameName.includes('reforged')) {
        console.log(`   🎯 Detected as Warcraft III -> battlenet://W3`);
        return 'battlenet://W3'; // Warcraft III: Reforged
      }
      if (gameName.includes('warcraft ii') || gameName.includes('warcraft 2')) {
        console.log(`   🎯 Detected as Warcraft II -> battlenet://W2BN`);
        return 'battlenet://W2BN'; // Warcraft II: Battle.net Edition Remastered
      }
      if (gameName.includes('warcraft: orcs & humans') || 
          (gameName.includes('warcraft') && (gameName.includes(' i ') || gameName.includes('warcraft i remastered') || gameName.includes('warcraft 1')))) {
        console.log(`   🎯 Detected as Warcraft I -> battlenet://WTCG`);
        return 'battlenet://WTCG'; // Warcraft: Orcs & Humans Remastered
      }
      console.log(`   ❌ Battle.net game but couldn't determine protocol`);
    }
    
    // Warcraft III: Reforged (check first to avoid conflicts)
    if (gameName.includes('warcraft iii') && (gameName.includes('reforged') || gameName.includes('remastered') || gameName.includes('(via battle.net)'))) {
      return 'battlenet://W3';
    }
    if (gameName.includes('warcraft 3') && (gameName.includes('reforged') || gameName.includes('remastered') || gameName.includes('(via battle.net)'))) {
      return 'battlenet://W3';
    }
    if (gamePath.includes('warcraft iii') && (gamePath.includes('reforged') || gamePath.includes('_retail_'))) {
      return 'battlenet://W3';
    }
    if (gamePath.includes('warcraft 3') && gamePath.includes('reforged')) {
      return 'battlenet://W3';
    }
    
    // Warcraft II Remastered (check second)
    if (gameName.includes('warcraft ii') && (gameName.includes('remastered') || gameName.includes('(via battle.net)'))) {
      return 'battlenet://W2BN';
    }
    if (gameName.includes('warcraft 2') && (gameName.includes('remastered') || gameName.includes('(via battle.net)'))) {
      return 'battlenet://W2BN';
    }
    if (gamePath.includes('warcraft ii remastered') || gamePath.includes('warcraft 2 remastered')) {
      return 'battlenet://W2BN';
    }
    
    // Warcraft I Remastered (check last to avoid conflicts)
    if (gameName.includes('warcraft: orcs & humans') && (gameName.includes('remastered') || gameName.includes('(via battle.net)'))) {
      return 'battlenet://WTCG';
    }
    if (gamePath.includes('warcraft i remastered') || gamePath.includes('warcraft 1 remastered')) {
      return 'battlenet://WTCG';
    }
    
    // Check for Battle.net installation paths (common Battle.net game structure)
    if (gamePath.includes('program files') && gamePath.includes('warcraft')) {
      // Check in order: III, II, then I (to avoid substring conflicts)
      if (gamePath.includes('warcraft iii') || gamePath.includes('warcraft 3')) {
        return 'battlenet://W3';
      }
      if (gamePath.includes('warcraft ii') || gamePath.includes('warcraft 2')) {
        return 'battlenet://W2BN';
      }
      if (gamePath.includes('warcraft i') || gamePath.includes('warcraft 1')) {
        return 'battlenet://WTCG';
      }
    }
    
    console.log(`   ❌ No Battle.net protocol detected - will launch directly`);
    return null; // Not a Battle.net game
  }

  async checkRunningGames() {
    console.log('Checking for running game processes...');
    
    // Get all games (detected + manual)
    const allGames = [...this.detectedGames, ...this.manualGames];
    
    try {
      // Use PowerShell to get running processes (more reliable than wmic)
      const { stdout } = await execAsync('powershell "Get-Process | Select-Object ProcessName, Path | ConvertTo-Json"');
      const processes = JSON.parse(stdout);
      
      // Create a set of running process names and paths for fast lookup
      const runningProcesses = new Set();
      const runningProcessPaths = new Set();
      
      processes.forEach(proc => {
        if (proc.ProcessName) {
          runningProcesses.add(proc.ProcessName.toLowerCase());
        }
        if (proc.Path) {
          runningProcessPaths.add(proc.Path.toLowerCase());
          runningProcesses.add(path.basename(proc.Path).toLowerCase());
        }
      });
      
      // Check each game (both detected and manual)
      let runningCount = 0;
      for (const game of allGames) {
        const executableName = path.basename(game.path).toLowerCase();
        const executableNameWithoutExt = executableName.replace('.exe', '');
        const gameDir = path.dirname(game.path).toLowerCase();
        
        let isRunning = false;
        
        // Direct process name match
        if (runningProcesses.has(executableName) || runningProcesses.has(executableNameWithoutExt)) {
          isRunning = true;
        }
        
        // Direct path match
        if (!isRunning && runningProcessPaths.has(game.path.toLowerCase())) {
          isRunning = true;
        }
        
        // Special handling for Battle.net launcher
        if (!isRunning && game.type === 'battlenet') {
          // Check if any Battle.net related process is running from the same directory
          const battlenetProcesses = ['battle.net.exe', 'battle.net launcher.exe', 'battlenet.exe'];
          
          for (const battlenetProcess of battlenetProcesses) {
            if (runningProcesses.has(battlenetProcess)) {
              // Check if it's from the same directory
              for (const runningPath of runningProcessPaths) {
                if (runningPath.includes(gameDir) && runningPath.includes('battle')) {
                  isRunning = true;
                  console.log(`🟢 ${game.name} detected as running via related Battle.net process`);
                  break;
                }
              }
              if (isRunning) break;
            }
          }
        }
        
        // Special handling for W3Champions
        if (!isRunning && game.type === 'w3champions') {
          if (runningProcesses.has('w3champions.exe') || runningProcesses.has('w3champions')) {
            isRunning = true;
          }
        }
        
        // Special handling for DOSBox games (Warcraft 1 batch files or DOSBox executables)
        if (!isRunning && game.type === 'warcraft1' && 
            (game.path.toLowerCase().endsWith('.bat') || game.executable.toLowerCase() === 'dosbox.exe')) {
          if (runningProcesses.has('dosbox.exe') || runningProcesses.has('dosbox')) {
            // Check if DOSBox is running from the game directory
            const gameParentDir = game.gameDir ? game.gameDir.toLowerCase() : path.dirname(game.path).toLowerCase();
            
            for (const runningPath of runningProcessPaths) {
              if (runningPath.includes('dosbox.exe') && runningPath.includes(gameParentDir)) {
                isRunning = true;
                console.log(`🟢 ${game.name} detected as running via DOSBox from correct directory`);
                break;
              }
            }
            
            // Fallback: if we can't match the path but DOSBox is running, assume it's this game
            if (!isRunning) {
              isRunning = true;
              console.log(`🟢 ${game.name} detected as running via DOSBox (fallback detection)`);
            }
          }
        }
        
        game.isRunning = isRunning;
        
        if (isRunning) {
          runningCount++;
          console.log(`🟢 ${game.name} is currently running (${executableName})`);
        }
      }
      
      console.log(`Process check complete: ${runningCount} games currently running`);
      
    } catch (error) {
      console.error('Failed to check running processes:', error.message);
      
      // Fallback: set all games as not running
      allGames.forEach(game => {
        game.isRunning = false;
      });
    }
  }

  async getDetectedGames() {
    // Always check running status before returning games
    await this.checkRunningGames();
    
    // Combine detected and manual games, then sort them
    const allGames = [...this.detectedGames, ...this.manualGames];
    return this.sortGames(allGames);
  }

  getRunningGames() {
    // Return only games that are currently running
    const allGames = [...this.detectedGames, ...this.manualGames];
    return allGames.filter(game => game.isRunning === true);
  }

  getGamesByType(type) {
    return this.detectedGames.filter(game => game.type === type);
  }

  // Registry search for Windows (more comprehensive)
  async searchWindowsRegistry() {
    if (process.platform !== 'win32') return;

    try {
      // Search for installed games in Windows registry
      const registryPaths = [
        'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall',
        'HKEY_LOCAL_MACHINE\\SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall',
        'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall'
      ];

      for (const regPath of registryPaths) {
        try {
          // Search for Warcraft games
          const { stdout: warcraftResults } = await execAsync(`reg query "${regPath}" /s /f "Warcraft" /d`);
          this.parseRegistryOutput(warcraftResults);
        } catch (error) {
          // Continue on registry errors
        }

        try {
          // Search for Battle.net
          const { stdout: battleNetResults } = await execAsync(`reg query "${regPath}" /s /f "Battle.net" /d`);
          this.parseRegistryOutput(battleNetResults);
        } catch (error) {
          // Continue on registry errors
        }

        try {
          // Search for W3Champions
          const { stdout: w3cResults } = await execAsync(`reg query "${regPath}" /s /f "W3Champions" /d`);
          this.parseRegistryOutput(w3cResults);
        } catch (error) {
          // Continue on registry errors
        }
      }
    } catch (error) {
      console.error('Registry search error:', error);
    }
  }

  parseRegistryOutput(output) {
    // Parse Windows registry output to find game installations
    const lines = output.split('\n');
    let currentKey = '';
    
    for (const line of lines) {
      if (line.startsWith('HKEY_')) {
        currentKey = line.trim();
      } else if (line.includes('InstallLocation') || line.includes('DisplayIcon')) {
        const match = line.match(/REG_SZ\s+(.+)/);
        if (match && match[1]) {
          const installPath = match[1].trim();
          if (installPath.toLowerCase().includes('warcraft') || 
              installPath.toLowerCase().includes('battle.net') ||
              installPath.toLowerCase().includes('w3champions') ||
              installPath.toLowerCase().includes('blizzard')) {
            // Add to potential game paths for verification
            this.verifyRegistryPath(installPath);
          }
        }
      }
    }
  }

  async verifyRegistryPath(installPath) {
    try {
      const exists = await this.pathExists(installPath);
      if (exists) {
        // Search this path for executables
        const allPatterns = [
          ...this.getWarcraft1Patterns(),
          ...this.getWarcraft2Patterns(),
          ...this.getWarcraft3Patterns(),
          ...this.getW3ChampionsPatterns(),
          ...this.getBattleNetPatterns()
        ];
        
        for (const pattern of allPatterns) {
          await this.checkForExecutables(installPath, pattern, pattern.type);
        }
      }
    } catch (error) {
      // Continue on error
    }
  }

  getWarcraft1Patterns() {
    return [
      { name: 'Warcraft: Orcs & Humans', executable: 'WAR.EXE', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans', executable: 'WARCRAFT.EXE', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (GOG)', executable: 'Warcraft.exe', folder: 'warcraft orcs and humans', type: 'warcraft1' },
      // Remastered versions - comprehensive patterns
      { name: 'Warcraft: Orcs & Humans (Remastered)', executable: 'Warcraft I & II Battle.net Edition.exe', folder: 'warcraft i & ii battle.net edition', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (Remastered)', executable: 'Warcraft Remastered.exe', folder: 'warcraft remastered', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (Remastered)', executable: 'Warcraft I- Orcs and Humans.exe', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (Remastered)', executable: 'Warcraft.exe', folder: 'warcraft i remastered', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (Remastered)', executable: 'Warcraft- Orcs and Humans.exe', folder: 'warcraft i remastered', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (Remastered)', executable: 'Warcraft.exe', folder: 'warcraft remastered', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (Remastered)', executable: 'Warcraft.exe', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans', executable: 'Warcraft- Orcs and Humans.exe', folder: 'warcraft', type: 'warcraft1' },
      // Enhanced DOS version detection - different configurations
      { name: 'Warcraft: Orcs & Humans (DOS)', executable: 'dosbox.exe', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (DOS)', executable: 'DOSBox.exe', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (DOS)', executable: 'Warcraft.bat', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (DOS)', executable: 'War1.bat', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (DOS)', executable: 'WAR.bat', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (DOS)', executable: 'WARCRAFT.bat', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (DOS)', executable: 'Play.bat', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (DOS)', executable: 'Start.bat', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (DOS)', executable: 'Launch.bat', folder: 'warcraft', type: 'warcraft1' },
      // DOS executables in modern Windows (ScummVM, etc.)
      { name: 'Warcraft: Orcs & Humans (DOS)', executable: 'scummvm.exe', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (DOS)', executable: 'WarcraftDOS.exe', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans (DOS)', executable: 'War1DOS.exe', folder: 'warcraft', type: 'warcraft1' },
      // Additional loose search patterns
      { name: 'Warcraft: Orcs & Humans', executable: 'war1.exe', folder: 'warcraft', type: 'warcraft1' },
      { name: 'Warcraft: Orcs & Humans', executable: 'warcraft1.exe', folder: 'warcraft', type: 'warcraft1' },
    ];
  }

  getWarcraft2Patterns() {
    return [
      { name: 'Warcraft II: Tides of Darkness', executable: 'WAR2.EXE', folder: 'warcraft ii', type: 'warcraft2' },
      { name: 'Warcraft II: Beyond the Dark Portal', executable: 'WAR2X.EXE', folder: 'warcraft ii', type: 'warcraft2' },
      { name: 'Warcraft II: Battle.net Edition', executable: 'WAR2BNE.EXE', folder: 'warcraft ii bne', type: 'warcraft2' },
      // Use BNE as default name unless explicitly detected as GOG
      { name: 'Warcraft II: Battle.net Edition', executable: 'Warcraft II BNE.exe', folder: 'warcraft 2 battlenet edition', type: 'warcraft2' },
      { name: 'Warcraft 2 Combat Edition', executable: 'Warcraft II BNE.exe', folder: 'war2combat', type: 'warcraft2' },
      { name: 'Warcraft II: Remastered', executable: 'Warcraft I & II Battle.net Edition.exe', folder: 'warcraft i & ii battle.net edition', type: 'warcraft2' },
      { name: 'Warcraft II: Remastered', executable: 'Warcraft II Remastered.exe', folder: 'warcraft ii remastered', type: 'warcraft2' },
      { name: 'Warcraft II: Remastered', executable: 'Warcraft II- Tides of Darkness.exe', folder: 'warcraft', type: 'warcraft2' },
      // Enhanced DOS version detection
      { name: 'Warcraft II: Tides of Darkness (DOS)', executable: 'dosbox.exe', folder: 'warcraft ii', type: 'warcraft2' },
      { name: 'Warcraft II: Tides of Darkness (DOS)', executable: 'War2.bat', folder: 'warcraft ii', type: 'warcraft2' },
      { name: 'Warcraft II: Tides of Darkness (DOS)', executable: 'Warcraft2.bat', folder: 'warcraft ii', type: 'warcraft2' },
      { name: 'Warcraft II: Tides of Darkness (DOS)', executable: 'Warcraft II.bat', folder: 'warcraft ii', type: 'warcraft2' },
      { name: 'Warcraft II: Tides of Darkness (DOS)', executable: 'WAR2.bat', folder: 'warcraft ii', type: 'warcraft2' },
      { name: 'Warcraft II: Beyond the Dark Portal (DOS)', executable: 'War2X.bat', folder: 'warcraft ii', type: 'warcraft2' },
      { name: 'Warcraft II: Beyond the Dark Portal (DOS)', executable: 'WAR2X.bat', folder: 'warcraft ii', type: 'warcraft2' },
      // Additional search patterns
      { name: 'Warcraft II', executable: 'war2.exe', folder: 'warcraft', type: 'warcraft2' },
      { name: 'Warcraft II', executable: 'WarcraftII.exe', folder: 'warcraft', type: 'warcraft2' },
      { name: 'Warcraft II', executable: 'warcraft2.exe', folder: 'warcraft ii', type: 'warcraft2' },
    ];
  }

  getWarcraft3Patterns() {
    return [
      { name: 'Warcraft III: Reign of Chaos', executable: 'war3.exe', folder: 'warcraft iii', type: 'warcraft3' },
      { name: 'Warcraft III: The Frozen Throne', executable: 'Frozen Throne.exe', folder: 'warcraft iii', type: 'warcraft3' },
      { name: 'Warcraft III: Reforged', executable: 'Warcraft III.exe', folder: 'warcraft iii', type: 'warcraft3' },
      { name: 'Warcraft III: Reforged', executable: 'Warcraft III.exe', folder: '_retail_', type: 'warcraft3' },
      { name: 'Warcraft III: Reforged', executable: 'Warcraft III.exe', folder: 'x86_64', type: 'warcraft3' },
      { name: 'Warcraft III: The Frozen Throne', executable: 'war3x.exe', folder: 'warcraft iii', type: 'warcraft3' },
      { name: 'Warcraft III', executable: 'warcraft3.exe', folder: 'warcraft', type: 'warcraft3' },
      { name: 'Warcraft III', executable: 'WarcraftIII.exe', folder: 'warcraft', type: 'warcraft3' },
      { name: 'Warcraft III', executable: 'Warcraft 3.exe', folder: 'warcraft', type: 'warcraft3' },
    ];
  }

  getW3ChampionsPatterns() {
    return [
      { name: 'W3Champions', executable: 'W3Champions.exe', folder: 'w3champions', type: 'w3champions' },
      { name: 'W3Champions', executable: 'W3Champions.App.exe', folder: 'w3champions', type: 'w3champions' },
      { name: 'W3Champions', executable: 'W3Champions Launcher.exe', folder: 'w3champions', type: 'w3champions' },
      { name: 'W3Champions', executable: 'W3ChampionsLauncher.exe', folder: 'w3champions', type: 'w3champions' },
    ];
  }

  getBattleNetPatterns() {
    return [
      { name: 'Battle.net', executable: 'Battle.net.exe', folder: 'battle.net', type: 'battlenet' },
      { name: 'Battle.net Launcher', executable: 'Battle.net Launcher.exe', folder: 'battle.net', type: 'battlenet' },
    ];
  }

  startPeriodicStatusCheck(updateCallback = null) {
    // Check running status every 10 seconds
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
    }
    
    this.statusCheckInterval = setInterval(async () => {
      if (this.detectedGames.length > 0) {
        await this.checkRunningGames();
        
        // Call the update callback if provided (for tray menu refresh)
        if (updateCallback && typeof updateCallback === 'function') {
          try {
            await updateCallback();
          } catch (error) {
            console.error('Error in periodic status update callback:', error);
          }
        }
      }
    }, 10000); // Check every 10 seconds
    
    console.log('Started periodic running status checks (every 10 seconds)');
  }
  
  stopPeriodicStatusCheck() {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
      this.statusCheckInterval = null;
      console.log('Stopped periodic running status checks');
    }
  }

  loadManualGames() {
    try {
      this.manualGames = this.store.get('manualGames', []);
      console.log(`Loaded ${this.manualGames.length} manually added games`);
    } catch (error) {
      console.error('Failed to load manual games:', error);
      this.manualGames = [];
    }
  }
  
  saveManualGames() {
    try {
      this.store.set('manualGames', this.manualGames);
      console.log(`Saved ${this.manualGames.length} manually added games`);
    } catch (error) {
      console.error('Failed to save manual games:', error);
    }
  }
  
  async addManualGame(gameData) {
    // Validate game data
    if (!gameData.name || !gameData.path || !gameData.type) {
      throw new Error('Game name, path, and type are required');
    }
    
    // Check if path exists
    const pathExists = await this.pathExists(gameData.path);
    if (!pathExists) {
      throw new Error(`Game executable not found: ${gameData.path}`);
    }
    
    // Create game object
    const game = {
      id: this.generateGameId(),
      name: gameData.name,
      type: gameData.type,
      path: gameData.path,
      args: gameData.args || [],
      gameDir: gameData.gameDir || path.dirname(gameData.path),
      configPath: gameData.configPath || null,
      isRunning: false,
      executable: path.basename(gameData.path),
      isManual: true
    };
    
    // Check for duplicates
    const isDuplicate = this.manualGames.some(existingGame => 
      existingGame.path.toLowerCase() === game.path.toLowerCase()
    );
    
    if (isDuplicate) {
      throw new Error('A game with this path is already added');
    }
    
    this.manualGames.push(game);
    this.saveManualGames();
    
    console.log(`Added manual game: ${game.name}`);
    return game;
  }
  
  removeManualGame(gameId) {
    const gameIndex = this.manualGames.findIndex(game => game.id === gameId);
    if (gameIndex === -1) {
      throw new Error('Manual game not found');
    }
    
    const removedGame = this.manualGames.splice(gameIndex, 1)[0];
    this.saveManualGames();
    
    console.log(`Removed manual game: ${removedGame.name}`);
    return removedGame;
  }
  
  updateManualGame(gameId, gameData) {
    const game = this.manualGames.find(g => g.id === gameId);
    if (!game) {
      throw new Error('Manual game not found');
    }
    
    // Update properties
    if (gameData.name) game.name = gameData.name;
    if (gameData.type) game.type = gameData.type;
    if (gameData.path) {
      game.path = gameData.path;
      game.executable = path.basename(gameData.path);
      game.gameDir = gameData.gameDir || path.dirname(gameData.path);
    }
    if (gameData.args !== undefined) game.args = gameData.args;
    if (gameData.configPath !== undefined) game.configPath = gameData.configPath;
    
    this.saveManualGames();
    
    console.log(`Updated manual game: ${game.name}`);
    return game;
  }
  
  getAllGames() {
    // Combine detected games and manual games
    return [...this.detectedGames, ...this.manualGames];
  }

  async openMapsFolder(gameId) {
    console.log('📁 Opening maps folder for game:', gameId);
    
    try {
      const allGames = this.getAllGames();
      const game = allGames.find(g => g.id === gameId || g.id === parseInt(gameId));
      
      if (!game) {
        throw new Error(`Game with ID ${gameId} not found`);
      }
      
      let mapsFolder = null;
      
      // Determine maps folder based on game type and path
      if (game.type === 'warcraft3') {
        // Warcraft 3 maps folder
        const gameDir = path.dirname(game.path);
        mapsFolder = path.join(gameDir, 'Maps');
        
        // Check if Maps folder exists, if not try common alternatives
        if (!(await this.pathExists(mapsFolder))) {
          const alternatives = [
            path.join(require('os').homedir(), 'Documents', 'Warcraft III', 'Maps'),
            path.join(require('os').homedir(), 'Documents', 'Warcraft III Reforged', 'Maps'),
            path.join(gameDir, 'maps'),
            path.join(gameDir, '..', 'Maps')
          ];
          
          for (const alt of alternatives) {
            if (await this.pathExists(alt)) {
              mapsFolder = alt;
              break;
            }
          }
        }
      } else if (game.type === 'warcraft2') {
        // Warcraft 2 maps folder  
        const gameDir = path.dirname(game.path);
        mapsFolder = path.join(gameDir, 'Maps');
        
        if (!(await this.pathExists(mapsFolder))) {
          const alternatives = [
            path.join(gameDir, 'maps'),
            path.join(gameDir, 'DATA', 'MAPS'),
            path.join(gameDir, 'data', 'maps')
          ];
          
          for (const alt of alternatives) {
            if (await this.pathExists(alt)) {
              mapsFolder = alt;
              break;
            }
          }
        }
      } else if (game.type === 'warcraft1') {
        // Warcraft 1 maps folder
        const gameDir = game.gameDir || path.dirname(game.path);
        mapsFolder = path.join(gameDir, 'DATA');
        
        if (!(await this.pathExists(mapsFolder))) {
          const alternatives = [
            path.join(gameDir, 'data'),
            path.join(gameDir, 'MAPS'),
            path.join(gameDir, 'maps')
          ];
          
          for (const alt of alternatives) {
            if (await this.pathExists(alt)) {
              mapsFolder = alt;
              break;
            }
          }
        }
      }
      
      if (!mapsFolder || !(await this.pathExists(mapsFolder))) {
        // Create a maps folder if it doesn't exist
        const gameDir = path.dirname(game.path);
        mapsFolder = path.join(gameDir, 'Maps');
        await fs.mkdir(mapsFolder, { recursive: true });
        console.log('📁 Created maps folder:', mapsFolder);
      }
      
      // Open the folder in file explorer
      const { shell } = require('electron');
      await shell.openPath(mapsFolder);
      
      console.log('✅ Opened maps folder:', mapsFolder);
      return { success: true, path: mapsFolder };
      
    } catch (error) {
      console.error('❌ Error opening maps folder:', error);
      throw error;
    }
  }
}
  
module.exports = { GameDetector }; 