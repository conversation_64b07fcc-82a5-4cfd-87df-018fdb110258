/* ==========================================
   FORUM BUTTONS - CLEAN & MODERN STYLING
   ========================================== */

/* Reset and base styles for all forum buttons */
.forum-feed button,
.forum-feed .btn,
.composer-actions button,
.composer-actions .btn,
.create-post-button,
.cancel-post-button,
.post-button,
#delete-post-modal .btn,
#delete-reply-modal .btn,
.modal.delete-modal .btn {
  /* Reset conflicting styles */
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  text-decoration: none;
  cursor: pointer;
  outline: none;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* ==========================================
   CREATE POST BUTTON - MAIN CTA
   ========================================== */
.create-post-button {
  /* Base Layout */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  min-height: 72px;
  padding: 20px 32px;
  margin-bottom: 24px;
  
  /* Typography */
  font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  font-size: 1.1rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: none;
  
  /* Visual Design */
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 50%, #FFD700 100%);
  color: #1a1a1a;
  border-radius: 16px;
  
  /* Shadows & Effects */
  box-shadow: 
    0 8px 24px rgba(255, 107, 53, 0.25),
    0 4px 12px rgba(247, 147, 30, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  
  /* Transitions */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Interaction */
  cursor: pointer;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.create-post-button:hover {
  background: linear-gradient(135deg, #FF8A65 0%, #FFB74D 50%, #FFF176 100%);
  transform: translateY(-2px);
  box-shadow: 
    0 12px 32px rgba(255, 107, 53, 0.3),
    0 6px 16px rgba(247, 147, 30, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.create-post-button:active {
  transform: translateY(0);
  box-shadow: 
    0 6px 16px rgba(255, 107, 53, 0.2),
    0 3px 8px rgba(247, 147, 30, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.create-post-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.create-post-button i {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* ==========================================
   CANCEL POST BUTTON - SECONDARY ACTION
   ========================================== */
.cancel-post-button {
  /* Base Layout */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  min-height: 44px;
  
  /* Typography */
  font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  font-size: 0.95rem;
  font-weight: 600;
  letter-spacing: 0.25px;
  
  /* Visual Design */
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  
  /* Shadows & Effects */
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  
  /* Transitions */
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Interaction */
  cursor: pointer;
  user-select: none;
}

.cancel-post-button:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #ced4da 100%);
  color: #212529;
  border-color: #adb5bd;
  transform: translateY(-1px);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.cancel-post-button:active {
  transform: translateY(0);
  box-shadow: 
    0 2px 6px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

.cancel-post-button i {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* ==========================================
   POST/SUBMIT BUTTON - PRIMARY ACTION
   ========================================== */
.post-button,
.composer-actions .btn-primary {
  /* Base Layout */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  min-height: 44px;
  
  /* Typography */
  font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  font-size: 0.95rem;
  font-weight: 600;
  letter-spacing: 0.25px;
  
  /* Visual Design */
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border-radius: 12px;
  
  /* Shadows & Effects */
  box-shadow: 
    0 4px 12px rgba(40, 167, 69, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  
  /* Transitions */
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Interaction */
  cursor: pointer;
  user-select: none;
}

.post-button:hover,
.composer-actions .btn-primary:hover {
  background: linear-gradient(135deg, #218838 0%, #1dd1a1 100%);
  transform: translateY(-1px);
  box-shadow: 
    0 6px 16px rgba(40, 167, 69, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.post-button:active,
.composer-actions .btn-primary:active {
  transform: translateY(0);
  box-shadow: 
    0 3px 8px rgba(40, 167, 69, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.post-button:disabled,
.composer-actions .btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* ==========================================
   MODAL BUTTONS - DELETE CONFIRMATIONS
   ========================================== */

/* Danger/Delete buttons */
#delete-post-modal .btn-danger,
#delete-reply-modal .btn-danger,
.modal.delete-modal .btn-danger {
  /* Base Layout */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 28px;
  min-height: 48px;
  min-width: 120px;
  
  /* Typography */
  font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  font-size: 0.95rem;
  font-weight: 600;
  letter-spacing: 0.25px;
  
  /* Visual Design */
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  border-radius: 12px;
  
  /* Shadows & Effects */
  box-shadow: 
    0 4px 12px rgba(220, 53, 69, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  
  /* Transitions */
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Interaction */
  cursor: pointer;
  user-select: none;
}

#delete-post-modal .btn-danger:hover,
#delete-reply-modal .btn-danger:hover,
.modal.delete-modal .btn-danger:hover {
  background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
  transform: translateY(-1px);
  box-shadow: 
    0 6px 16px rgba(220, 53, 69, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

#delete-post-modal .btn-danger:active,
#delete-reply-modal .btn-danger:active,
.modal.delete-modal .btn-danger:active {
  transform: translateY(0);
  box-shadow: 
    0 3px 8px rgba(220, 53, 69, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Secondary/Cancel buttons in modals */
#delete-post-modal .btn-secondary,
#delete-reply-modal .btn-secondary,
.modal.delete-modal .btn-secondary {
  /* Base Layout */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 28px;
  min-height: 48px;
  min-width: 120px;
  
  /* Typography */
  font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  font-size: 0.95rem;
  font-weight: 600;
  letter-spacing: 0.25px;
  
  /* Visual Design */
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  border-radius: 12px;
  
  /* Shadows & Effects */
  box-shadow: 
    0 4px 12px rgba(108, 117, 125, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  
  /* Transitions */
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Interaction */
  cursor: pointer;
  user-select: none;
}

#delete-post-modal .btn-secondary:hover,
#delete-reply-modal .btn-secondary:hover,
.modal.delete-modal .btn-secondary:hover {
  background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
  transform: translateY(-1px);
  box-shadow: 
    0 6px 16px rgba(108, 117, 125, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

#delete-post-modal .btn-secondary:active,
#delete-reply-modal .btn-secondary:active,
.modal.delete-modal .btn-secondary:active {
  transform: translateY(0);
  box-shadow: 
    0 3px 8px rgba(108, 117, 125, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* ==========================================
   RESPONSIVE DESIGN
   ========================================== */
@media (max-width: 768px) {
  .create-post-button {
    padding: 16px 24px;
    min-height: 64px;
    font-size: 1rem;
  }
  
  .composer-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .composer-actions button {
    width: 100%;
  }
  
  #delete-post-modal .btn,
  #delete-reply-modal .btn,
  .modal.delete-modal .btn {
    padding: 12px 20px;
    min-height: 44px;
    font-size: 0.9rem;
  }
}

/* ==========================================
   FOCUS STATES FOR ACCESSIBILITY
   ========================================== */
.create-post-button:focus-visible,
.cancel-post-button:focus-visible,
.post-button:focus-visible,
.composer-actions .btn:focus-visible,
#delete-post-modal .btn:focus-visible,
#delete-reply-modal .btn:focus-visible,
.modal.delete-modal .btn:focus-visible {
  outline: 2px solid #4A90E2;
  outline-offset: 2px;
}

/* ==========================================
   ANIMATION KEYFRAMES
   ========================================== */
@keyframes buttonPressDown {
  0% { transform: translateY(0); }
  50% { transform: translateY(1px); }
  100% { transform: translateY(0); }
}

@keyframes buttonPressUp {
  0% { transform: translateY(0); }
  50% { transform: translateY(-1px); }
  100% { transform: translateY(0); }
}

/* Apply press animations */
.create-post-button:active,
.cancel-post-button:active,
.post-button:active,
.composer-actions .btn:active,
#delete-post-modal .btn:active,
#delete-reply-modal .btn:active,
.modal.delete-modal .btn:active {
  animation: buttonPressDown 0.1s ease-out;
} 