@echo off
echo ========================================
echo WC Arena Core - Electron with Proxy
echo ========================================
echo.

echo [1/3] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo [2/3] Starting proxy server on port 3001...
start "Electron Proxy Server" cmd /k "node electron-proxy.js"

echo.
echo [3/3] Waiting 3 seconds for proxy to start...
timeout /t 3 /nobreak >nul

echo.
echo [4/4] Starting Electron app...
start "WC Arena Core" cmd /k "npm start"

echo.
echo ✅ Both services started!
echo 📡 Proxy Server: http://127.0.0.1:3001
echo 🖥️  Electron App: WC Arena Core
echo.
echo Press any key to stop all services...
pause >nul

echo.
echo 🛑 Stopping all services...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im electron.exe >nul 2>&1
echo ✅ All services stopped. 