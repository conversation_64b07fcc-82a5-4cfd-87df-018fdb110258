// Campaigns page functionality
class CampaignsManager {
  constructor() {
    this.campaigns = [];
    this.userProgress = [];
    this.userStats = {};
    this.selectedFiles = [];
    this.currentCampaignId = null;
    this.selectedGame = 'war2'; // Default to WC2

    this.init();
  }

  async init() {
    // Wait for navbar to be loaded by main.js
    await this.waitForNavbar();
    await this.loadData();
    this.setupEventListeners();
    this.setupGameTabs();
    this.renderCampaigns();
  }

  async waitForNavbar() {
    // Wait for navbar to be loaded by main.js
    return new Promise((resolve) => {
      const checkNavbar = () => {
        if (document.getElementById('navbar-container').innerHTML.trim() !== '') {
          resolve();
        } else {
          setTimeout(checkNavbar, 100);
        }
      };
      checkNavbar();
    });
  }

  async loadData() {
    try {
      console.log('Loading campaign data...');
      
      // Load campaigns, user progress, and stats in parallel
      const [campaignsResponse, progressResponse, statsResponse] = await Promise.all([
        fetch('/api/campaigns'),
        fetch('/api/campaigns/user/progress'),
        fetch('/api/campaigns/user/stats')
      ]);

      console.log('Campaign response status:', campaignsResponse.status);
      console.log('Progress response status:', progressResponse.status);
      console.log('Stats response status:', statsResponse.status);

      if (campaignsResponse.ok) {
        this.campaigns = await campaignsResponse.json();
        console.log('Loaded campaigns:', this.campaigns.length, 'game groups');
      } else {
        console.error('Failed to load campaigns:', campaignsResponse.status, campaignsResponse.statusText);
      }

      if (progressResponse.ok) {
        this.userProgress = await progressResponse.json();
        console.log('Loaded user progress:', this.userProgress.length, 'entries');
      } else {
        console.error('Failed to load user progress:', progressResponse.status, progressResponse.statusText);
      }

      if (statsResponse.ok) {
        this.userStats = await statsResponse.json();
        console.log('Loaded user stats:', this.userStats);
      } else {
        console.error('Failed to load user stats:', statsResponse.status, statsResponse.statusText);
      }

      this.updateStats();
    } catch (error) {
      console.error('Error loading campaign data:', error);
      this.showError('Failed to load campaign data');
    }
  }

  updateStats() {
    // Calculate stats for the selected game only
    const gameStats = this.calculateGameStats(this.selectedGame);
    
    document.getElementById('missions-completed').textContent = gameStats.missionsCompleted || 0;
    document.getElementById('total-experience').textContent = gameStats.experienceEarned || 0;
    document.getElementById('arena-gold').textContent = gameStats.arenaGoldEarned || 0;
    document.getElementById('honor-points').textContent = gameStats.honorPointsEarned || 0;
    document.getElementById('achievements-unlocked').textContent = gameStats.achievementsUnlocked || 0;
  }

  /**
   * Calculate stats for a specific game
   */
  calculateGameStats(gameType) {
    const stats = {
      missionsCompleted: 0,
      experienceEarned: 0,
      arenaGoldEarned: 0,
      honorPointsEarned: 0,
      achievementsUnlocked: 0
    };

    // Map tab game values to database game values
    const gameMapping = {
      'war1': 'warcraft1',
      'war2': 'warcraft2', 
      'war3': 'warcraft3'
    };
    
    const dbGameType = gameMapping[gameType] || gameType;

    // Filter user progress by game type
    const gameProgress = this.userProgress.filter(progress => 
      progress._id.game === dbGameType
    );

    // Calculate totals
    gameProgress.forEach(progress => {
      stats.missionsCompleted += progress.completedMissions || 0;
      
      // Calculate experience and gold from missions
      if (progress.missions) {
        progress.missions.forEach(mission => {
          stats.experienceEarned += mission.experienceEarned || 0;
          stats.arenaGoldEarned += mission.arenaGoldEarned || 0;
          stats.honorPointsEarned += mission.honorPointsEarned || 0;
        });
      }
    });

    return stats;
  }

  /**
   * Setup game tab functionality
   */
  setupGameTabs() {
    console.log('Setting up game tabs...');
    const gameTabs = document.querySelectorAll('.campaigns-tab');
    
    gameTabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // Remove active class from all tabs
        gameTabs.forEach(t => t.classList.remove('active'));
        
        // Add active class to clicked tab
        tab.classList.add('active');
        
        // Update selected game
        this.selectedGame = tab.dataset.game;
        console.log('Selected game changed to:', this.selectedGame);
        
        // Update stats for the selected game
        this.updateStats();
        
        // Re-render campaigns for the selected game
        this.renderCampaigns();
      });
    });
  }

  renderCampaigns() {
    console.log('Rendering campaigns for game:', this.selectedGame);
    const container = document.getElementById('campaigns-content');
    
    console.log('Campaigns data:', this.campaigns);
    console.log('Campaigns length:', this.campaigns ? this.campaigns.length : 'undefined');
    
    if (!this.campaigns || this.campaigns.length === 0) {
      console.log('No campaigns to display');
      container.innerHTML = '<div class="error"><i class="fas fa-exclamation-triangle"></i><p>No campaigns available</p></div>';
      return;
    }

    // Group campaigns by game and filter by selected game
    const gameGroups = this.groupCampaignsByGame();
    console.log('Game groups:', gameGroups);
    
    // Map tab game values to database game values
    const gameMapping = {
      'war1': 'warcraft1',
      'war2': 'warcraft2', 
      'war3': 'warcraft3'
    };
    
    const dbGameType = gameMapping[this.selectedGame];
    console.log('Looking for database game type:', dbGameType);
    
    // Sort game groups for consistent order
    const sortedGameKeys = Object.keys(gameGroups)
      .filter(gameKey => gameKey.split('_')[0] === dbGameType)
      .sort((a, b) => {
        // Base games come before expansions
        const aExpansion = a.split('_')[1];
        const bExpansion = b.split('_')[1];
        
        // Define expansion order
        const expansionOrder = {
          'base': 1,
          'btdp': 2,  // Beyond the Dark Portal
          'roc': 1,   // Reign of Chaos (base WC3)
          'tft': 2    // The Frozen Throne
        };
        
        const aOrder = expansionOrder[aExpansion] || 999;
        const bOrder = expansionOrder[bExpansion] || 999;
        
        return aOrder - bOrder;
      });
    
    let html = '';
    for (const gameKey of sortedGameKeys) {
      const gameData = gameGroups[gameKey];
      console.log('✅ Match found! Rendering game section for:', gameKey);
      html += this.renderGameSection(gameKey, gameData);
    }

    // If no campaigns found for selected game, show message
    if (sortedGameKeys.length === 0 || html === '') {
      const gameNames = {
        'war1': 'Warcraft I',
        'war2': 'Warcraft II',
        'war3': 'Warcraft III'
      };
      console.log('No campaigns found for:', this.selectedGame);
      html = `<div class="error">
        <i class="fas fa-info-circle"></i>
        <p>No ${gameNames[this.selectedGame]} campaigns available yet</p>
        <p>Check back later for new content!</p>
      </div>`;
    }

    console.log('Generated HTML length:', html.length);
    container.innerHTML = html;
  }

  groupCampaignsByGame() {
    const groups = {};
    
    this.campaigns.forEach(gameGroup => {
      const gameKey = `${gameGroup._id.game}_${gameGroup._id.expansion}`;
      const gameTitle = this.getGameTitle(gameGroup._id.game, gameGroup._id.expansion);
      
      if (!groups[gameKey]) {
        groups[gameKey] = {
          title: gameTitle,
          icon: this.getGameIcon(gameGroup._id.game),
          campaigns: []
        };
      }
      
      gameGroup.campaigns.forEach(campaign => {
        // Calculate progress for this campaign
        const progress = this.calculateCampaignProgress(campaign, gameGroup._id.game, gameGroup._id.expansion);
        groups[gameKey].campaigns.push({
          ...campaign,
          _id: campaign._id || `${gameGroup._id.game}_${gameGroup._id.expansion}_${campaign.race}_${campaign.campaignName}`.replace(/\s+/g, '_'),
          game: gameGroup._id.game,
          expansion: gameGroup._id.expansion,
          progress
        });
      });

      // Sort campaigns within each game group for consistent order
      groups[gameKey].campaigns.sort((a, b) => {
        // Define race order priority (Human/Alliance first, then Orc/Horde)
        const raceOrder = {
          'human': 1,
          'alliance': 1,
          'orc': 2,
          'horde': 2,
          'undead': 3,
          'nightelf': 4,
          'night elf': 4
        };
        
        const aOrder = raceOrder[a.race.toLowerCase()] || 999;
        const bOrder = raceOrder[b.race.toLowerCase()] || 999;
        
        // If race order is the same, sort by campaign name
        if (aOrder === bOrder) {
          return a.campaignName.localeCompare(b.campaignName);
        }
        
        return aOrder - bOrder;
      });
    });

    return groups;
  }

  calculateCampaignProgress(campaign, game, expansion) {
    const userCampaignProgress = this.userProgress.find(p => 
      p._id.game === game && 
      p._id.expansion === expansion && 
      p._id.campaignName === campaign.campaignName &&
      p._id.race === campaign.race
    );

    const completed = userCampaignProgress ? userCampaignProgress.completedMissions : 0;
    const total = campaign.totalMissions;
    const percentage = total > 0 ? (completed / total) * 100 : 0;

    return {
      completed,
      total,
      percentage: Math.round(percentage),
      missions: campaign.missions.map(mission => {
        const isCompleted = userCampaignProgress ? 
          userCampaignProgress.missions.some(m => m.missionId.toString() === mission._id.toString()) : 
          false;
        
        return {
          ...mission,
          isCompleted
        };
      })
    };
  }

  renderGameSection(gameKey, gameData) {
    const totalCompleted = gameData.campaigns.reduce((sum, c) => sum + c.progress.completed, 0);
    const totalMissions = gameData.campaigns.reduce((sum, c) => sum + c.progress.total, 0);
    const overallProgress = totalMissions > 0 ? Math.round((totalCompleted / totalMissions) * 100) : 0;

    return `
      <div class="game-section" data-game="${gameKey}">
        <div class="game-header">
          <i class="${gameData.icon} game-icon"></i>
          <div class="game-title">${gameData.title}</div>
          <div class="game-progress">${totalCompleted}/${totalMissions} missions (${overallProgress}%)</div>
        </div>
        <div class="campaigns-grid">
          ${gameData.campaigns.map(campaign => this.renderCampaignCard(campaign)).join('')}
        </div>
      </div>
    `;
  }

  renderCampaignCard(campaign) {
    const raceClass = `race-${campaign.race.toLowerCase().replace(/\s+/g, '')}`;
    const raceIcon = this.getRaceIcon(campaign.race);
    
    return `
      <div class="campaign-card">
        <div class="campaign-header">
          <div class="race-icon ${raceClass}">
            <i class="${raceIcon}"></i>
          </div>
          <div>
            <h3 class="campaign-title">${campaign.campaignName}</h3>
            <p class="campaign-subtitle">${campaign.race} • ${campaign.totalMissions} missions</p>
          </div>
        </div>
        
        <div class="campaign-progress">
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${campaign.progress.percentage}%"></div>
          </div>
          <div class="progress-text">${campaign.progress.completed}/${campaign.progress.total} completed (${campaign.progress.percentage}%)</div>
        </div>

        <div class="missions-list">
          ${campaign.progress.missions.map(mission => `
            <div class="mission-item">
              <div class="mission-status ${mission.isCompleted ? 'mission-completed' : 'mission-incomplete'}">
                <i class="fas ${mission.isCompleted ? 'fa-check' : 'fa-lock'}"></i>
              </div>
              <div class="mission-name">${mission.missionNumber}. ${mission.missionName}</div>
              <div class="mission-rewards">
                <span class="arena-gold"><i class="fas fa-coins"></i> 5</span>
                <span class="achievement-xp"><i class="fas fa-star"></i> 5 XP</span>
              </div>
              ${!mission.isCompleted ? `
                <button class="btn-complete-mission" onclick="openCompletionModal('${mission._id}', '${mission.missionName.replace(/'/g, "\\'")}', '${mission._id}')">
                  <i class="fas fa-flag-checkered"></i> Complete
                </button>
              ` : ''}
            </div>
          `).join('')}
        </div>
        
        <div class="campaign-actions">
          ${this.getNextMissionButton(campaign)}
        </div>
      </div>
    `;
  }

  getNextMissionButton(campaign) {
    const nextMission = campaign.progress.missions.find(m => !m.isCompleted);
    
    if (!nextMission) {
      return '<button class="btn btn-success" disabled><i class="fas fa-trophy"></i> Campaign Complete</button>';
    }

    return `<button class="btn btn-complete-main" onclick="openCompletionModal('${nextMission._id}', '${nextMission.missionName.replace(/'/g, "\\'")}', '${nextMission._id}')">
      <i class="fas fa-flag-checkered"></i> Complete Mission ${nextMission.missionNumber}
    </button>`;
  }

  getGameTitle(game, expansion) {
    const titles = {
      'warcraft1_base': 'WC: Orcs & Humans',
      'warcraft2_base': 'WC II: Tides of Darkness',
      'warcraft2_btdp': 'WC II: Beyond the Dark Portal',
      'warcraft3_roc': 'WC III: Reign of Chaos',
      'warcraft3_tft': 'WC III: The Frozen Throne'
    };
    return titles[`${game}_${expansion}`] || `${game} (${expansion})`;
  }

  getGameIcon(game) {
    const icons = {
      'warcraft1': 'fas fa-castle',
      'warcraft2': 'fas fa-ship',
      'warcraft3': 'fas fa-fire'
    };
    return icons[game] || 'fas fa-gamepad';
  }

  getRaceIcon(race) {
    const icons = {
      'human': 'fas fa-shield-alt',
      'orc': 'fas fa-hammer',
      'undead': 'fas fa-skull',
      'nightelf': 'fas fa-leaf',
      'alliance': 'fas fa-flag',
      'horde': 'fas fa-flag'
    };
    return icons[race.toLowerCase()] || 'fas fa-user';
  }

  setupEventListeners() {
    // Modal close events
    const modal = document.getElementById('completion-modal');
    const closeBtn = document.getElementById('modal-close');
    const cancelBtn = document.getElementById('cancel-btn');

    closeBtn.addEventListener('click', () => {
      modal.style.display = 'none';
      modal.classList.remove('show');
    });

    cancelBtn.addEventListener('click', () => {
      modal.style.display = 'none';
      modal.classList.remove('show');
    });

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
      }
    });

    // Speedrun checkbox toggle
    const speedrunCheckbox = document.getElementById('is-speedrun');
    const speedrunTimeGroup = document.getElementById('speedrun-time-group');
    const videoProofGroup = document.getElementById('video-proof-group');
    
    speedrunCheckbox.addEventListener('change', async (e) => {
      if (e.target.checked) {
        speedrunTimeGroup.style.display = 'block';
        videoProofGroup.style.display = 'block';
      } else {
        speedrunTimeGroup.style.display = 'none';
        videoProofGroup.style.display = 'none';
        // Clear speedrun fields
        document.getElementById('completion-minutes').value = '';
        document.getElementById('completion-seconds').value = '';
        document.getElementById('video-proof').value = '';
      }
      await this.updateRewardPreview();
    });

    // Difficulty change
    document.getElementById('difficulty').addEventListener('change', async () => {
      await this.updateRewardPreview();
    });

    // Time input handling
    const minutesInput = document.getElementById('completion-minutes');
    const secondsInput = document.getElementById('completion-seconds');
    const hiddenTimeInput = document.getElementById('completion-time');

    const updateHiddenTime = () => {
      const minutes = parseInt(minutesInput.value) || 0;
      const seconds = parseInt(secondsInput.value) || 0;
      const totalSeconds = (minutes * 60) + seconds;
      hiddenTimeInput.value = totalSeconds > 0 ? totalSeconds : '';
    };

    minutesInput.addEventListener('input', updateHiddenTime);
    secondsInput.addEventListener('input', updateHiddenTime);

    // File upload handling - UI interactions only (compression handled by global system)
    const fileInput = document.getElementById('screenshots');
    const fileList = document.getElementById('file-list');
    const fileUpload = document.getElementById('file-upload');

    // Setup file removal functionality (once)
    this.setupFileListRemoval();
    
    // Expose file list update function for global compression system
    window.updateCampaignFileList = (files) => {
      this.handleFileSelection(files);
    };

    // Click to open file dialog
    fileUpload.addEventListener('click', (e) => {
      // Don't trigger if clicking on the actual file input
      if (e.target !== fileInput) {
        e.preventDefault();
        fileInput.click();
      }
    });

    // Drag and drop
    fileUpload.addEventListener('dragover', (e) => {
      e.preventDefault();
      e.stopPropagation();
      fileUpload.classList.add('drag-over');
    });

    fileUpload.addEventListener('dragleave', (e) => {
      e.preventDefault();
      e.stopPropagation();
      fileUpload.classList.remove('drag-over');
    });

    fileUpload.addEventListener('drop', (e) => {
      e.preventDefault();
      e.stopPropagation();
      fileUpload.classList.remove('drag-over');
      
      // Set files to input and let global compression handle it
      const files = e.dataTransfer.files;
      fileInput.files = files;
      fileInput.dispatchEvent(new Event('change', { bubbles: true }));
    });

    // Form submission
    document.getElementById('completion-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      await this.handleSubmission(e);
    });

    // Event delegation for dynamically created buttons
    document.addEventListener('click', (e) => {
      // Handle game section toggle buttons
      if (e.target.closest('.game-header')) {
        const gameHeader = e.target.closest('.game-header');
        const gameSection = gameHeader.closest('.game-section');
        if (gameSection) {
          const gameKey = gameSection.getAttribute('data-game');
          if (gameKey) {
            toggleGameSection(gameKey);
          }
        }
      }

      // Handle report mission buttons
      if (e.target.closest('.btn-report-mission')) {
        e.preventDefault();
        e.stopPropagation();
        
        const btn = e.target.closest('.btn-report-mission');
        const onclick = btn.getAttribute('onclick');
        
        if (onclick) {
          try {
            // Extract function call from onclick attribute
            const match = onclick.match(/openCompletionModal\('([^']*)',\s*'([^']*)',\s*'([^']*)'\)/);
            if (match) {
              const [, missionId, missionName, duplicateMissionId] = match;
              this.openCompletionModal(missionId, missionName, duplicateMissionId);
            } else {
              // Fallback to eval if pattern doesn't match
              eval(onclick);
            }
          } catch (error) {
            console.error('Error executing onclick:', error);
          }
        }
      }

      // Handle view campaign details buttons
      if (e.target.closest('.btn-view-campaign')) {
        e.preventDefault();
        const btn = e.target.closest('.btn-view-campaign');
        const onclick = btn.getAttribute('onclick');
        if (onclick) {
          try {
            eval(onclick);
          } catch (error) {
            console.error('Error executing onclick:', error);
          }
        }
      }
    });
  }

  handleFileSelection(files) {
    const fileList = document.getElementById('file-list');
    
    // Just display the file list - don't modify fileInput.files (global compression handles that)
    const selectedFiles = Array.from(files).slice(0, 5);
    
    // Display file list
    fileList.innerHTML = '';
    selectedFiles.forEach((file, index) => {
      const fileItem = document.createElement('div');
      fileItem.className = 'file-item';
      fileItem.innerHTML = `
        <span class="file-name">${file.name}</span>
        <span class="file-size">${this.formatFileSize(file.size)}</span>
        <button type="button" class="remove-file" data-index="${index}">×</button>
      `;
      fileList.appendChild(fileItem);
    });
  }

  setupFileListRemoval() {
    const fileList = document.getElementById('file-list');
    const fileInput = document.getElementById('screenshots');
    
    // Single event listener for file removal (setup once)
    fileList.addEventListener('click', (e) => {
      if (e.target.classList.contains('remove-file')) {
        const index = parseInt(e.target.dataset.index);
        const dt = new DataTransfer();
        Array.from(fileInput.files).forEach((file, i) => {
          if (i !== index) dt.items.add(file);
        });
        fileInput.files = dt.files;
        
        // Update display without triggering compression again
        this.handleFileSelection(fileInput.files);
      }
    });
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async handleSubmission(e) {
    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = document.getElementById('submit-btn');
    const originalText = submitBtn.textContent;

    try {
      // Validate required fields
      const screenshots = formData.get('screenshots');
      if (!screenshots || screenshots.size === 0) {
        alert('Please upload at least one screenshot');
        return;
      }

      // Validate speedrun data if speedrun is checked
      const isSpeedrun = formData.get('isSpeedrun');
      if (isSpeedrun) {
        const completionTime = formData.get('completionTime');
        if (!completionTime || completionTime <= 0) {
          alert('Please enter a valid completion time for speedruns');
          return;
        }
      }

      // Show loading state
      submitBtn.disabled = true;
      submitBtn.textContent = 'Submitting...';

      const campaignId = formData.get('campaignId');
      const response = await fetch(`/api/campaigns/${campaignId}/complete`, {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to submit completion');
      }

      // Get mission name from modal title for notification
      const modalTitle = document.getElementById('modal-title').textContent;
      const missionName = modalTitle.replace('Complete Mission: ', '');

      // Close modal and refresh data
      const modal = document.getElementById('completion-modal');
      modal.style.display = 'none';
      modal.classList.remove('show');

      // Show success notification immediately
      this.showMissionCompletionNotification(result, missionName);

      // Show achievement notifications immediately (if any)
      if (result.achievementsUnlocked && result.achievementsUnlocked.length > 0) {
        result.achievementsUnlocked.forEach((achievement, index) => {
          setTimeout(() => {
            this.showAchievementNotification(achievement);
          }, (index + 1) * 500); // Reduced delay from 2000ms to 500ms
        });
      }

      // Refresh campaigns data
      await this.loadData();
      
      // Re-render campaigns to show updated completion status
      this.renderCampaigns();

      // Refresh notifications dropdown to show new achievement notifications
      if (window.notificationsManager) {
        await window.notificationsManager.loadNotifications();
      }

    } catch (error) {
      console.error('Error submitting completion:', error);
      alert(`Error: ${error.message}`);
    } finally {
      submitBtn.disabled = false;
      submitBtn.textContent = originalText;
    }
  }

  showMissionCompletionNotification(result, missionName) {
    // Create mission completion notification
    const notification = document.createElement('div');
    notification.className = 'mission-completion-notification';
    
    let rewardsHtml = '';
    if (result.rewards) {
      rewardsHtml = `
        <div class="completion-rewards">
          <span><i class="fas fa-star"></i> +${result.rewards.experience} XP</span>
          <span><i class="fas fa-coins"></i> +${result.rewards.arenaGold} Gold</span>
          ${result.rewards.difficultyMultiplier !== 1 ? `<span><i class="fas fa-trophy"></i> ${result.rewards.difficultyMultiplier}x Difficulty</span>` : ''}
          ${result.rewards.speedrunBonus ? `<span><i class="fas fa-stopwatch"></i> +10% Speedrun</span>` : ''}
        </div>
      `;
    }
    
    notification.innerHTML = `
      <div class="completion-icon">
        <i class="fas fa-flag-checkered"></i>
      </div>
      <div class="completion-content">
        <h4>Mission Completed!</h4>
        <p><strong>${missionName}</strong></p>
        <p class="completion-subtitle">Successfully completed</p>
        ${rewardsHtml}
      </div>
    `;

    document.body.appendChild(notification);

    // Animate in immediately
    setTimeout(() => notification.classList.add('show'), 50);

    // Remove after 4 seconds
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => notification.remove(), 300);
    }, 4000);
  }

  showAchievementNotification(achievement) {
    // Create achievement notification
    const notification = document.createElement('div');
    notification.className = 'achievement-notification';
    
    // Use the new reward structure
    const rewards = achievement.rewards;
    
    notification.innerHTML = `
      <div class="achievement-icon">
        <i class="fas fa-trophy"></i>
      </div>
      <div class="achievement-content">
        <h4>Achievement Unlocked!</h4>
        <p>${achievement.name}</p>
        <div class="achievement-rewards">
          <small>+${rewards.experience} experience</small>
          <small>+${rewards.arenaGold} arena gold</small>
          ${rewards.honorPoints > 0 ? `<small>+${rewards.honorPoints} honor points</small>` : ''}
        </div>
      </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => notification.classList.add('show'), 100);

    // Remove after 5 seconds
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => notification.remove(), 300);
    }, 5000);
  }

  openCompletionModal(missionId, missionName, campaignData) {
    const modal = document.getElementById('completion-modal');
    const modalTitle = document.getElementById('modal-title');
    const missionDisplayName = document.getElementById('mission-display-name');
    const campaignIdInput = document.getElementById('campaign-id');
    const campaignGameInput = document.getElementById('campaign-game');
    
    // Set modal data
    modalTitle.textContent = `Complete Mission: ${missionName}`;
    if (missionDisplayName) {
      missionDisplayName.textContent = missionName;
    }
    campaignIdInput.value = missionId;
    
    // Determine game from campaign data or mission ID
    let gameType = 'warcraft2'; // default
    if (typeof campaignData === 'object' && campaignData.game) {
      gameType = campaignData.game;
    } else if (typeof campaignData === 'string') {
      // Try to determine from campaign data string or use default
      gameType = 'warcraft2';
    }
    
    campaignGameInput.value = gameType;
    
            // Show/hide difficulty selection for WC3
    const difficultyGroup = document.getElementById('difficulty-group');
    if (gameType === 'warcraft3') {
      difficultyGroup.style.display = 'block';
    } else {
      difficultyGroup.style.display = 'none';
    }
    
    // Reset form
    document.getElementById('completion-form').reset();
    campaignIdInput.value = missionId;
    campaignGameInput.value = gameType;
    
    // Reset file list
    document.getElementById('file-list').innerHTML = '';
    
    // Set up initial reward preview (after game type is set)
    this.updateRewardPreview().catch(error => {
      console.error('Error updating reward preview:', error);
    });
    
    // Show modal
    modal.style.display = 'flex';
    modal.classList.add('show');
  }

  async updateRewardPreview() {
    const difficulty = document.getElementById('difficulty').value || 'normal';
    const isSpeedrun = document.getElementById('is-speedrun').checked;
    const gameType = document.getElementById('campaign-game').value || 'warcraft2';
    
    // Base mission rewards (standard for all missions)
    let arenaGold = 5;
    let achievementXP = 5;
    
    // Apply difficulty multipliers for WC3 only
    if (gameType === 'warcraft3') {
      const difficultyMultipliers = {
        'story': 0.8,
        'normal': 1.0,
        'hard': 1.5
      };
      const difficultyMultiplier = difficultyMultipliers[difficulty] || 1.0;
      achievementXP = Math.round(achievementXP * difficultyMultiplier);
      
      // Show difficulty bonus section
      const difficultyBonusItem = document.querySelector('.reward-item.war3-only');
      difficultyBonusItem.style.display = 'flex';
      document.getElementById('difficulty-multiplier').textContent = `${difficultyMultiplier}x`;
    } else {
      // Hide difficulty bonus for other games
      const difficultyBonusItem = document.querySelector('.reward-item.war3-only');
      difficultyBonusItem.style.display = 'none';
    }
    
    // Apply speedrun bonus (small XP bonus for speedruns)
    if (isSpeedrun) {
      achievementXP = Math.round(achievementXP * 1.1);
    }
    
    // Update base reward display
    document.getElementById('arena-gold-reward').textContent = arenaGold;
    document.getElementById('achievement-xp-reward').textContent = achievementXP;
    document.getElementById('honor-points-reward').textContent = 0; // Default honor points
    
    // Show/hide speedrun bonus indicator
    const speedrunBonus = document.getElementById('speedrun-bonus');
    if (isSpeedrun) {
      speedrunBonus.style.display = 'flex';
    } else {
      speedrunBonus.style.display = 'none';
    }

    // Handle campaign completion bonuses for WC1
    await this.updateCampaignCompletionRewards(gameType);

    // Update potential achievements preview
    this.updateAchievementPreview(difficulty, isSpeedrun);
  }

  async updateCampaignCompletionRewards(gameType) {
    const campaignRewardsSection = document.getElementById('campaign-completion-rewards');
    const rewardBreakdown = document.getElementById('completion-reward-breakdown');
    
    if (gameType === 'warcraft1') {
      try {
        // Get user's current progress to check campaign completion status
        const response = await fetch('/api/campaigns/user/progress');
        const result = await response.json();
        
        if (result.success) {
          const userProgress = result.data;
          
          // Check WC1 campaign completion status
          const orcCampaignComplete = this.isCampaignComplete(userProgress, 'warcraft1', 'base', 'orc');
          const humanCampaignComplete = this.isCampaignComplete(userProgress, 'warcraft1', 'base', 'human');
          
          let completionRewards = [];
          
          // Individual campaign completion rewards
          if (!orcCampaignComplete) {
            completionRewards.push({
              name: 'Orc Campaign Master',
              description: 'Complete the WC1 Orc campaign',
              arenaGold: 50,
              achievementXP: 25,
              icon: 'fa-skull'
            });
          }
          
          if (!humanCampaignComplete) {
            completionRewards.push({
              name: 'Human Campaign Master', 
              description: 'Complete the WC1 Human campaign',
              arenaGold: 50,
              achievementXP: 25,
              icon: 'fa-shield'
            });
          }
          
          // Both campaigns completion bonus
          if (!orcCampaignComplete || !humanCampaignComplete) {
            completionRewards.push({
              name: 'WC1 Champion',
              description: 'Complete both Orc and Human campaigns',
              arenaGold: 100,
              achievementXP: 100,
              icon: 'fa-crown',
              requirement: 'Requires both campaigns'
            });
          }
          
          if (completionRewards.length > 0) {
            rewardBreakdown.innerHTML = completionRewards.map(reward => `
              <div class="completion-reward-item">
                <div class="reward-icon">
                  <i class="fas ${reward.icon}"></i>
                </div>
                <div class="reward-info">
                  <div class="reward-name">${reward.name}</div>
                  <div class="reward-desc">${reward.description}</div>
                  ${reward.requirement ? `<div class="reward-requirement">${reward.requirement}</div>` : ''}
                  <div class="reward-values">
                    <span class="reward-gold"><i class="fas fa-coins"></i> +${reward.arenaGold}</span>
                    <span class="reward-xp"><i class="fas fa-star"></i> +${reward.achievementXP}</span>
                  </div>
                </div>
              </div>
            `).join('');
            
            campaignRewardsSection.style.display = 'block';
          } else {
            campaignRewardsSection.style.display = 'none';
          }
        } else {
          campaignRewardsSection.style.display = 'none';
        }
      } catch (error) {
        console.error('Error loading campaign completion rewards:', error);
        campaignRewardsSection.style.display = 'none';
      }
    } else {
      campaignRewardsSection.style.display = 'none';
    }
  }

  isCampaignComplete(userProgress, game, expansion, race) {
    // Check if a specific campaign is complete based on user progress
    const campaignKey = `${game}_${expansion}_${race}`;
    return userProgress.some(progress => 
      progress.campaignId.includes(campaignKey) && progress.isCompleted
    );
  }

  /**
   * Show potential achievements that could be unlocked
   */
  async updateAchievementPreview(difficulty, isSpeedrun) {
    try {
      const previewContainer = document.getElementById('achievement-preview');
      if (!previewContainer) return;

      // Get user's current detailed progress for achievement calculations
      const response = await fetch('/api/campaigns/user/achievement-progress');
      const result = await response.json();
      
      if (!result.success) {
        console.error('Failed to load achievement progress:', result.error);
        previewContainer.style.display = 'none';
        return;
      }

      const progressData = result.data;
      const potentialAchievements = [];

      // Check for mission count achievements
      const nextMissionMilestone = progressData.nextMilestones.missions;
      if (nextMissionMilestone && progressData.totalMissionsCompleted + 1 === nextMissionMilestone) {
        const achievementNames = {
          1: { name: 'First Steps', description: 'Complete your first campaign mission', experience: 25 },
          5: { name: 'Getting Started', description: 'Complete 5 campaign missions', experience: 50 },
          10: { name: 'Mission Veteran', description: 'Complete 10 campaign missions', experience: 75 },
          25: { name: 'Campaign Warrior', description: 'Complete 25 campaign missions', experience: 100 },
          50: { name: 'Mission Master', description: 'Complete 50 campaign missions', experience: 150 },
          100: { name: 'Campaign Legend', description: 'Complete 100 campaign missions', experience: 200 }
        };

        if (achievementNames[nextMissionMilestone]) {
          potentialAchievements.push({
            ...achievementNames[nextMissionMilestone],
            icon: nextMissionMilestone === 1 ? 'fa-flag-checkered' : 'fa-list-check'
          });
        }
      }

      // Check for difficulty achievements (hard mode)
      if (difficulty === 'hard') {
        const nextHardMilestone = progressData.nextMilestones.hardMissions;
        if (nextHardMilestone && progressData.hardMissionsCompleted + 1 === nextHardMilestone) {
          const hardAchievements = {
            1: { name: 'Rising to the Challenge', description: 'Complete your first mission on Hard difficulty', experience: 50 },
            5: { name: 'Hardcore Gamer', description: 'Complete 5 missions on Hard difficulty', experience: 100 },
            10: { name: 'Master of Difficulty', description: 'Complete 10 missions on Hard difficulty', experience: 150 }
          };
          
          if (hardAchievements[nextHardMilestone]) {
            potentialAchievements.push({
              ...hardAchievements[nextHardMilestone],
              icon: 'fa-fire'
            });
          }
        }
      }

      // Check for speedrun achievements
      if (isSpeedrun) {
        const nextSpeedrunMilestone = progressData.nextMilestones.speedruns;
        if (nextSpeedrunMilestone && progressData.speedrunMissionsCompleted + 1 === nextSpeedrunMilestone) {
          const speedrunAchievements = {
            1: { name: 'Need for Speed', description: 'Complete your first speedrun mission', experience: 40 },
            5: { name: 'Speed Demon', description: 'Complete 5 speedrun missions', experience: 75 },
            10: { name: 'Speedrun Master', description: 'Complete 10 speedrun missions', experience: 125 }
          };

          if (speedrunAchievements[nextSpeedrunMilestone]) {
            potentialAchievements.push({
              ...speedrunAchievements[nextSpeedrunMilestone],
              icon: 'fa-stopwatch'
            });
          }
        }

        // Check for time-based speedrun achievements
        // Assume user might complete in under 5 or 10 minutes based on their settings
        if (progressData.speedrunStats.under5Minutes === 0 && !progressData.unlockedAchievements.includes('speedrun_under_5_minutes')) {
          potentialAchievements.push({
            name: 'Lightning Fast',
            description: 'Complete a mission in under 5 minutes',
            experience: 75,
            icon: 'fa-bolt'
          });
        } else if (progressData.speedrunStats.under10Minutes === 0 && !progressData.unlockedAchievements.includes('speedrun_under_10_minutes')) {
          potentialAchievements.push({
            name: 'Speed Runner',
            description: 'Complete a mission in under 10 minutes',
            experience: 50,
            icon: 'fa-clock'
          });
        }
      }

      // Check for game-specific first mission achievements based on current game
      const gameType = document.getElementById('campaign-game').value || 'warcraft2';
      
      if (gameType === 'warcraft1') {
        const wc1Achievements = [
          { id: 'first_warcraft1_mission', name: 'Welcome to Azeroth', description: 'Complete your first WC1 mission', experience: 25, icon: 'fa-castle' }
        ];

        wc1Achievements.forEach(ach => {
          if (!progressData.unlockedAchievements.includes(ach.id)) {
            // Check if user has 0 completions for WC1
            if (progressData.gameProgress['warcraft1'] && progressData.gameProgress['warcraft1'].totalMissions === 0) {
              potentialAchievements.push(ach);
            }
          }
        });
      } else if (gameType === 'warcraft2') {
        const wc2Achievements = [
          { id: 'first_warcraft2_mission', name: 'Tides of Darkness', description: 'Complete your first WC2 mission', experience: 25, icon: 'fa-ship' }
        ];

        wc2Achievements.forEach(ach => {
          if (!progressData.unlockedAchievements.includes(ach.id)) {
            if (progressData.gameProgress['warcraft2'] && progressData.gameProgress['warcraft2'].totalMissions === 0) {
              potentialAchievements.push(ach);
            }
          }
        });
      } else if (gameType === 'warcraft3') {
        const wc3Achievements = [
          { id: 'first_warcraft3_mission', name: 'Reign of Chaos', description: 'Complete your first WC3 mission', experience: 25, icon: 'fa-sword' }
        ];

        wc3Achievements.forEach(ach => {
          if (!progressData.unlockedAchievements.includes(ach.id)) {
            if (progressData.gameProgress['warcraft3'] && progressData.gameProgress['warcraft3'].totalMissions === 0) {
              potentialAchievements.push(ach);
            }
          }
        });
      }

      // Render potential achievements
      if (potentialAchievements.length > 0) {
        previewContainer.innerHTML = `
          <div class="potential-achievements">
            <h4><i class="fas fa-star"></i> Potential Achievements</h4>
            <div class="achievement-list">
              ${potentialAchievements.map(ach => `
                <div class="achievement-preview-item">
                  <i class="fas ${ach.icon}"></i>
                  <div class="achievement-info">
                    <div class="achievement-name">${ach.name}</div>
                    <div class="achievement-desc">${ach.description}</div>
                    <div class="achievement-experience">+${ach.experience} XP</div>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        `;
        previewContainer.style.display = 'block';
      } else {
        previewContainer.style.display = 'none';
      }

    } catch (error) {
      console.error('Error updating achievement preview:', error);
      // Hide the preview on error
      const previewContainer = document.getElementById('achievement-preview');
      if (previewContainer) {
        previewContainer.style.display = 'none';
      }
    }
  }

  showError(message) {
    const container = document.getElementById('campaigns-content');
    container.innerHTML = `
      <div class="error">
        <i class="fas fa-exclamation-triangle"></i>
        <p>${message}</p>
        <button class="btn-campaign btn-view" onclick="location.reload()">Retry</button>
      </div>
    `;
  }
}

// Global functions for onclick handlers
function toggleGameSection(gameKey) {
  console.log('toggleGameSection called with:', gameKey);
  const section = document.querySelector(`[data-game="${gameKey}"]`);
  
  if (!section) {
    console.error('Section not found for gameKey:', gameKey);
    return;
  }
  
  const chevron = section.querySelector('.fa-chevron-down');
  
  section.classList.toggle('collapsed');
  
  if (chevron) {
    chevron.style.transform = section.classList.contains('collapsed') ? 'rotate(-90deg)' : 'rotate(0deg)';
  }
  
  console.log('Section toggled, collapsed:', section.classList.contains('collapsed'));
}

function viewCampaignDetails(game, expansion, race) {
  console.log('Viewing campaign details:', { game, expansion, race });
  
  // Find the campaign data from the grouped campaigns
  let campaign = null;
  for (const gameGroup of campaignsManager.campaigns) {
    if (gameGroup._id.game === game && gameGroup._id.expansion === expansion) {
      campaign = gameGroup.campaigns.find(c => c.race === race);
      if (campaign) {
        // Add the game and expansion info to the campaign
        campaign.game = game;
        campaign.expansion = expansion;
        break;
      }
    }
  }
  
  if (!campaign) {
    showNotification('Campaign not found', 'error');
    return;
  }
  
  // Create and show campaign details modal
  const modal = document.createElement('div');
  modal.className = 'modal campaign-details-modal';
  modal.style.display = 'flex';
  
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-flag"></i>
          ${campaign.campaignName}
        </h2>
        <button class="close-modal" onclick="this.closest('.modal').remove()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="campaign-details-header">
          <div class="campaign-info">
            <h3>${campaignsManager.getGameTitle(game, expansion)}</h3>
            <p><strong>Race:</strong> ${race}</p>
            <p><strong>Total Missions:</strong> ${campaign.totalMissions}</p>
            <p><strong>Progress:</strong> ${campaign.progress.completed}/${campaign.progress.total} completed (${campaign.progress.percentage}%)</p>
          </div>
          <div class="campaign-progress-large">
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${campaign.progress.percentage}%"></div>
            </div>
          </div>
        </div>
        
        <div class="missions-detailed-list">
          <h4>Mission List</h4>
          ${campaign.progress.missions.map(mission => `
            <div class="mission-detailed-item ${mission.isCompleted ? 'completed' : 'incomplete'}">
              <div class="mission-status">
                <i class="fas ${mission.isCompleted ? 'fa-check-circle' : 'fa-circle'}"></i>
              </div>
              <div class="mission-details">
                <h5>${mission.missionNumber}. ${mission.missionName}</h5>
                <p class="mission-reward">Rewards: 5 arena gold + 5 experience</p>
                ${mission.isCompleted ? 
                  `<p class="completion-date">Completed: ${new Date(mission.completedAt || Date.now()).toLocaleDateString()}</p>` :
                  '<p class="mission-status-text">Not completed</p>'
                }
              </div>
              <div class="mission-actions">
                ${!mission.isCompleted ? `
                  <button class="btn btn-primary btn-sm" onclick="openCompletionModal('${mission._id}', '${mission.missionName}', '${mission._id}'); this.closest('.modal').remove();">
                    <i class="fas fa-upload"></i> Report Completion
                  </button>
                ` : `
                  <span class="completion-badge">
                    <i class="fas fa-trophy"></i> Complete
                  </span>
                `}
              </div>
            </div>
          `).join('')}
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
          <i class="fas fa-times"></i> Close
        </button>
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
  
  // Close modal when clicking outside
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });
}

function openCompletionModal(missionId, missionName, duplicateMissionId) {
  campaignsManager.openCompletionModal(missionId, missionName, duplicateMissionId);
}

// Initialize when page loads
let campaignsManager;
document.addEventListener('DOMContentLoaded', () => {
  campaignsManager = new CampaignsManager();
});

// Notification system
function showNotification(message, type = 'info') {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
    <i class="fas ${getNotificationIcon(type)}"></i>
    <span>${message}</span>
    <button class="notification-close" onclick="this.parentElement.remove()">
      <i class="fas fa-times"></i>
    </button>
  `;

  // Add styles if not already present
  if (!document.getElementById('notification-styles')) {
    const styles = document.createElement('style');
    styles.id = 'notification-styles';
    styles.textContent = `
      .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #333;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        border-left: 4px solid;
        display: flex;
        align-items: center;
        gap: 10px;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        animation: slideIn 0.3s ease-out;
      }
      
      .notification-success { border-left-color: #28a745; }
      .notification-error { border-left-color: #dc3545; }
      .notification-warning { border-left-color: #ffc107; }
      .notification-info { border-left-color: #17a2b8; }
      
      .notification-close {
        background: none;
        border: none;
        color: #ccc;
        cursor: pointer;
        margin-left: auto;
        padding: 0;
        font-size: 1rem;
      }
      
      .notification-close:hover {
        color: white;
      }
      
      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
    `;
    document.head.appendChild(styles);
  }

  // Add to page
  document.body.appendChild(notification);

  // Auto-remove after 5 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.remove();
    }
  }, 5000);
}

function getNotificationIcon(type) {
  const icons = {
    success: 'fa-check-circle',
    error: 'fa-exclamation-circle',
    warning: 'fa-exclamation-triangle',
    info: 'fa-info-circle'
  };
  return icons[type] || 'fa-info-circle';
} 