const fs = require('fs').promises;
const path = require('path');
const chokidar = require('chokidar');
const { EventEmitter } = require('events');

class LogFileMonitor extends EventEmitter {
  constructor(gameDetector) {
    super();
    this.gameDetector = gameDetector;
    this.watchers = [];
    this.isMonitoring = false;
    this.lastFilePositions = new Map();
    
    // Define log file locations for each game
    this.logPaths = {
      warcraft1: [
        // DOSBox log locations
        path.join(require('os').homedir(), 'Documents', 'DOSBox', 'logs'),
        path.join(require('os').homedir(), 'AppData', 'Local', 'DOSBox'),
        'C:\\DOSBox\\logs',
        // ScummVM save locations
        path.join(require('os').homedir(), 'Documents', 'ScummVM', 'Saves'),
        path.join(require('os').homedir(), 'AppData', 'Roaming', 'ScummVM'),
      ],
      warcraft2: [
        // War2 BNE locations
        path.join(require('os').homedir(), 'Documents', 'Warcraft II BNE'),
        path.join(require('os').homedir(), 'Documents', 'Warcraft II'),
        'C:\\Program Files (x86)\\Warcraft II BNE',
        'C:\\Program Files\\Warcraft II BNE',
        // GOG locations
        path.join(require('os').homedir(), 'Documents', 'GOG Galaxy', 'Games', 'Warcraft II'),
        // DOSBox locations for DOS version
        path.join(require('os').homedir(), 'Documents', 'DOSBox'),
      ],
      warcraft3: [
        // WC3 Standard locations
        path.join(require('os').homedir(), 'Documents', 'Warcraft III'),
        path.join(require('os').homedir(), 'Documents', 'Warcraft III Reforged'),
        'C:\\Program Files (x86)\\Warcraft III',
        'C:\\Program Files\\Warcraft III',
        // Battle.net locations
        path.join(require('os').homedir(), 'Documents', 'Battle.net'),
        // Custom game locations
        path.join(require('os').homedir(), 'Documents', 'W3Champions'),
      ]
    };
    
    // Match result patterns for each game
    this.matchPatterns = {
      warcraft1: {
        victory: [
          /victory/i,
          /mission accomplished/i,
          /you have won/i,
          /conquest complete/i
        ],
        defeat: [
          /defeat/i,
          /mission failed/i,
          /you have been defeated/i,
          /game over/i
        ],
        players: [
          /player\s+(\d+):\s*([^,\n]+)/i,
          /human\s+player:\s*([^,\n]+)/i,
          /orc\s+player:\s*([^,\n]+)/i
        ]
      },
      warcraft2: {
        victory: [
          /victory!/i,
          /mission completed/i,
          /you are victorious/i,
          /conquest achieved/i
        ],
        defeat: [
          /defeat!/i,
          /mission failed/i,
          /you have been defeated/i,
          /your forces have been destroyed/i
        ],
        players: [
          /player\s+(\d+):\s*([^,\n]+)/i,
          /human:\s*([^,\n]+)/i,
          /orc:\s*([^,\n]+)/i
        ]
      },
      warcraft3: {
        victory: [
          /victory/i,
          /mission accomplished/i,
          /you win/i,
          /your forces have won/i
        ],
        defeat: [
          /defeat/i,
          /mission failed/i,
          /you lose/i,
          /your forces have been defeated/i
        ],
        players: [
          /player\s+(\d+):\s*([^,\n]+)/i,
          /human:\s*([^,\n]+)/i,
          /orc:\s*([^,\n]+)/i,
          /undead:\s*([^,\n]+)/i,
          /night\s+elf:\s*([^,\n]+)/i,
          /random:\s*([^,\n]+)/i
        ]
      }
    };
  }
  
  async startMonitoring() {
    if (this.isMonitoring) return;
    
    console.log('🔍 Starting log file monitoring for match results...');
    this.isMonitoring = true;
    
    // Monitor log files for each game type
    for (const [gameType, paths] of Object.entries(this.logPaths)) {
      for (const logPath of paths) {
        try {
          const pathExists = await fs.access(logPath).then(() => true).catch(() => false);
          if (!pathExists) continue;
          
          const watcher = chokidar.watch(logPath, {
            ignored: /node_modules/,
            persistent: true,
            depth: 3,
            awaitWriteFinish: {
              stabilityThreshold: 2000,
              pollInterval: 100
            },
            ignorePermissionErrors: true
          });
          
          watcher.on('add', (filePath) => this.handleNewLogFile(filePath, gameType));
          watcher.on('change', (filePath) => this.handleLogFileChange(filePath, gameType));
          
          this.watchers.push(watcher);
          console.log(`📁 Monitoring ${gameType} logs in: ${logPath}`);
          
        } catch (error) {
          console.error(`Failed to monitor ${gameType} logs in ${logPath}:`, error);
        }
      }
    }
  }
  
  async handleNewLogFile(filePath, gameType) {
    const fileName = path.basename(filePath).toLowerCase();
    
    // Check if it's a relevant log file
    if (this.isRelevantLogFile(fileName, gameType)) {
      console.log(`📄 New ${gameType} log file detected: ${filePath}`);
      await this.processLogFile(filePath, gameType);
    }
  }
  
  async handleLogFileChange(filePath, gameType) {
    const fileName = path.basename(filePath).toLowerCase();
    
    if (this.isRelevantLogFile(fileName, gameType)) {
      console.log(`📝 ${gameType} log file updated: ${filePath}`);
      await this.processLogFile(filePath, gameType, true);
    }
  }
  
  isRelevantLogFile(fileName, gameType) {
    const relevantPatterns = {
      warcraft1: ['war.log', 'warcraft.log', 'game.log', 'dosbox.log', 'scummvm.log'],
      warcraft2: ['war2.log', 'warcraft2.log', 'game.log', 'battle.log', 'dosbox.log'],
      warcraft3: ['war3.log', 'warcraft3.log', 'game.log', 'battle.log', 'match.log', 'replay.log']
    };
    
    const patterns = relevantPatterns[gameType] || [];
    return patterns.some(pattern => fileName.includes(pattern));
  }
  
  async processLogFile(filePath, gameType, isUpdate = false) {
    try {
      const stats = await fs.stat(filePath);
      const fileSize = stats.size;
      
      let startPosition = 0;
      if (isUpdate && this.lastFilePositions.has(filePath)) {
        startPosition = this.lastFilePositions.get(filePath);
      }
      
      // Only read new content if file has grown
      if (fileSize <= startPosition) return;
      
      const fileHandle = await fs.open(filePath, 'r');
      const buffer = Buffer.alloc(fileSize - startPosition);
      await fileHandle.read(buffer, 0, buffer.length, startPosition);
      await fileHandle.close();
      
      const content = buffer.toString('utf8');
      this.lastFilePositions.set(filePath, fileSize);
      
      // Parse the log content for match results
      const matchResult = this.parseMatchResult(content, gameType);
      
      if (matchResult) {
        console.log(`🎯 Match result detected in ${gameType} log: ${matchResult.result}`);
        
        // Emit match result event
        this.emit('matchResult', {
          gameType,
          filePath,
          timestamp: new Date(),
          ...matchResult
        });
      }
      
    } catch (error) {
      console.error(`Error processing log file ${filePath}:`, error);
    }
  }
  
  parseMatchResult(content, gameType) {
    const patterns = this.matchPatterns[gameType];
    if (!patterns) return null;
    
    const lines = content.split('\n');
    let result = null;
    let confidence = 0;
    let players = [];
    let matchData = {};
    
    // Check for victory/defeat patterns
    for (const line of lines) {
      // Check victory patterns
      for (const pattern of patterns.victory) {
        if (pattern.test(line)) {
          result = 'victory';
          confidence = 90;
          break;
        }
      }
      
      // Check defeat patterns
      for (const pattern of patterns.defeat) {
        if (pattern.test(line)) {
          result = 'defeat';
          confidence = 90;
          break;
        }
      }
      
      // Extract player information
      for (const pattern of patterns.players) {
        const match = pattern.exec(line);
        if (match) {
          players.push({
            name: match[2] || match[1],
            playerNumber: match[1] ? parseInt(match[1]) : null
          });
        }
      }
    }
    
    // Additional parsing for specific game types
    if (gameType === 'warcraft3') {
      // Parse WC3-specific data (races, teams, etc.)
      matchData = this.parseWC3SpecificData(content);
    } else if (gameType === 'warcraft2') {
      // Parse WC2-specific data (map, resources, etc.)
      matchData = this.parseWC2SpecificData(content);
    }
    
    if (result && confidence > 70) {
      return {
        result,
        confidence,
        players,
        matchData,
        gameType,
        source: 'log_file'
      };
    }
    
    return null;
  }
  
  parseWC3SpecificData(content) {
    const data = {};
    
    // Extract map name
    const mapMatch = content.match(/map:\s*([^\n]+)/i);
    if (mapMatch) {
      data.mapName = mapMatch[1].trim();
    }
    
    // Extract game mode
    const modeMatch = content.match(/game\s+mode:\s*([^\n]+)/i);
    if (modeMatch) {
      data.gameMode = modeMatch[1].trim();
    }
    
    // Extract duration
    const durationMatch = content.match(/duration:\s*(\d+:\d+)/i);
    if (durationMatch) {
      data.duration = durationMatch[1];
    }
    
    return data;
  }
  
  parseWC2SpecificData(content) {
    const data = {};
    
    // Extract map name
    const mapMatch = content.match(/map:\s*([^\n]+)/i);
    if (mapMatch) {
      data.mapName = mapMatch[1].trim();
    }
    
    // Extract resource level
    const resourceMatch = content.match(/resources:\s*(high|medium|low)/i);
    if (resourceMatch) {
      data.resourceLevel = resourceMatch[1].toLowerCase();
    }
    
    return data;
  }
  
  stopMonitoring() {
    if (!this.isMonitoring) return;
    
    console.log('🛑 Stopping log file monitoring...');
    
    for (const watcher of this.watchers) {
      watcher.close();
    }
    
    this.watchers = [];
    this.isMonitoring = false;
    this.lastFilePositions.clear();
  }
  
  getMonitoringStats() {
    return {
      isMonitoring: this.isMonitoring,
      watchedPaths: this.watchers.length,
      trackedFiles: this.lastFilePositions.size,
      supportedGames: Object.keys(this.logPaths)
    };
  }
}

module.exports = LogFileMonitor; 