<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <title>WC Arena - Epic Map Collection</title>
  
  <!-- Preload critical resources -->
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Universal Navbar Consistency - MUST be loaded last for proper override -->
  <link rel="stylesheet" href="/css/navbar-universal.css" />
  
  <!-- Chart.js for terrain visualization -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  
  <!-- Maps Page Styles -->
  <link rel="stylesheet" href="/css/maps/maps-base.css">
  <link rel="stylesheet" href="/css/maps/maps-hero.css">
  <link rel="stylesheet" href="/css/maps/maps-tabs.css">
  <link rel="stylesheet" href="/css/maps/maps-cards.css">
  <link rel="stylesheet" href="/css/maps/maps-modal.css">
  <link rel="stylesheet" href="/css/maps/maps-modal-modern.css">
  <link rel="stylesheet" href="/css/maps/maps-war3-images.css">
  <link rel="stylesheet" href="/css/maps/maps-war3-overlay.css">
</head>
<body>
  <div id="navbar-container"></div>

  <main>
    <!-- Epic Maps Hero Section -->
    <section class="maps-hero">
      <div class="hero-content fade-in">
        <h1 class="hero-title" data-theme="atlas">🗺️ Atlas</h1>
      </div>
    </section>

    <!-- Main Maps Content -->
    <div class="maps-main">
      <!-- Game Selection Tabs with Stats -->
      <section class="game-tabs-container fade-in">
        <div class="game-tabs-wrapper">
          <div class="game-tabs">
            <button class="game-tab" data-game="war1">
              <i class="fas fa-dragon"></i>
              <span>WC I</span>
            </button>
            <button class="game-tab active" data-game="war2">
              <i class="fas fa-shield-alt"></i>
              <span>WC II</span>
            </button>
            <button class="game-tab" data-game="war3">
              <i class="fas fa-chess-king"></i>
              <span>WC III</span>
            </button>
          </div>
          
          <div class="hero-stats">
            <div class="hero-stat">
              <div class="hero-stat-value" id="current-game-maps">...</div>
              <div class="hero-stat-label">Maps Available</div>
            </div>
            <div class="hero-stat">
              <div class="hero-stat-value" id="recent-uploads">...</div>
              <div class="hero-stat-label">This Month</div>
            </div>
          </div>
        </div>
      </section>

      <!-- WC I - Custom Scenarios Section -->
      <div class="game-content war1-content" id="war1-content">
        <div class="war1-section fade-in">
          <div class="section-header">
            <div class="section-title-with-search">
              <div class="section-title">
                <i class="fas fa-dragon"></i>
                <span>WC I</span>
              </div>
            </div>
            
            <div class="section-actions">
              <button id="war1-random-scenario-btn" class="epic-btn secondary">
                <i class="fas fa-random"></i>
                <span>Random Scenario</span>
              </button>
            </div>
          </div>

          <!-- Category tabs for WC1 -->
          <div class="maps-tabs-section">
            <div class="maps-tabs">
              <button class="tab-btn active" data-category="all">All Scenarios</button>
              <button class="tab-btn" data-category="forest">Forest</button>
              <button class="tab-btn" data-category="swamp">Swamp</button>
              <button class="tab-btn" data-category="dungeon">Dungeon</button>
            </div>
          </div>

          <!-- WC1 Scenarios Grid -->
          <div class="war1-scenarios-grid" id="war1-scenarios-grid">
            <!-- Scenarios will be loaded here dynamically -->
            <div class="loading">Loading classic scenarios...</div>
          </div>
        </div>
      </div>

      <!-- WC II - Active Content -->
      <div class="game-content war2-content active" id="war2-content">
        <div class="war2-section fade-in">
          <div class="section-header">
            <div class="section-title-with-search">
              <div class="section-title">
                <i class="fas fa-shield-alt"></i>
                <span>WC II</span>
              </div>
              
              <!-- Search bar moved next to WC II -->
              <div class="maps-search-inline">
                <div class="search-input-container">
                  <input type="text" id="search-input" placeholder="Search maps...">
                  <button id="search-btn" class="search-btn-modern">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <div class="section-actions">
              <button id="upload-map-btn" class="epic-btn">
                <i class="fas fa-upload"></i>
                <span>Upload Map</span>
              </button>
              <button id="show-random-map-btn" class="epic-btn secondary">
                <i class="fas fa-random"></i>
                <span>Random Map</span>
              </button>
            </div>
          </div>

          <!-- Filter tabs moved up -->
          <div class="maps-tabs-section">
            <div class="maps-tabs">
              <button class="tab-btn active" data-tab="all">All Maps</button>
              <button class="tab-btn" data-tab="popular">Most Played</button>
              <button class="tab-btn" data-tab="top-rated">Top Rated</button>
              <button class="tab-btn" data-tab="recent">Recently Added</button>
              <button class="tab-btn" data-tab="land">Land Maps</button>
              <button class="tab-btn" data-tab="sea">Sea Maps</button>
            </div>
          </div>

          <div class="maps-grid" id="maps-grid">
            <!-- Maps will be loaded here dynamically -->
            <div class="loading">Loading epic battlegrounds...</div>
          </div>

          <!-- Pagination at bottom -->
          <div class="pagination-container">
            <div class="pagination" id="pagination">
              <!-- Pagination will be added here dynamically -->
            </div>
          </div>
        </div>
      </div>

      <!-- WC III - Content -->
      <div class="game-content war3-content" id="war3-content">
        <div class="war3-section fade-in">
          <div class="section-header">
            <div class="section-title-with-search">
              <div class="section-title">
                <i class="fas fa-chess-king"></i>
                <span>WC III</span>
              </div>
              
              <!-- Search bar for WC3 -->
              <div class="maps-search-inline">
                <div class="search-input-container">
                  <input type="text" id="war3-search-input" placeholder="Search WC3 maps...">
                  <button id="war3-search-btn" class="search-btn-modern">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <div class="section-actions">
              <button id="process-enhanced-war3-maps-btn" class="epic-btn admin-only" style="display: none;">
                <i class="fas fa-cogs"></i>
                <span>Enhanced Processing</span>
              </button>
              <button id="process-war3-maps-btn" class="epic-btn admin-only" style="display: none;">
                <i class="fas fa-cogs"></i>
                <span>Process Maps</span>
              </button>
              <button id="war3-random-map-btn" class="epic-btn secondary">
                <i class="fas fa-random"></i>
                <span>Random Map</span>
              </button>
              <label for="war3-map-upload-input" class="epic-btn">
                <i class="fas fa-upload"></i>
                <span>Upload Map</span>
              </label>
              <input type="file" id="war3-map-upload-input" accept=".w3m,.w3x" style="display: none;">
            </div>
          </div>

          <!-- Filter tabs for WC3 -->
          <div class="maps-tabs-section">
            <div class="maps-tabs">
              <button class="tab-btn active" data-tab="all">All Maps</button>
              <button class="tab-btn" data-tab="popular">Most Played</button>
              <button class="tab-btn" data-tab="top-rated">Top Rated</button>
              <button class="tab-btn" data-tab="recent">Recently Added</button>
            </div>
          </div>

          <!-- War3 Maps Grid -->
          <div class="war3-maps-grid" id="war3-maps-grid">
            <!-- War3 maps will be loaded here dynamically -->
            <div class="loading">Loading War3Net maps...</div>
          </div>

          <!-- War3 Pagination -->
          <div class="war3-pagination-container">
            <div class="pagination" id="war3-pagination">
              <!-- War3 pagination will be added here dynamically -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <div id="footer-container"></div>

  <!-- Loading Overlay -->
  <div id="loadingOverlay" class="loading-overlay">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-text">Loading Epic Maps...</div>
    </div>
  </div>

  <!-- Map Upload Modal -->
  <div id="upload-map-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Upload New Map</h2>
        <span class="close-modal">&times;</span>
      </div>
      <div class="modal-body">
        <form id="upload-map-form" method="POST" enctype="multipart/form-data">
          <div class="form-group">
            <label for="map-file">Select Map File (.pud)</label>
            <input type="file" id="map-file" name="mapFile" accept=".pud" required>
            <div id="map-name-display" style="display: none;">
              <p>Map Name: <strong id="map-name-text"></strong></p>
            </div>
          </div>
          
          <div class="form-group">
            <label for="map-description">Description (Optional)</label>
            <textarea id="map-description" name="description" rows="3" placeholder="Describe your map..."></textarea>
          </div>
          
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" id="cancel-upload-btn">Cancel</button>
            <button type="submit" class="btn btn-primary" id="upload-map-submit-btn" disabled>
              <i class="fas fa-cloud-upload-alt"></i>
              <span>Select a PUD file first</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Map Details Modal -->
  <div id="map-details-modal" class="modal">
    <div class="modal-content modal-large">
      <div class="modal-header">
        <h2>Map Details</h2>
        <span class="close-modal">&times;</span>
      </div>
      <div class="modal-body">
        <div id="map-details-container">
          <!-- Map details will be loaded here dynamically -->
        </div>
      </div>
    </div>
  </div>

  <!-- Core Scripts -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
  <script src="/js/utils.js"></script>
  <script src="/js/notifications.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/app.js"></script>
  
  <!-- Image Compression -->
  <script type="module" src="/js/modules/ImageCompressor.js"></script>
  <script type="module" src="/js/ultraCompressionIntegration.js"></script>
  
  <!-- Maps System Scripts as ES6 modules -->
  <script type="module" src="/js/maps.js"></script>
  <script type="module" src="/js/modules/MapsCore.js"></script>
  <script type="module" src="/js/modules/MapsGrid.js"></script>
  <script type="module" src="/js/modules/MapDetails.js"></script>
  <script type="module" src="/js/modules/MapManagement.js"></script>
  <script type="module" src="/js/modules/WC1Manager.js"></script>
  <script type="module" src="/js/modules/MapDetailsViewer.js"></script>
  <script src="/js/modules/War3OverlayRenderer.js"></script>

  <script>
    // Initialize maps system when DOM is ready
    document.addEventListener('DOMContentLoaded', async () => {
      console.log('🗺️ Maps page loaded - thumbnail auto-generation enabled');
      
      // Initialize maps system
      if (typeof initializeMapsSystem === 'function') {
        await initializeMapsSystem();
      }
      
      // Load unified navigation and update profile
      console.log('🔄 Initializing navigation on maps page...');

      // Load unified navigation
      if (typeof window.loadNavigation === 'function') {
        await window.loadNavigation();
      } else if (typeof window.loadNavbar === 'function') {
        await window.loadNavbar();
      }

      // Update navbar profile
      setTimeout(async () => {
        if (window.updateNavbarProfileUnified) {
          console.log('🔄 Updating navbar profile (unified) on maps page');
          await window.updateNavbarProfileUnified();
        } else if (window.updateNavbarProfile) {
          console.log('🔄 Updating navbar profile (legacy) on maps page');
          await window.updateNavbarProfile();
        }
      }, 100);
      
      console.log('🗺️ Using War3Net system for War3 maps');
      
      // Setup compression handlers
      console.log('🔧 Setting up compression handlers...');
      
      // Add change listener to map file input
      const mapFileInput = document.getElementById('map-file');
      if (mapFileInput) {
        console.log('📁 Found map file input, adding change listener...');
        mapFileInput.addEventListener('change', function(event) {
          const file = event.target.files[0];
          if (!file) {
            console.log('📁 No file selected');
            // Hide map name display and disable button
            const mapNameDisplay = document.getElementById('map-name-display');
            const submitBtn = document.getElementById('upload-map-submit-btn');
            
            if (mapNameDisplay) mapNameDisplay.style.display = 'none';
            if (submitBtn) {
              submitBtn.disabled = true;
              submitBtn.innerHTML = '<i class="fas fa-cloud-upload-alt"></i><span>Select a PUD file first</span>';
            }
            return;
          }
          
          console.log(`📁 Selected map file: ${file.name} (${file.size} bytes)`);
          
          // Validate file type
          if (!file.name.toLowerCase().endsWith('.pud')) {
            alert('Only .pud files are allowed for maps');
            event.target.value = '';
            return;
          }
          
          console.log('✅ File validation passed');
          
          // Show map name
          const mapNameDisplay = document.getElementById('map-name-display');
          const mapNameText = document.getElementById('map-name-text');
          if (mapNameDisplay && mapNameText) {
            const mapName = file.name.replace(/\.pud$/i, '');
            mapNameText.textContent = mapName;
            mapNameDisplay.style.display = 'block';
            console.log(`✨ Displayed map name: ${mapName}`);
          }

          // Enable upload button
          const submitBtn = document.getElementById('upload-map-submit-btn');
          if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-cloud-upload-alt"></i> <span>Upload Map</span>';
            console.log('✅ Submit button enabled');
          }
        });
        
        console.log('✅ File input change listener added');
      } else {
        console.error('❌ Map file input not found!');
      }
    });

    async function loadComponents() {
      try {
        // Load unified navigation (already handled above)
        console.log('🔄 Navigation loading handled in main initialization');

        // Load footer
        const footerResponse = await fetch('/components/footer.html');
        const footerHTML = await footerResponse.text();
        document.getElementById('footer-container').innerHTML = footerHTML;

        console.log('✅ Components loaded successfully');
      } catch (error) {
        console.error('❌ Error loading components:', error);
      } finally {
        // Hide loading overlay
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
          loadingOverlay.style.display = 'none';
        }
      }
    }

    // Enhanced button interactions
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('epic-btn') || e.target.closest('.epic-btn')) {
        const btn = e.target.classList.contains('epic-btn') ? e.target : e.target.closest('.epic-btn');
        
        // Create ripple effect
        const ripple = document.createElement('span');
        ripple.style.position = 'absolute';
        ripple.style.borderRadius = '50%';
        ripple.style.background = 'rgba(255, 255, 255, 0.5)';
        ripple.style.transform = 'scale(0)';
        ripple.style.animation = 'ripple 0.6s linear';
        ripple.style.left = e.offsetX + 'px';
        ripple.style.top = e.offsetY + 'px';
        ripple.style.width = ripple.style.height = '20px';
        ripple.style.marginLeft = ripple.style.marginTop = '-10px';
        
        btn.appendChild(ripple);
        
        setTimeout(() => {
          ripple.remove();
        }, 600);
      }
    });

    // Add ripple animation keyframes
    const style = document.createElement('style');
    style.textContent = `
      @keyframes ripple {
        to {
          transform: scale(4);
          opacity: 0;
        }
      }
      
      /* Enhanced search bar styling */
      .section-title-with-search {
        display: flex;
        align-items: center;
        gap: 2rem;
        flex: 1;
      }
      
      .maps-search-inline {
        flex: 1;
        max-width: 400px;
      }
      
      .search-input-container {
        position: relative;
        display: flex;
        align-items: center;
      }
      
      .search-input-container input {
        flex: 1;
        padding: 0.75rem 3rem 0.75rem 1rem;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 215, 0, 0.3);
        border-radius: 12px;
        color: white;
        font-size: 0.95rem;
        transition: all 0.3s ease;
      }
      
      .search-input-container input:focus {
        outline: none;
        border-color: #ffd700;
        background: rgba(255, 255, 255, 0.08);
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.2);
      }
      
      .search-btn-modern {
        position: absolute;
        right: 4px;
        top: 50%;
        transform: translateY(-50%);
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        border: none;
        border-radius: 8px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #1a1a1a;
        font-size: 1rem;
      }
      
      .search-btn-modern:hover {
        background: linear-gradient(135deg, #ffed4e, #ffd700);
        transform: translateY(-50%) scale(1.05);
        box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
      }
      
      .maps-tabs-section {
        margin: 1rem 0;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }
      
      .pagination-container {
        display: flex;
        justify-content: center;
        margin: 2rem 0;
        padding: 1rem;
      }
    `;
    document.head.appendChild(style);
  </script>

  <div class="map-tooltip"></div>
</body>
</html> 