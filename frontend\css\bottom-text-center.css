/* =============================================================================
   BOTTOM TEXT CENTERING
   ============================================================================= */

.bottom-text-center {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  text-align: center;
  color: #ffffff;
  font-family: 'Cinzel', serif;
  font-size: 1rem;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.bottom-text-center:hover {
  background: rgba(0, 0, 0, 0.8);
  border-color: rgba(212, 175, 55, 0.5);
  transform: translateX(-50%) translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .bottom-text-center {
    bottom: 10px;
    font-size: 0.875rem;
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  .bottom-text-center {
    bottom: 5px;
    font-size: 0.75rem;
    padding: 4px 8px;
  }
} 