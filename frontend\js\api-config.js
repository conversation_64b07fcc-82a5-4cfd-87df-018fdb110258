// API Configuration
// This file defines the base URL for all API calls
const API_BASE_URL = window.location.hostname === 'localhost'
? 'http://localhost:3000'
: 'http://127.0.0.1:3000';

const API_CONFIG = {
  baseURL: API_BASE_URL,
  
  // Helper function to build full API URLs
  getApiUrl: function(endpoint) {
    // Remove leading slash if present to avoid double slashes
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${this.baseURL}/${cleanEndpoint}`;
  }
};

// Export for use in other modules
window.API_CONFIG = API_CONFIG; 