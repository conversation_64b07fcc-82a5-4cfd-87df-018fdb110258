/* Dropdown Menu Core Styles */
.nav-dropdown {
    position: relative;
}

.nav-dropdown .dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-dropdown .dropdown-toggle:hover,
.nav-dropdown .dropdown-toggle[aria-expanded="true"] {
    background: rgba(212, 175, 55, 0.1);
    color: #D4AF37;
    border-color: #D4AF37;
    transform: translateY(-1px);
    box-shadow: 0 4px 20px rgba(212, 175, 55, 0.2);
}

.nav-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 220px;
    background: linear-gradient(135deg, rgba(20, 20, 20, 0.98), rgba(40, 40, 40, 0.95));
    border: 2px solid #D4AF37;
    border-radius: 0.75rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.6), 0 0 30px rgba(212, 175, 55, 0.4);
    backdrop-filter: blur(15px);
    z-index: 10002;
    padding: 0.5rem 0;
    margin-top: 8px;
    display: none;
}

/* Active State */
.nav-dropdown.active .nav-dropdown-menu {
    display: block !important;
}

/* Force Visible State */
.nav-dropdown-menu.force-visible {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

/* Dropdown Items */
.nav-dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    border-left: 3px solid transparent;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.nav-dropdown-item:hover {
    background: rgba(212, 175, 55, 0.1);
    color: #D4AF37;
    border-left-color: #D4AF37;
}

/* Divider */
.dropdown-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 0.5rem 0;
}

/* Town Hall Specific */
.town-hall-dropdown .nav-dropdown-menu {
    left: 50%;
    transform: translateX(-50%);
    min-width: 200px;
}

.town-hall-dropdown .dropdown-toggle {
    position: relative;
}

.town-hall-dropdown .dropdown-toggle::after {
    content: '';
    display: inline-block;
    width: 0.5em;
    height: 0.5em;
    margin-left: 0.5em;
    border-right: 2px solid currentColor;
    border-bottom: 2px solid currentColor;
    transform: rotate(45deg);
    transition: transform 0.2s ease;
}

.town-hall-dropdown.active .dropdown-toggle::after {
    transform: rotate(-135deg);
}

/* Profile Dropdown Specific */
.profile-dropdown {
    position: relative;
    z-index: 10001;
}

.profile-dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-dropdown-toggle:hover,
.profile-dropdown-toggle[aria-expanded="true"] {
    background: rgba(212, 175, 55, 0.1);
    color: #D4AF37;
    border-color: #D4AF37;
    transform: translateY(-1px);
    box-shadow: 0 4px 20px rgba(212, 175, 55, 0.2);
}

/* Mobile Adjustments */
@media (max-width: 768px) {
    .nav-dropdown-menu {
        position: fixed;
        top: 80px;
        left: 0;
        right: 0;
        border-radius: 0;
        margin-top: 0;
        max-height: calc(100vh - 80px);
        overflow-y: auto;
    }
    
    .town-hall-dropdown .nav-dropdown-menu {
        transform: none;
    }
} 