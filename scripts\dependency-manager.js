#!/usr/bin/env node

/**
 * Dependency Manager - Ensures version consistency across all package.json files
 * 
 * This script helps maintain consistent dependency versions across:
 * - Root package.json
 * - backend/package.json  
 * - electron-app/package.json
 */

const fs = require('fs');
const path = require('path');

// Define shared dependencies and their target versions
const SHARED_DEPENDENCIES = {
  'axios': '^1.10.0',
  'lodash': '^4.17.21',
  'moment': '^2.30.1',
  'jimp': '^0.22.10',
  'sharp': '^0.33.4',
  'tesseract.js': '^5.1.1',
  'node-cron': '^3.0.3',
  'chokidar': '^3.5.3',
  'mime-types': '^2.1.35',
  'node-fetch': '^3.3.2',
  'canvas': '^3.1.0'
};

const SHARED_DEV_DEPENDENCIES = {
  'electron-builder': '^25.0.5'
};

class DependencyManager {
  constructor() {
    this.rootDir = path.resolve(__dirname, '..');
    this.packages = [
      { name: 'root', path: path.join(this.rootDir, 'package.json') },
      { name: 'backend', path: path.join(this.rootDir, 'backend', 'package.json') },
      { name: 'electron-app', path: path.join(this.rootDir, 'electron-app', 'package.json') }
    ];
  }

  /**
   * Load all package.json files
   */
  loadPackages() {
    const packages = {};
    
    for (const pkg of this.packages) {
      try {
        const content = fs.readFileSync(pkg.path, 'utf8');
        packages[pkg.name] = {
          path: pkg.path,
          data: JSON.parse(content)
        };
        console.log(`✅ Loaded ${pkg.name} package.json`);
      } catch (error) {
        console.error(`❌ Failed to load ${pkg.name} package.json:`, error.message);
      }
    }
    
    return packages;
  }

  /**
   * Check for version inconsistencies
   */
  checkInconsistencies() {
    const packages = this.loadPackages();
    const inconsistencies = [];

    console.log('\n🔍 Checking for version inconsistencies...');

    // Check shared dependencies
    for (const [depName, targetVersion] of Object.entries(SHARED_DEPENDENCIES)) {
      const versions = {};
      
      for (const [pkgName, pkg] of Object.entries(packages)) {
        const deps = { ...pkg.data.dependencies, ...pkg.data.devDependencies };
        if (deps[depName]) {
          versions[pkgName] = deps[depName];
        }
      }

      if (Object.keys(versions).length > 1) {
        const uniqueVersions = [...new Set(Object.values(versions))];
        if (uniqueVersions.length > 1) {
          inconsistencies.push({
            dependency: depName,
            target: targetVersion,
            current: versions
          });
        }
      }
    }

    return inconsistencies;
  }

  /**
   * Fix version inconsistencies
   */
  fixInconsistencies() {
    const packages = this.loadPackages();
    let fixed = 0;

    console.log('\n🔧 Fixing version inconsistencies...');

    for (const [pkgName, pkg] of Object.entries(packages)) {
      let modified = false;

      // Fix shared dependencies
      for (const [depName, targetVersion] of Object.entries(SHARED_DEPENDENCIES)) {
        if (pkg.data.dependencies && pkg.data.dependencies[depName]) {
          if (pkg.data.dependencies[depName] !== targetVersion) {
            console.log(`  📦 ${pkgName}: ${depName} ${pkg.data.dependencies[depName]} → ${targetVersion}`);
            pkg.data.dependencies[depName] = targetVersion;
            modified = true;
          }
        }
      }

      // Fix shared dev dependencies
      for (const [depName, targetVersion] of Object.entries(SHARED_DEV_DEPENDENCIES)) {
        if (pkg.data.devDependencies && pkg.data.devDependencies[depName]) {
          if (pkg.data.devDependencies[depName] !== targetVersion) {
            console.log(`  📦 ${pkgName}: ${depName} ${pkg.data.devDependencies[depName]} → ${targetVersion} (dev)`);
            pkg.data.devDependencies[depName] = targetVersion;
            modified = true;
          }
        }
      }

      // Save if modified
      if (modified) {
        try {
          fs.writeFileSync(pkg.path, JSON.stringify(pkg.data, null, 2) + '\n');
          console.log(`✅ Updated ${pkgName} package.json`);
          fixed++;
        } catch (error) {
          console.error(`❌ Failed to save ${pkgName} package.json:`, error.message);
        }
      }
    }

    return fixed;
  }

  /**
   * Generate dependency report
   */
  generateReport() {
    const packages = this.loadPackages();
    const report = {
      packages: Object.keys(packages).length,
      dependencies: {},
      devDependencies: {},
      inconsistencies: this.checkInconsistencies()
    };

    // Collect all dependencies
    for (const [pkgName, pkg] of Object.entries(packages)) {
      if (pkg.data.dependencies) {
        for (const [depName, version] of Object.entries(pkg.data.dependencies)) {
          if (!report.dependencies[depName]) report.dependencies[depName] = {};
          report.dependencies[depName][pkgName] = version;
        }
      }
      
      if (pkg.data.devDependencies) {
        for (const [depName, version] of Object.entries(pkg.data.devDependencies)) {
          if (!report.devDependencies[depName]) report.devDependencies[depName] = {};
          report.devDependencies[depName][pkgName] = version;
        }
      }
    }

    return report;
  }

  /**
   * Main execution
   */
  run() {
    console.log('🔧 WC Arena Dependency Manager');
    console.log('================================\n');

    const args = process.argv.slice(2);
    
    if (args.includes('--check')) {
      const inconsistencies = this.checkInconsistencies();
      
      if (inconsistencies.length === 0) {
        console.log('✅ No version inconsistencies found!');
      } else {
        console.log(`❌ Found ${inconsistencies.length} inconsistencies:`);
        inconsistencies.forEach(inc => {
          console.log(`\n📦 ${inc.dependency}:`);
          console.log(`   Target: ${inc.target}`);
          Object.entries(inc.current).forEach(([pkg, version]) => {
            console.log(`   ${pkg}: ${version}`);
          });
        });
      }
    } else if (args.includes('--fix')) {
      const fixed = this.fixInconsistencies();
      console.log(`\n✅ Fixed ${fixed} package.json files`);
      
      if (fixed > 0) {
        console.log('\n💡 Run "npm run install:all" to update lock files');
      }
    } else if (args.includes('--report')) {
      const report = this.generateReport();
      console.log('📊 Dependency Report:');
      console.log(`   Packages: ${report.packages}`);
      console.log(`   Unique dependencies: ${Object.keys(report.dependencies).length}`);
      console.log(`   Unique dev dependencies: ${Object.keys(report.devDependencies).length}`);
      console.log(`   Inconsistencies: ${report.inconsistencies.length}`);
    } else {
      console.log('Usage:');
      console.log('  node scripts/dependency-manager.js --check   # Check for inconsistencies');
      console.log('  node scripts/dependency-manager.js --fix    # Fix inconsistencies');
      console.log('  node scripts/dependency-manager.js --report # Generate report');
    }
  }
}

// Run if called directly
if (require.main === module) {
  const manager = new DependencyManager();
  manager.run();
}

module.exports = DependencyManager;
