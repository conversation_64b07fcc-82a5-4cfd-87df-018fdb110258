/**
 * Modern Navbar System
 * Handles navigation, mobile menu, and user interactions
 */

// CRITICAL DEBUG: Script loading trace
console.log('🚀 NAVBAR SCRIPT LOADING - navbar-modern.js is being executed');
console.log('🚀 Current URL:', window.location.href);
console.log('🚀 Document ready state:', document.readyState);
console.log('🚀 DOM elements present:', {
    hasLogoutItem: !!document.querySelector('.logout-item'),
    hasLogoutApp: !!document.querySelector('#logout-app'),
    hasLogoutLinks: document.querySelectorAll('a[href*="logout"]').length,
    hasLogoutButtons: document.querySelectorAll('button[onclick*="logout"]').length
});

console.log('🚀 Modern Navbar JS loading...');

// Create global instance
window.ModernNavbar = class ModernNavbar {
  constructor() {
    this.initialized = false;
    this.dropdownHandlers = new Map();
    this.documentClickHandler = null;
    this.eventListeners = []; // Initialize event listeners array
    this.domElements = {};
  }

  async init() {
    if (this.initialized) {
      console.log('⚠️ Navbar already initialized, skipping...');
      return;
    }

    try {
      console.log('🚀 Initializing modern navbar...');
      
      // Set page-specific data attribute
      const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
      const navbar = document.querySelector('.navbar-modern');
      if (navbar) {
        navbar.setAttribute('data-page', currentPage);
      }

      // Cache DOM elements
      this.cacheDOMElements();
      
      // Setup dropdowns first
      this.setupDropdowns();
      
      // Setup document click handler to close dropdowns
      this.setupDocumentClickHandler();
      
      // Setup navigation after dropdowns
      this.setupNavigation();
      
      // Setup keyboard navigation
      this.setupKeyboardNavigation();
      
      // Check if user data is already available from AuthManager
      let existingUserData = null;
      if (window._appInstance) {
        const authManager = window._appInstance.getComponent('auth');
        if (authManager && authManager.getUser()) {
          existingUserData = authManager.getUser();
          console.log('✅ User data already available from AuthManager');
        }
      }
      
      if (existingUserData) {
        this.updateUserDisplay(existingUserData);
      } else {
        // Load user data
        await this.loadUserData();
      }
      
      // Listen for auth state changes
      this.setupAuthListener();
      
      // Handle hash in URL for scrolling
      if (window.location.hash) {
        const targetId = window.location.hash.slice(1);
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          setTimeout(() => {
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }, 100);
        }
      }
      
      // Mark as initialized
      this.initialized = true;
      console.log('✅ Modern navbar initialized successfully');
      
    } catch (error) {
      console.error('❌ Error initializing navbar:', error);
      this.cleanup();
    }
  }

  async waitForNavbar() {
    console.log('⏳ Waiting for navbar to be available...');
    let attempts = 0;
    const maxAttempts = 50;
    
    while (attempts < maxAttempts) {
      const navbar = document.querySelector('.navbar, .navbar-modern');
      if (navbar) {
        console.log('✅ Navbar found in DOM');
        return;
      }
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
    
    throw new Error('Navbar not found in DOM after waiting');
  }

  cacheDOMElements() {
    console.log('📦 Caching DOM elements...');
    
    const navbar = document.querySelector('.navbar-modern');
    if (!navbar) {
      console.error('❌ Navbar element not found');
      return false;
    }
    
    console.log('🔍 Navbar element found:', !!navbar);
    
    this.domElements = {
      navbar,
      dropdownToggles: navbar.querySelectorAll('.profile-dropdown-toggle, [data-dropdown-toggle]'),
      dropdownMenus: navbar.querySelectorAll('.nav-dropdown-menu'),
      navLinks: navbar.querySelectorAll('.nav-item:not(.profile-dropdown-toggle), .nav-dropdown-item:not(.logout-item), .brand-link, .admin-link'),
      adminLink: navbar.querySelector('.admin-link'),
      profileImages: {
        desktop: navbar.querySelector('.profile-avatar img') || null,
        mobile: navbar.querySelector('.mobile-profile-image') || null
      }
    };

    console.log('📦 Cached elements:', this.domElements);
    console.log('✅ DOM elements cached');
    return true;
  }

  setupMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    const navbar = document.querySelector('.navbar-modern');

    if (mobileToggle && mobileMenu) {
      // Ensure mobile menu is hidden by default
      mobileMenu.style.display = 'none';
      mobileToggle.style.display = 'none'; // Hide on desktop

      const toggleHandler = (e) => {
        e.preventDefault();
        const isActive = mobileMenu.classList.contains('active');
        
        if (isActive) {
          mobileMenu.classList.remove('active');
          mobileMenu.style.display = 'none';
          navbar.classList.remove('mobile-active');
        } else {
          mobileMenu.classList.add('active');
          mobileMenu.style.display = 'flex';
          navbar.classList.add('mobile-active');
        }
      };

      // Clean up old event listeners
      mobileToggle.removeEventListener('click', toggleHandler);
      
      // Add new event listener
      mobileToggle.addEventListener('click', toggleHandler);
      
      // Handle clicks outside mobile menu
      document.addEventListener('click', (e) => {
        if (mobileMenu.classList.contains('active') &&
            !mobileMenu.contains(e.target) &&
            !mobileToggle.contains(e.target)) {
          mobileMenu.classList.remove('active');
          mobileMenu.style.display = 'none';
          navbar.classList.remove('mobile-active');
        }
      });

      // Handle window resize
      window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
          mobileMenu.classList.remove('active');
          mobileMenu.style.display = 'none';
          navbar.classList.remove('mobile-active');
        }
      });
    }
  }

  toggleMobileMenu() {
    this.isMenuOpen = !this.isMenuOpen;
    const { mobileNav, mobileOverlay, mobileMenuToggle } = this.domElements;

    if (this.isMenuOpen) {
      mobileNav?.classList.add('active');
      mobileOverlay?.classList.add('active');
      mobileMenuToggle?.classList.add('active');
      mobileMenuToggle?.setAttribute('aria-expanded', 'true');
      document.body.classList.add('mobile-menu-open');
    } else {
      this.closeMobileMenu();
    }
  }

  closeMobileMenu() {
    this.isMenuOpen = false;
    const { mobileNav, mobileOverlay, mobileMenuToggle } = this.domElements;

    mobileNav?.classList.remove('active');
    mobileOverlay?.classList.remove('active');
    mobileMenuToggle?.classList.remove('active');
    mobileMenuToggle?.setAttribute('aria-expanded', 'false');
    document.body.classList.remove('mobile-menu-open');
  }

  setupDropdowns() {
    console.log('📋 Setting up dropdowns...');
    
    const dropdowns = document.querySelectorAll('.nav-dropdown');
    
    dropdowns.forEach(dropdown => {
      const toggle = dropdown.querySelector('[data-dropdown-toggle]');
      const menu = dropdown.querySelector('.nav-dropdown-menu');
      
      if (toggle && menu) {
        console.log('🔍 Found dropdown:', {
          toggle: toggle.className,
          menu: menu.className,
          parent: dropdown.className
        });

        // Remove any existing handlers
        if (this.dropdownHandlers.has(toggle)) {
          toggle.removeEventListener('click', this.dropdownHandlers.get(toggle));
          this.dropdownHandlers.delete(toggle);
        }

        // Create new handler
        const handler = (e) => {
          e.preventDefault();
          e.stopPropagation();
          
          console.log('🔘 Dropdown clicked:', dropdown.className);
          
          // Close all other dropdowns first
          dropdowns.forEach(otherDropdown => {
            if (otherDropdown !== dropdown) {
              otherDropdown.classList.remove('active');
              const otherToggle = otherDropdown.querySelector('[data-dropdown-toggle]');
              if (otherToggle) {
                otherToggle.setAttribute('aria-expanded', 'false');
              }
            }
          });

          // Toggle current dropdown
          const isActive = dropdown.classList.contains('active');
          dropdown.classList.toggle('active');
          toggle.setAttribute('aria-expanded', !isActive);
          
          // Force menu visibility
          if (!isActive) {
            menu.classList.add('force-visible');
            // Position the menu
            const toggleRect = toggle.getBoundingClientRect();
            menu.style.top = `${toggleRect.bottom + 8}px`;
          } else {
            menu.classList.remove('force-visible');
          }
          
          console.log('🔄 Dropdown state:', {
            isActive: !isActive,
            hasForceVisible: menu.classList.contains('force-visible')
          });
        };

        // Store and add new handler
        this.dropdownHandlers.set(toggle, handler);
        toggle.addEventListener('click', handler);
        
        // Add keyboard support
        toggle.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handler(e);
          }
        });
      }
    });
    
    console.log('✅ Dropdowns setup complete');
  }

  setupDocumentClickHandler() {
    // Remove existing handler if any
    if (this.documentClickHandler) {
      document.removeEventListener('click', this.documentClickHandler);
    }

    // Create new handler
    this.documentClickHandler = (e) => {
      const dropdowns = document.querySelectorAll('.nav-dropdown');
      dropdowns.forEach(dropdown => {
        if (!dropdown.contains(e.target)) {
          dropdown.classList.remove('active');
          const toggle = dropdown.querySelector('[data-dropdown-toggle]');
          if (toggle) {
            toggle.setAttribute('aria-expanded', 'false');
          }
        }
      });
    };

    // Add new handler
    document.addEventListener('click', this.documentClickHandler);
  }

  setupKeyboardNavigation() {
    console.log('⌨️ Setting up keyboard navigation...');
    
    const handleKeyDown = (e) => {
      // Handle Escape key
      if (e.key === 'Escape') {
        const dropdowns = document.querySelectorAll('.nav-dropdown');
        dropdowns.forEach(dropdown => {
          const menu = dropdown.querySelector('.nav-dropdown-menu');
          const toggle = dropdown.querySelector('.profile-dropdown-toggle');
          if (menu && toggle) {
            menu.style.display = 'none';
            toggle.setAttribute('aria-expanded', 'false');
            dropdown.classList.remove('active');
          }
        });
      }
    };

    // Add keyboard event listener
    document.addEventListener('keydown', handleKeyDown);
    this.eventListeners.push({
      element: document,
      type: 'keydown',
      handler: handleKeyDown
    });

    console.log('✅ Keyboard navigation setup complete');
  }

  setupMusicSystem() {
    console.log('🎵 Setting up music system integration...');
    // Initialize music system
    if (typeof window.initializeMusic === 'function') {
      window.initializeMusic();
    } else {
      // Wait for music functions
      setTimeout(() => {
        if (typeof window.initializeMusic === 'function') {
          window.initializeMusic();
        }
      }, 500);
    }
  }

  setupAuthListener() {
    // Listen for auth state changes
    window.addEventListener('authStateChange', (event) => {
      console.log('🔐 Auth state changed:', event.detail);
      if (event.detail.type === 'login' && event.detail.user) {
        console.log('✅ User logged in, updating navbar');
        this.updateUserDisplay(event.detail.user);
      } else if (event.detail.type === 'logout') {
        console.log('🚪 User logged out, updating navbar');
        this.updateUserDisplay(null);
      }
    });
    
    // Also listen for app ready event in case auth loads after navbar
    window.addEventListener('app:ready', () => {
      console.log('🚀 App ready event received, checking auth state');
      setTimeout(() => {
        if (window._appInstance) {
          const authManager = window._appInstance.getComponent('auth');
          if (authManager && authManager.getUser()) {
            console.log('✅ Auth data found after app ready, updating navbar');
            this.updateUserDisplay(authManager.getUser());
          }
        }
      }, 100);
    });
  }

  async loadUserData() {
    console.log('👤 Loading user data...');
    
    // Check if we're in Electron mode and authentication hasn't been established yet
    if (window.isElectronMode && !window.electronAuthEstablished) {
      console.log('🛑 User data load blocked in Electron mode - authentication not established');
      return;
    }
    
    try {
      // Try multiple sources for user data
      const authToken = localStorage.getItem('authToken');
      const api = window.apiClient;
      
      // First, try to get user data from AuthManager
      let userData = null;
      if (window._appInstance) {
        const authManager = window._appInstance.getComponent('auth');
        if (authManager && authManager.getUser()) {
          userData = authManager.getUser();
          console.log('✅ User data found in AuthManager');
        }
      }
      
      // If we have user data from AuthManager, use it immediately
      if (userData) {
        this.updateUserDisplay(userData);
        return userData;
      }
      
      // If we have auth token and API client, try to fetch user data
      if (authToken && api) {
        console.log('🔍 Fetching user data from API...');
        const response = await api.get(`/api/me?_t=${Date.now()}`);
        if (response && response.success) {
          console.log('✅ User data loaded from API');
          this.updateUserDisplay(response.data);
          return response.data;
        }
      }
      
      // If no auth token, retry after a delay
      if (!authToken) {
        console.log('⚠️ No auth token found, will retry in 2 seconds...');
        this.updateUserDisplay(null);
        
        // Retry after 2 seconds in case token is stored later
        setTimeout(() => {
          const retryToken = localStorage.getItem('authToken');
          if (retryToken) {
            console.log('🔄 Auth token found on retry, loading user data...');
            this.loadUserData();
          }
        }, 2000);
        
        return;
      }
      
      // If we get here, we have a token but API call failed
      console.log('⚠️ API call failed, showing guest state');
      this.updateUserDisplay(null);
      
    } catch (error) {
      console.error('❌ Error loading user data:', error);
      this.updateUserDisplay(null);
    }
  }

  updateUserDisplay(user) {
    console.log('👤 Updating user display...');
    
    // Debug admin link state
    const adminLink = this.domElements.adminLink;
    console.log('🔍 Admin link debug:', {
      user,
      adminLink: adminLink ? 'found' : 'not found',
      adminLinkDisplay: adminLink?.style.display,
      adminLinkElement: adminLink
    });

    if (user) {
      // Update profile image
      if (user.avatar && this.domElements.profileImages) {
        if (this.domElements.profileImages.desktop) {
          this.domElements.profileImages.desktop.src = user.avatar;
          console.log('✅ Updated desktop profile image');
        }
        if (this.domElements.profileImages.mobile) {
          this.domElements.profileImages.mobile.src = user.avatar;
          console.log('✅ Updated mobile profile image');
        }
      }

      // Update username
      const usernameElements = document.querySelectorAll('#navbar-username, #navbar-username-mobile');
      usernameElements.forEach(el => {
        if (el) el.textContent = user.username;
      });

      // Handle admin link visibility
      if (adminLink) {
        console.log('🐢 TURTLEMAN DEBUG:', {
          username: user.username,
          role: user.role,
          isAdmin: user.role === 'admin',
          roleType: typeof user.role,
          roleComparison: user.role === 'admin',
          fullUserObject: user
        });

        console.log('🔍 Admin check:', {
          role: user.role,
          username: user.username,
          isAdmin: user.role === 'admin'
        });

        console.log('🔍 Admin check details:', {
          hasUser: !!user,
          roleType: typeof user.role,
          role: user.role,
          isAdmin: user.role === 'admin'
        });

        if (user.role === 'admin') {
          adminLink.style.display = 'flex';
          adminLink.classList.add('show-admin');
          console.log('✅ Admin link shown for admin user');
        } else {
          adminLink.style.display = 'none';
          adminLink.classList.remove('show-admin');
        }
      }

      // Update avatar in navbar
      console.log('👤 Using database avatar for navbar:', user.avatar);
    } else {
      // Reset to default state if no user
      if (this.domElements.profileImages) {
        if (this.domElements.profileImages.desktop) {
          this.domElements.profileImages.desktop.src = '/assets/img/ranks/emblem.png';
        }
        if (this.domElements.profileImages.mobile) {
          this.domElements.profileImages.mobile.src = '/assets/img/ranks/emblem.png';
        }
      }

      const usernameElements = document.querySelectorAll('#navbar-username, #navbar-username-mobile');
      usernameElements.forEach(el => {
        if (el) el.textContent = 'Guest';
      });

      if (adminLink) {
        adminLink.style.display = 'none';
        adminLink.classList.remove('show-admin');
      }
    }

    console.log('✅ User display updated successfully');
  }

  // Helper method to set rank images
  setRankImages(profileImage, profileImageMobile, rankImageUrl, rankName) {
    if (profileImage) {
      profileImage.src = rankImageUrl;
      profileImage.alt = `${rankName} rank`;
      console.log('✅ Updated desktop profile image');
    }
    if (profileImageMobile) {
      profileImageMobile.src = rankImageUrl;
      profileImageMobile.alt = `${rankName} rank`;
      console.log('✅ Updated mobile profile image');
    }
  }

  // Cache management methods removed - using database avatar directly

  setDefaultImages(profileImage, profileImageMobile) {
    const defaultImageUrl = '/assets/img/ranks/emblem.png';
    if (profileImage) {
      profileImage.src = defaultImageUrl;
      profileImage.alt = 'Default profile';
    }
    if (profileImageMobile) {
      profileImageMobile.src = defaultImageUrl;
      profileImageMobile.alt = 'Default profile';
    }
  }

  async initNotifications() {
    console.log('🔔 Initializing notifications system...');
    
    // Wait for NotificationsManager to be available
    let attempts = 0;
    const maxAttempts = 50;
    
    while (attempts < maxAttempts && !window.notificationsManager) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
    
    if (!window.notificationsManager) {
      console.warn('⚠️ NotificationsManager not available after waiting');
      return;
    }
    
    // Initialize the notifications manager if not already initialized
    if (!window.notificationsManager.isInitialized) {
      try {
        await window.notificationsManager.init();
        console.log('✅ NotificationsManager initialized');
      } catch (error) {
        console.error('❌ Error initializing NotificationsManager:', error);
      }
    }
    
    console.log('✅ Notification system initialized');
  }

  setupLogoutHandling() {
    console.log('🔐 Setting up logout handling...');
    
    // Find all possible logout elements
    const logoutSelectors = [
      '.logout-item',
      '#logout-app', 
      'a[href*="logout"]',
      'button[onclick*="logout"]'
    ];
    
    let logoutElementsFound = 0;
    
    logoutSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      console.log(`🔍 LOGOUT SETUP: Found ${elements.length} elements for selector "${selector}"`);
      
      elements.forEach((element, index) => {
        logoutElementsFound++;
        console.log(`🔍 LOGOUT SETUP: Element ${index + 1} for "${selector}":`, {
          tag: element.tagName,
          class: element.className,
          href: element.href,
          text: element.textContent?.trim(),
          hasHandler: element.hasAttribute('data-logout-handler-attached')
        });
        
        // Skip if handler already attached
        if (element.hasAttribute('data-logout-handler-attached')) {
          console.log(`⏭️ LOGOUT SETUP: Skipping element ${index + 1} - handler already attached`);
          return;
        }
        
        // Create a new handler function to avoid closure issues
        const newHandler = (e) => {
          console.log(`🚪 LOGOUT TRIGGERED via selector "${selector}" element ${index + 1}`);
          console.log('🔍 LOGOUT EVENT: Element that triggered:', e.target);
          console.log('🔍 LOGOUT EVENT: Event details:', e);
          e.preventDefault();
          e.stopPropagation();
          this.handleLogout(e);
        };
        
        // Store handler reference on the element for proper cleanup later
        element._logoutHandler = newHandler;
        
        // Add the event listener (removeEventListener won't work without the exact same function reference)
        element.addEventListener('click', newHandler);
        
        // Mark element as having a handler attached
        element.setAttribute('data-logout-handler-attached', 'true');
        
        console.log(`✅ LOGOUT HANDLER: Attached to element ${index + 1} for "${selector}"`);
      });
    });
    
    console.log(`🔐 LOGOUT SETUP COMPLETE: Total ${logoutElementsFound} logout elements configured`);
    
    // Global logout click detector for debugging and fallback
    document.addEventListener('click', (e) => {
      const clickedElement = e.target.closest('a, button');
      if (!clickedElement) return;
      
      const text = clickedElement.textContent?.toLowerCase() || '';
      const href = clickedElement.href || '';
      const onclick = clickedElement.onclick || '';
      
      if (text.includes('logout') || href.includes('logout') || onclick.toString().includes('logout')) {
        const hasOurHandler = clickedElement.hasAttribute('data-logout-handler-attached');
        console.log('🚨 GLOBAL LOGOUT CLICK DETECTED:', {
          element: clickedElement,
          text: clickedElement.textContent,
          href: clickedElement.href,
          onclick: clickedElement.onclick,
          hasOurHandler: hasOurHandler,
          ourHandlersActive: logoutElementsFound > 0
        });
        
        // If our handler isn't attached, handle it manually
        if (!hasOurHandler) {
          console.log('🚨 MANUAL LOGOUT HANDLING - our handler not attached');
          e.preventDefault();
          e.stopPropagation();
          
          // Check if we have a navbar instance
          if (window.navbar && window.navbar.handleLogout) {
            console.log('✅ Using navbar.handleLogout');
            window.navbar.handleLogout(e);
          } else {
            console.log('❌ No navbar.handleLogout available, using fallback');
            // Fallback logout handling
            handleFallbackLogout(e);
          }
        }
      }
    });
    
    console.log('✅ Global logout detector installed');
    
    // Fallback logout function
    function handleFallbackLogout(e) {
      console.log('🔄 FALLBACK LOGOUT HANDLER CALLED');
      
      // Check if we're in Electron iframe
      const isInElectronIframe = window !== window.top && 
                                 (window.location.search.includes('electron=true') || 
                                  window.location.search.includes('electronApp='));
      
      console.log('🔍 Fallback environment check:', {
        isInElectronIframe,
        isTopWindow: window === window.top,
        searchParams: window.location.search
      });
      
      if (isInElectronIframe) {
        console.log('🛡️ FALLBACK: Electron iframe logout');
        
        // Clear auth data
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
        
        // Send logout message to parent
        try {
          window.parent.postMessage({
            type: 'ELECTRON_LOGOUT',
            timestamp: Date.now()
          }, '*');
          console.log('✅ Fallback: Electron logout message sent');
        } catch (error) {
          console.error('❌ Fallback: Failed to send logout message:', error);
        }
      } else {
        console.log('🌐 FALLBACK: Web browser logout');
        window.location.href = '/auth/logout';
      }
    }
  }

  setupNavigation() {
    console.log('🔗 Setting up navigation...');
    
    // Handle scroll-to links
    document.querySelectorAll('[data-scroll-to]').forEach(link => {
      link.addEventListener('click', async (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('data-scroll-to');
        const targetElement = document.getElementById(targetId);
        const href = link.getAttribute('href');
        
        // If we're on the same page and the target exists
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        } else if (href) {
          // Special handling for hash-based navigation in Electron
          if (href.includes('#') && (window.electron || window.isElectronMode)) {
            // Extract the base URL and hash
            const urlParts = href.split('#');
            const baseUrl = urlParts[0];
            const hash = urlParts[1];
            
            // Navigate to the base URL first, then scroll to the hash
            await this.handleNavigation(baseUrl, hash);
          } else {
            await this.handleNavigation(href);
          }
        }
      });
    });
    
    const navLinks = this.domElements.navLinks;
    if (!navLinks) return;

    navLinks.forEach(link => {
      // Clean up old handlers first
      const oldHandler = this.eventListeners.find(el => el.element === link)?.handler;
      if (oldHandler) {
        link.removeEventListener('click', oldHandler);
      }

      const handler = async (e) => {
        // Don't prevent default for admin link if it's hidden
        if (link.classList.contains('admin-link') && link.style.display === 'none') {
          return;
        }
        
        e.preventDefault();
        const href = link.getAttribute('href');
        
        if (href) {
          // Save current state (like music)
          if (window.saveMusicState) {
            window.saveMusicState();
          }
          
          await this.handleNavigation(href);
        }
      };

      // Add new handler
      link.addEventListener('click', handler);
      
      // Store for cleanup
      this.eventListeners.push({
        element: link,
        type: 'click',
        handler
      });

      console.log('✅ Navigation handler added for:', link.textContent.trim());
    });

    // Handle logout links separately
    const logoutLinks = document.querySelectorAll('.logout-item');
    logoutLinks.forEach(link => {
      const handler = (e) => {
        // Let the default behavior handle logout
        console.log('🔄 Processing logout...');
      };
      link.addEventListener('click', handler);
      this.eventListeners.push({
        element: link,
        type: 'click',
        handler: handler
      });
    });

    // Specifically handle admin link visibility
    const adminLink = this.domElements.adminLink;
    if (adminLink) {
      // Check if user is admin (you might want to adjust this based on your actual admin check)
      fetch('/api/me', {
        credentials: 'include',
        headers: {
          'Accept': 'application/json'
        }
      })
      .then(response => response.json())
      .then(user => {
        if (user.isAdmin) {
          adminLink.style.display = 'flex';
          adminLink.classList.add('show-admin');
        } else {
          adminLink.style.display = 'none';
          adminLink.classList.remove('show-admin');
        }
      })
      .catch(error => {
        console.error('Failed to check admin status:', error);
        adminLink.style.display = 'none';
      });
    }
  }

  // Helper method to handle navigation
  async handleNavigation(href, hash = null) {
    if (href.startsWith('http')) {
      const finalUrl = hash ? `${href}#${hash}` : href;
      window.location.href = finalUrl;
    } else {
      // Handle Electron environment
      if (window.electron || window.isElectronMode) {
        console.log('🧭 Electron navigation requested:', href);
        
        // Special handling for Blacksmith page (index.html)
        if (href === '/' || href === '/index.html' || href === 'index.html' || href === 'blacksmith') {
          console.log('⚒️ Navigating to Blacksmith page in Electron mode');
          if (window.electronAPI && window.electronAPI.navigation && window.electronAPI.navigation.loadPage) {
            try {
              const result = await window.electronAPI.navigation.loadPage('blacksmith');
              if (result && result.success) {
                console.log('✅ Blacksmith navigation successful via Electron API');
                return;
              }
            } catch (error) {
              console.error('❌ Blacksmith navigation error:', error);
            }
          }
          // Fallback for Blacksmith
          const basePath = 'http://127.0.0.1:3000';
          const electronParams = 'electron=true&electronApp=WC_Arena_Core';
          const finalUrl = `${basePath}/?${electronParams}`;
          window.location.href = finalUrl;
          return;
        }
        
        // Use Electron navigation API if available
        if (window.electronAPI && window.electronAPI.navigation && window.electronAPI.navigation.loadPage) {
          try {
            console.log('🚀 Using Electron navigation API for:', href);
            const result = await window.electronAPI.navigation.loadPage(href);
            if (result && result.success) {
              console.log('✅ Electron navigation successful');
              return;
            } else {
              console.warn('⚠️ Electron navigation failed, falling back to URL navigation');
            }
          } catch (error) {
            console.error('❌ Electron navigation error:', error);
          }
        }
        
        // Fallback to URL navigation for Electron
        const basePath = 'http://127.0.0.1:3000';
        const fullPath = href.startsWith('/') ? href : `/${href}`;
        const electronParams = 'electron=true&electronApp=WC_Arena_Core';
        const finalUrl = hash 
          ? `${basePath}${fullPath}?${electronParams}#${hash}`
          : `${basePath}${fullPath}?${electronParams}`;
        window.location.href = finalUrl;
      } else {
        // Regular web environment
        const basePath = window.location.origin;
        const fullPath = href.startsWith('/') ? href : `/${href}`;
        const finalUrl = hash ? `${basePath}${fullPath}#${hash}` : `${basePath}${fullPath}`;
        window.location.href = finalUrl;
      }
    }
  }

  updateCurrentPageMenuItem() {
    console.log('📍 Updating current page menu item...');
    
    // Get current page path
    const currentPath = window.location.pathname;
    console.log('Current path:', currentPath);
    
    // Define page mappings
    const pageMapping = {
      '/views/myprofile.html': 'My Profile',
      '/views/campaigns.html': 'War Table', 
      '/views/forum.html': 'Forum',
      '/views/chat.html': 'Live Chat',
      '/download': 'Download App'
    };
    
    // Setup logout handling for Electron
    this.setupLogoutHandling();
    
    // Find all dropdown menu items
    const dropdownItems = document.querySelectorAll('.nav-dropdown-item:not(.logout-item)');
    const mobileNavItems = document.querySelectorAll('.mobile-nav-item:not(.logout-item)');
    
    // Check both desktop and mobile menu items
    [...dropdownItems, ...mobileNavItems].forEach(item => {
      const href = item.getAttribute('href');
      
      if (href && currentPath.includes(href)) {
        // Add current page styling
        item.classList.add('current-page');
        item.style.pointerEvents = 'none';
        
        // Add "Currently viewing" text
        const textContent = item.textContent.trim();
        if (!textContent.includes('Currently viewing')) {
          const textSpan = item.querySelector('span') || item;
          if (textSpan.textContent) {
            textSpan.textContent = `${textSpan.textContent} (Current)`;
          }
        }
        
        console.log(`✅ Marked as current page: ${href}`);
      } else {
        // Remove current page styling if it exists
        item.classList.remove('current-page');
        item.style.pointerEvents = '';
        
        // Remove "Current" text if it exists
        const textSpan = item.querySelector('span') || item;
        if (textSpan.textContent && textSpan.textContent.includes(' (Current)')) {
          textSpan.textContent = textSpan.textContent.replace(' (Current)', '');
        }
      }
    });
  }

  // Cleanup method to prevent memory leaks
  destroy() {
    console.log('🧹 Cleaning up navbar event listeners...');
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    this.eventListeners = [];
    
    console.log('✅ Navbar cleanup complete');
  }

  /**
   * Refresh notifications (called by achievement system)
   */
  async refreshNotifications() {
    console.log('🔔 ModernNavbar: Refreshing notifications...');
    
    if (window.notificationsManager && typeof window.notificationsManager.loadNotifications === 'function') {
      await window.notificationsManager.loadNotifications();
      console.log('✅ ModernNavbar: Notifications refreshed');
    }
  }

  async handleLogout(e) {
    console.log('🚪 FRONTEND LOGOUT HANDLER CALLED!');
    console.log('🔍 Event details:', e);
    console.log('🔍 Target element:', e.target);
    console.log('🔍 Current URL:', window.location.href);
    console.log('🔍 Window parent check:', window !== window.top);
    
    // Prevent multiple logout attempts
    if (window.isLoggingOut) {
      console.log('🛑 Logout already in progress, ignoring duplicate request');
      return;
    }
    
    // Set logout flag to prevent API calls
    window.isLoggingOut = true;
    
    // Check if we're in an Electron iframe
    const isInElectronIframe = window !== window.top && 
                               (window.location.search.includes('electron=true') || 
                                window.location.search.includes('electronApp='));
    
    console.log('🔍 Environment check:', {
      isInElectronIframe,
      isTopWindow: window === window.top,
      searchParams: window.location.search,
      hasParent: !!window.parent,
      parentSame: window.parent === window
    });
    
    // Show immediate visual feedback
    const logoutButton = e.target.closest('button') || e.target;
    const originalText = logoutButton.textContent;
    const originalDisabled = logoutButton.disabled;
    
    if (logoutButton) {
      logoutButton.textContent = 'Logging out...';
      logoutButton.disabled = true;
    }
    
    // Check if we're in Electron
    const userAgent = navigator.userAgent;
    const isElectron = userAgent.includes('Electron') || !!(window.electronAPI && window.electronAPI.invoke);
    console.log('🔍 Environment detected:', { isElectron, userAgent: userAgent.substring(0, 100) });
    
    // Handle Electron iframe logout differently
    if (isInElectronIframe) {
      console.log('🛡️ ELECTRON IFRAME LOGOUT - Using postMessage to parent');
      
      // Clear authentication data immediately
      this.clearAuthenticationData();
      
      // Show navbar as logged out immediately
      this.showLoggedOutState();
      
      // Send logout message to parent Electron window
      try {
        console.log('📤 Sending ELECTRON_LOGOUT message to parent...');
        const message = {
          type: 'ELECTRON_LOGOUT',
          timestamp: Date.now()
        };
        console.log('📤 Message content:', message);
        
        window.parent.postMessage(message, '*');
        console.log('✅ Electron logout message sent to parent window');
        
        // Wait a moment for the logout to be processed
        console.log('⏳ Waiting for logout processing...');
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('⏳ Wait completed');
        
      } catch (error) {
        console.error('❌ Failed to send Electron logout message:', error);
      }
      
      // CRITICAL: Don't navigate or call server logout - let the parent handle everything
      // The parent will clear auth data and redirect to login page
      console.log('🛑 Electron iframe logout complete - parent will handle redirect');
      
      // Prevent any further navigation or API calls
      window.isLoggingOut = false;
      return;
    }
    
    if (isElectron) {
      console.log('🛡️ ELECTRON LOGOUT - Starting immediate cleanup');
      
      // IMMEDIATE AUTH CLEANUP - Frontend first
      this.clearAuthenticationData();
      
      // Show navbar as logged out immediately
      this.showLoggedOutState();
      
      // CRITICAL: Clear Electron app's stored authentication data
      const isIframe = window !== window.top;
      console.log('🔍 Checking electronAPI availability:', {
        hasElectronAPI: !!(window.electronAPI),
        hasAuth: !!(window.electronAPI && window.electronAPI.auth),
        hasLogout: !!(window.electronAPI && window.electronAPI.auth && window.electronAPI.auth.logout),
        electronAPIKeys: window.electronAPI ? Object.keys(window.electronAPI) : [],
        authKeys: window.electronAPI && window.electronAPI.auth ? Object.keys(window.electronAPI.auth) : [],
        isIframe: isIframe
      });
      
      // For iframe context, use postMessage to communicate with parent Electron window
      if (isIframe && isElectron) {
        console.log('🧹 Clearing Electron app authentication data via postMessage...');
        try {
          // Send logout message to parent Electron window
          window.parent.postMessage({
            type: 'ELECTRON_LOGOUT',
            timestamp: Date.now()
          }, '*');
          console.log('✅ Electron logout message sent to parent window');
          
          // Wait a moment for the logout to be processed
          await new Promise(resolve => setTimeout(resolve, 200));
          
        } catch (error) {
          console.error('❌ Failed to send Electron logout message:', error);
        }
      } else if (window.electronAPI && window.electronAPI.auth && typeof window.electronAPI.auth.logout === 'function') {
        console.log('🧹 Clearing Electron app authentication data via electronAPI...');
        try {
          const logoutResult = await window.electronAPI.auth.logout();
          console.log('✅ Electron app logout successful:', logoutResult);
          
          // Give Electron a moment to fully process the logout
          await new Promise(resolve => setTimeout(resolve, 100));
          
        } catch (error) {
          console.error('❌ Electron app logout failed:', error);
        }
      } else {
        console.warn('⚠️ electronAPI.auth.logout not available - debugging info:', {
          electronAPI: !!window.electronAPI,
          auth: !!(window.electronAPI && window.electronAPI.auth),
          logout: !!(window.electronAPI && window.electronAPI.auth && window.electronAPI.auth.logout),
          logoutType: window.electronAPI && window.electronAPI.auth ? typeof window.electronAPI.auth.logout : 'undefined',
          isIframe: isIframe
        });
      }
      
      // For iframe context, don't call server logout - parent handles everything
      if (isIframe) {
        console.log('🛑 Electron iframe logout complete - parent will handle server logout and redirect');
        return;
      }
      
      // Only call server logout for non-iframe Electron contexts
      console.log('🔄 Attempting server logout for non-iframe Electron context...');
      try {
        await this.attemptServerLogout();
        console.log('✅ Server logout completed successfully');
        // attemptServerLogout will handle the redirect
      } catch (error) {
        console.warn('⚠️ Server logout had issues:', error);
        // If server logout fails, still redirect to login page
        console.log('🔄 Redirecting to login page after logout error...');
        window.location.href = '/views/login.html';
      }
      
    } else {
      console.log('🌐 Web browser - using direct logout redirect');
      this.clearAuthenticationData();
      window.location.href = '/auth/logout';
    }
    
    // Reset logout flag after a delay
    setTimeout(() => {
      window.isLoggingOut = false;
    }, 5000);
  }
  
  clearAuthenticationData() {
    console.log('🧹 Clearing authentication data...');
    try {
      // Clear localStorage
      const keysToRemove = ['authToken', 'user', 'lastProfileRefresh'];
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });
      
      // Clear navbar profile cache
      localStorage.removeItem('navbar_profile_cache');
      localStorage.removeItem('navbar_profile_timestamp');
      
      // Clear sessionStorage caches
      sessionStorage.removeItem('user_session_cache');
      sessionStorage.removeItem('user_session_timestamp');
      
      // Clear cookies
      const cookiesToClear = ['authToken', 'sessionId', 'connect.sid', 'session', 'token'];
      cookiesToClear.forEach(cookieName => {
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=localhost;`;
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      });
      
      console.log('✅ Authentication data cleared');
    } catch (error) {
      console.error('❌ Error clearing auth data:', error);
    }
  }
  
  showLoggedOutState() {
    console.log('🔄 Updating navbar to logged-out state');
    try {
      // Update username displays
      const usernameElements = document.querySelectorAll('#navbar-username, #navbar-username-mobile');
      usernameElements.forEach(el => {
        if (el) el.textContent = 'Guest';
      });
      
      // Reset profile images
      const profileImages = document.querySelectorAll('#profile-image, #profile-image-mobile');
      profileImages.forEach(img => {
        if (img) img.src = '/assets/img/default-avatar.svg';
      });
      
      // Hide admin links
      const adminLinks = document.querySelectorAll('#admin-link');
      adminLinks.forEach(link => {
        if (link) link.style.display = 'none';
      });
      
      console.log('✅ Navbar updated to logged-out state');
    } catch (error) {
      console.error('❌ Error updating navbar state:', error);
    }
  }

  async attemptServerLogout() {
    console.log('🔄 Attempting server logout...');
    
    // Check if we're in Electron iframe context
    const isInElectronIframe = window !== window.top && 
                               (window.location.search.includes('electron=true') || 
                                window.location.search.includes('electronApp='));
    
    if (isInElectronIframe) {
      console.log('🛑 Server logout blocked - in Electron iframe context');
      console.log('🛑 Parent window should handle logout');
      return;
    }
    
    try {
      const api = window.apiClient;
      const response = await Promise.race([
        api.get('/auth/logout'),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Server logout timeout')), 1000)
        )
      ]);
      
      if (response) {
        const data = response;
        console.log('✅ Server logout successful:', data);
        
        // After successful logout, redirect to login page
        console.log('🔄 Redirecting to login page after successful logout...');
        window.location.href = '/views/login.html';
        return;
      } else {
        console.warn('⚠️ Server logout failed:', response.status);
      }
      
    } catch (error) {
      console.warn('⚠️ Server logout error:', error.message);
    }
    
    console.log('🔄 Server logout attempt completed');
    
    // If we get here, redirect to login page anyway
    console.log('🔄 Redirecting to login page after logout attempt...');
    window.location.href = '/views/login.html';
  }
}

// Global logout handler for compatibility with onclick attributes
window.handleGlobalLogout = function(event) {
  event.preventDefault();
  console.log('🚪 Global logout handler called');
  
  // Check if we're in Electron iframe
  const isInElectronIframe = window !== window.top && 
                             (window.location.search.includes('electron=true') || 
                              window.location.search.includes('electronApp='));
  
  if (isInElectronIframe) {
    console.log('🛡️ Electron iframe logout via global handler');
    // Send logout message to parent
    window.parent.postMessage({
      type: 'ELECTRON_LOGOUT',
      timestamp: Date.now()
    }, '*');
    return;
  }
  
  // Use navbar logout handler if available
  if (window.navbar && window.navbar.handleLogout) {
    console.log('🎯 Using navbar logout handler');
    window.navbar.handleLogout(event);
    return;
  }
  
  // Fallback for web browser
  console.log('🌐 Web browser logout via global handler');
  window.location.href = '/auth/logout';
};

// Initialize navbar when script loads
window.initModernNavbar = async function() {
    console.log('🚀 Initializing ModernNavbar...');
    
    // Prevent multiple initializations
    if (window.navbarInitializationInProgress) {
        console.log('⏳ Navbar initialization already in progress, skipping...');
        return;
    }
    
    if (!window.navbar) {
        window.navbarInitializationInProgress = true;
        try {
            window.navbar = new ModernNavbar();
            await window.navbar.init();
            console.log('✅ ModernNavbar initialized');
        } finally {
            window.navbarInitializationInProgress = false;
        }
    } else if (window.navbar && !window.navbar.initialized) {
        window.navbarInitializationInProgress = true;
        try {
            console.log('🔄 Re-initializing existing navbar instance...');
            await window.navbar.init();
            console.log('✅ ModernNavbar re-initialized');
        } finally {
            window.navbarInitializationInProgress = false;
        }
    } else {
        console.log('⚠️ ModernNavbar already initialized');
    }
};

// Auto-initialize if document is ready
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    window.initModernNavbar();
} else {
    document.addEventListener('DOMContentLoaded', window.initModernNavbar);
}

// Retry initialization if navbar elements aren't found initially
setTimeout(() => {
    if (!window.navbar) {
        console.log('🔄 Retrying navbar initialization...');
        window.initModernNavbar();
    }
}, 2000);

// Additional retry for logout elements
setTimeout(() => {
    const logoutElements = document.querySelectorAll('.logout-item');
    if (logoutElements.length > 0 && window.navbar) {
        console.log('🔄 Re-setting up logout handling...');
        window.navbar.setupLogoutHandling();
    }
}, 3000);

// Enhance the destroy method to properly clean up event listeners
ModernNavbar.prototype.destroy = function() {
  console.log('🧹 Starting navbar cleanup...');
  
  // Clean up event listeners
  this.eventListeners.forEach(({ element, event, handler }) => {
    if (element && element.removeEventListener) {
      element.removeEventListener(event, handler);
    }
  });
  this.eventListeners = [];
  
  // Clean up logout handlers
  const logoutElements = [
    ...document.querySelectorAll('.logout-item'),
    ...document.querySelectorAll('#logout-app'),
    ...document.querySelectorAll('a[href*="logout"]'),
    ...document.querySelectorAll('button[onclick*="logout"]')
  ];
  
  logoutElements.forEach(element => {
    if (element._logoutHandler) {
      element.removeEventListener('click', element._logoutHandler);
      delete element._logoutHandler;
    }
  });
  
  // Reset state
  this.isMenuOpen = false;
  this.domElements = {};
  this.musicSystem = null;
  
  console.log('✅ Navbar cleanup complete');
};

// Enhance the setupLogoutHandling method to track handlers
ModernNavbar.prototype.setupLogoutHandling = function() {
  console.log('🔐 Setting up logout handling...');
  
  // Clean up existing handlers first
  const existingHandlers = document.querySelectorAll('[data-logout-handler]');
  let cleanupCount = 0;
  existingHandlers.forEach(element => {
    if (element._logoutHandler) {
      element.removeEventListener('click', element._logoutHandler);
      delete element._logoutHandler;
      cleanupCount++;
    }
  });
  console.log(`🧹 Cleaned up ${cleanupCount} existing logout handlers`);
  
  const setupLogoutHandler = (element) => {
    if (element.hasAttribute('data-logout-handler')) {
      console.log('⏭️ Skipping element - handler already attached');
      return;
    }
    
    const newHandler = async (e) => {
      e.preventDefault();
      await this.handleLogout(e);
    };
    
    element._logoutHandler = newHandler;
    element.addEventListener('click', newHandler);
    element.setAttribute('data-logout-handler', 'true');
    console.log('✅ LOGOUT HANDLER: Attached to element');
  };
  
  // Set up handlers for all logout elements
  [
    '.logout-item',
    '#logout-app',
    'a[href*="logout"]',
    'button[onclick*="logout"]'
  ].forEach(selector => {
    const elements = document.querySelectorAll(selector);
    console.log(`🔍 LOGOUT SETUP: Found ${elements.length} elements for selector "${selector}"`);
    
    elements.forEach((element, index) => {
      console.log(`🔍 LOGOUT SETUP: Element ${index + 1} for "${selector}":`, element);
      setupLogoutHandler(element);
    });
  });
  
  // Install global logout detector
  if (!window._globalLogoutDetector) {
    window._globalLogoutDetector = (e) => {
      const target = e.target.closest('[data-logout-handler]');
      if (target && target._logoutHandler) {
        target._logoutHandler(e);
      }
    };
    document.addEventListener('click', window._globalLogoutDetector);
  }
  console.log('✅ Global logout detector installed');
};

// Handle navigation clicks
document.addEventListener('click', async (e) => {
  const navLink = e.target.closest('a[href]:not([href^="#"]):not([href^="javascript"]):not(.logout-item)');
  
  if (navLink) {
    const href = navLink.getAttribute('href');
    const isExternal = href.startsWith('http') || href.startsWith('//');
    const isDownload = navLink.hasAttribute('download');
    const isMailto = href.startsWith('mailto:');
    const isTel = href.startsWith('tel:');
    
    // Don't handle special links
    if (isExternal || isDownload || isMailto || isTel) {
      return;
    }
    
    // Prevent default navigation
    e.preventDefault();
    
    // Get current auth state
    const authToken = localStorage.getItem('authToken');
    const isAuthenticated = !!authToken;
    
    // Save current scroll position
    const scrollPosition = {
      x: window.scrollX,
      y: window.scrollY
    };
    localStorage.setItem('scrollPosition', JSON.stringify(scrollPosition));
    
    // Navigate using history API
    try {
      history.pushState(null, '', href);
      
      // Update Electron mode flags if the function exists
      if (typeof window.updateElectronModeFlags === 'function') {
        console.log('🔄 Updating Electron mode flags after navigation');
        window.updateElectronModeFlags();
      }
      
      window.dispatchEvent(new PopStateEvent('popstate'));
    } catch (error) {
      console.error('Navigation error:', error);
      window.location.href = href;
    }
  }
});

// Handle back/forward navigation
window.addEventListener('popstate', () => {
  // Restore scroll position if available
  const savedPosition = localStorage.getItem('scrollPosition');
  if (savedPosition) {
    const { x, y } = JSON.parse(savedPosition);
    window.scrollTo(x, y);
  }
});

console.log('✅ Modern navbar script loaded'); 