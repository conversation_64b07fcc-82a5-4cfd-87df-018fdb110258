/**
 * Barracks Manager Module
 * Handles game type filtering and player display in the Barracks section
 */

export class BarracksManager {
  constructor() {
    this.currentFilter = 'all';
    this.players = [];
    this.filteredPlayers = [];
    this.initialized = false;
  }

  /**
   * Initialize the Barracks manager
   */
  async init() {
    console.log('🏰 Initializing Barracks Manager...');
    
    try {
      this.setupGameTypeFilters();
      this.initialized = true;
      console.log('✅ Barracks Manager initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Barracks Manager:', error);
      throw error;
    }
  }

  /**
   * Setup game type filter tabs
   */
  setupGameTypeFilters() {
    const gameTypeTabs = document.querySelectorAll('.game-type-tab');
    
    gameTypeTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        const gameType = tab.dataset.gameType;
        this.filterByGameType(gameType);
      });
    });

    console.log('🎮 Game type filters setup complete');
  }

  /**
   * Filter players by game type
   * @param {string} gameType - The game type to filter by ('all', 'war1', 'war2', 'war3')
   */
  filterByGameType(gameType) {
    console.log(`🔍 Filtering players by game type: ${gameType}`);
    
    // Update active tab
    document.querySelectorAll('.game-type-tab').forEach(tab => {
      tab.classList.remove('active');
      if (tab.dataset.gameType === gameType) {
        tab.classList.add('active');
      }
    });

    this.currentFilter = gameType;
    this.updatePlayerDisplay();
  }

  /**
   * Update player display based on current filter
   */
  updatePlayerDisplay() {
    const container = document.getElementById('player-names-container');
    if (!container) return;

    // Filter players based on current filter
    if (this.currentFilter === 'all') {
      this.filteredPlayers = [...this.players];
    } else {
      // Handle both old and new game type naming conventions
      this.filteredPlayers = this.players.filter(player => {
        if (this.currentFilter === 'war1') {
          return player.gameType === 'war1' || player.gameType === 'warcraft1';
        } else if (this.currentFilter === 'war2') {
          return player.gameType === 'war2' || player.gameType === 'warcraft2';
        } else if (this.currentFilter === 'war3') {
          return player.gameType === 'war3' || player.gameType === 'warcraft3';
        }
        return player.gameType === this.currentFilter;
      });
    }

    // Clear container
    container.innerHTML = '';

    // Show welcome message if no players at all
    if (this.players.length === 0) {
      const gameTypeNames = {
        'war1': 'Warcraft: Orcs & Humans',
        'war2': 'Warcraft II',
        'war3': 'Warcraft III'
      };
      
      container.innerHTML = `
        <div class="no-players-welcome">
          <div class="welcome-icon">
            <i class="fas fa-fort-awesome"></i>
          </div>
          <h3>Welcome to Your Barracks!</h3>
          <p>Your barracks is currently empty. Get started by linking your player names to track your competitive journey across all Warcraft games.</p>
          <div class="welcome-benefits">
            <div class="benefit-item">
              <i class="fas fa-chart-line"></i>
              <span>Track your MMR and rank progression</span>
            </div>
            <div class="benefit-item">
              <i class="fas fa-trophy"></i>
              <span>View detailed match statistics</span>
            </div>
            <div class="benefit-item">
              <i class="fas fa-users"></i>
              <span>Compare performance across game types</span>
            </div>
          </div>
          <div class="welcome-actions">
            <button class="epic-btn primary" onclick="document.getElementById('add-player-btn').click()">
              <i class="fas fa-plus"></i>
              <span>Add Your First Player</span>
            </button>
          </div>
        </div>
      `;
      return;
    }

    // Show filtered players
    if (this.filteredPlayers.length === 0) {
      const gameTypeNames = {
        'war1': 'Warcraft: Orcs & Humans',
        'war2': 'Warcraft II',
        'war3': 'Warcraft III'
      };
      
      const gameTypeName = gameTypeNames[this.currentFilter] || 'this game type';
      
      container.innerHTML = `
        <div class="no-players-message">
          <i class="fas fa-user-plus"></i>
          <p>No players found for ${gameTypeName}</p>
          <p>Create a player or link an existing one to get started!</p>
          <div class="filter-actions">
            <button class="epic-btn secondary" onclick="document.getElementById('add-player-btn').click()">
              <i class="fas fa-plus"></i>
              <span>Add ${gameTypeName} Player</span>
            </button>
          </div>
        </div>
      `;
      return;
    }

    // Create player cards
    this.filteredPlayers.forEach(player => {
      const playerCard = this.createPlayerCard(player);
      container.appendChild(playerCard);
    });

    console.log(`📊 Displaying ${this.filteredPlayers.length} players (filtered from ${this.players.length})`);
  }

  /**
   * Create a player card element
   * @param {Object} player - Player data
   * @returns {HTMLElement} Player card element
   */
  createPlayerCard(player) {
    const playerCard = document.createElement('div');
    playerCard.className = 'player-card';
    playerCard.dataset.gameType = player.gameType;

    // Calculate stats - handle nested stats structure
    const stats = player.stats?.stats || player.stats || {};
    const totalMatches = stats.totalMatches || 0;
    const wins = stats.wins || 0;
    const losses = stats.losses || 0;
    const winRate = totalMatches > 0 ? Math.round((wins / totalMatches) * 100) : 0;

    // Get rank info
    const rankImage = player.rank?.image || '/assets/img/ranks/b1.png';
    const rankName = player.rank?.name || 'Bronze 1';

    // Game type display names
    const gameTypeNames = {
      'war1': 'WC1',
      'warcraft1': 'WC1',
      'war2': 'WC2',
      'warcraft2': 'WC2', 
      'war3': 'WC3',
      'warcraft3': 'WC3'
    };

    // Check if this is a WC1 player for special handling
    const isWC1 = player.gameType === 'war1' || player.gameType === 'warcraft1';
    
    playerCard.innerHTML = `
      <div class="player-header">
        <div class="player-name">${player.name}</div>
        <div class="player-game-type">${gameTypeNames[player.gameType] || 'Unknown'}${isWC1 ? ' vs AI' : ''}</div>
      </div>
      <div class="player-rank-section">
        <img src="${rankImage}" alt="${rankName}" class="player-rank-image" 
             onerror="this.src='/assets/img/ranks/b1.png'">
        <div class="rank-info">
          <div class="rank-name">${rankName}</div>
          <div class="player-mmr">${player.mmr || 1500} MMR</div>
        </div>
      </div>
      <div class="player-stats">
        <div class="stat-item">
          <span class="stat-value">${totalMatches}</span>
          <span class="stat-label">Matches</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">${wins}/${losses}</span>
          <span class="stat-label">W/L</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">${winRate}%</span>
          <span class="stat-label">Win Rate</span>
        </div>
      </div>
      ${isWC1 ? `
        <div class="wc1-info">
          <p><i class="fas fa-sync-alt"></i> Character name synced with account username</p>
        </div>
      ` : ''}
      <div class="player-actions">
        <button class="btn btn-view-stats" data-player-id="${player._id}">
          <i class="fas fa-chart-line"></i> View Stats
        </button>
      </div>
    `;

    // Add event listener for view stats button
    const viewStatsBtn = playerCard.querySelector('.btn-view-stats');
    viewStatsBtn.addEventListener('click', () => {
      this.showPlayerStats(player.name); // Pass player name instead of ID
    });

    return playerCard;
  }

  /**
   * Show player statistics modal
   * @param {string} playerName - Player name
   */
  async showPlayerStats(playerName) {
    try {
      console.log(`🎯 BarracksManager.showPlayerStats called with: "${playerName}"`);
      console.log(`🎯 Available functions:`);
      console.log(`   - window.showPlayerDetails: ${!!window.showPlayerDetails}`);
      console.log(`   - window.openPlayerDetailsModal: ${!!window.openPlayerDetailsModal}`);
      console.log(`   - window.modalManager: ${!!window.modalManager}`);
      
      // Find player data
      const player = this.players.find(p => p.name === playerName);
      if (!player) {
        console.error('❌ Player not found:', playerName);
        console.log('🔍 Available players:', this.players.map(p => p.name));
        return;
      }

      console.log('✅ Player found:', player);

      // Use existing player stats modal functionality
      if (window.showPlayerDetails) {
        console.log('🎯 Calling window.showPlayerDetails...');
        window.showPlayerDetails(playerName); // Pass player name
      } else if (window.openPlayerDetailsModal) {
        console.log('🎯 Calling window.openPlayerDetailsModal...');
        window.openPlayerDetailsModal(playerName);
      } else {
        console.error('❌ No player details modal functions available');
        console.log('🔧 Available window functions:', Object.keys(window).filter(key => key.includes('player') || key.includes('modal')));
      }
    } catch (error) {
      console.error('❌ Error in showPlayerStats:', error);
    }
  }

  /**
   * Update players data
   * @param {Array} players - Array of player objects
   */
  updatePlayers(players) {
    console.log(`🔄 Updating players data: ${players.length} players`);
    
    this.players = players || [];
    
    // Check for WC1 username synchronization needs
    this.checkWC1UsernamSync();
    
    this.updatePlayerDisplay();
    this.updateFilterCounts();
  }

  /**
   * Check if WC1 players need username synchronization
   */
  async checkWC1UsernamSync() {
    const wc1Players = this.players.filter(p => p.gameType === 'warcraft1' || p.gameType === 'war1');
    
    // Get current user to compare username
    try {
      const response = await fetch('/api/me', { 
        headers: window.getAuthHeaders ? window.getAuthHeaders() : {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'include' 
      });
      if (response.ok) {
        const currentUser = await response.json();
        
        for (const player of wc1Players) {
          if (player.name !== currentUser.username) {
            console.log(`⚠️ WC1 player "${player.name}" doesn't match username "${currentUser.username}"`);
            await this.syncWC1Username();
            break; // Only need to sync once
          }
        }
      }
    } catch (error) {
      console.error('❌ Error checking WC1 username sync:', error);
    }
  }

  /**
   * Sync WC1 player name with current username
   */
  async syncWC1Username() {
    try {
      console.log('🔄 Syncing WC1 username...');
      const response = await fetch('/api/ladder/wc1/sync-username', {
        method: 'PUT',
        headers: window.getAuthHeaders ? window.getAuthHeaders() : {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ WC1 username synced:', data);
        
        // Reload players data to reflect changes
        if (window.profileUIManager && window.profileUIManager.loadPlayerNames) {
          window.profileUIManager.loadPlayerNames();
        }
      } else {
        const error = await response.json();
        console.error('❌ Failed to sync WC1 username:', error);
      }
    } catch (error) {
      console.error('❌ Error syncing WC1 username:', error);
    }
  }

  /**
   * Update filter tab counts
   */
  updateFilterCounts() {
    const counts = {
      all: this.players.length,
      war1: this.players.filter(p => p.gameType === 'war1' || p.gameType === 'warcraft1').length,
      war2: this.players.filter(p => p.gameType === 'war2' || p.gameType === 'warcraft2').length,
      war3: this.players.filter(p => p.gameType === 'war3' || p.gameType === 'warcraft3').length
    };

    // Update tab text with counts
    document.querySelectorAll('.game-type-tab').forEach(tab => {
      const gameType = tab.dataset.gameType;
      const count = counts[gameType] || 0;
      const span = tab.querySelector('span');
      
      if (span) {
        const baseText = span.textContent.split(' (')[0]; // Remove existing count
        span.textContent = count > 0 ? `${baseText} (${count})` : baseText;
      }
    });

    console.log('📊 Filter counts updated:', counts);
  }

  /**
   * Get current filter status
   */
  getFilterStatus() {
    return {
      currentFilter: this.currentFilter,
      totalPlayers: this.players.length,
      filteredPlayers: this.filteredPlayers.length,
      initialized: this.initialized
    };
  }

  /**
   * Reset filters to show all players
   */
  resetFilters() {
    this.filterByGameType('all');
  }
} 