const { BrowserWindow } = require('electron');
const axios = require('axios');
const crypto = require('crypto');
const path = require('path');
const { app, shell } = require('electron');

class AuthManager {
  constructor(store) {
    this.store = store;
    this.serverUrl = store.get('serverUrl') || 'http://127.0.0.1:3001';
    this.user = null;
    this._isAuthenticated = false;
    this.pendingAuthPromise = null;
    this.currentOAuthState = null;
    this.oauthState = null;
    this.mainWindow = null;

    
    console.log('AuthManager initialized with server URL:', this.serverUrl);
  }

  updateServerUrl(url) {
    this.serverUrl = url;
  }

  getAuthStatus() {
    return {
      isAuthenticated: this._isAuthenticated,
      user: this.user,
      serverUrl: this.serverUrl
    };
  }

  async validateStoredUser(storedUser) {
    try {
      // Get the stored token
      const token = this.store.get('authToken');
      if (!token) {
        console.log('❌ No stored token found for user validation');
        // Clear ALL invalid stored data
        this.clearAllAuthData();
        return false;
      }

      // Try to validate the token with the server
      console.log('🔍 Validating stored token with server...');
      const response = await axios.get(`${this.serverUrl}/auth/electron/verify`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Electron-Client': 'true',
          'X-Session-Isolation': 'true'
        },
        timeout: 10000
      });

      if (response.data.success && response.data.user) {
        console.log('✅ Stored token validated successfully');
        this.user = response.data.user;
        this._isAuthenticated = true;
        return true;
      }
      
      console.log('❌ Server rejected the stored token');
      // Clear ALL invalid stored data
      this.clearAllAuthData();
      return false;
    } catch (error) {
      console.log('❌ User validation failed:', error.message);
      // Clear ALL invalid stored data on any error
      this.clearAllAuthData();
      return false;
    }
  }

  // Helper method to ensure consistent auth data cleanup
  clearAllAuthData() {
    console.log('🧹 Clearing all stored authentication data...');
    this.store.delete('user');
    this.store.delete('authToken');
    this.store.delete('rememberLogin');
    this.user = null;
    this._isAuthenticated = false;
    console.log('✅ All authentication data cleared');
  }

  async loginWithProvider(provider) {
    console.log(`🔐 Starting ${provider} OAuth login flow`);
    
    try {
        console.log('🔍 DEBUGGING: Main window available:', !!this.mainWindow);
        console.log('🔍 DEBUGGING: Server URL:', this.serverUrl);
        
        // Create embedded OAuth window
        console.log('🔧 DEBUGGING: Creating OAuth window with settings:', {
            width: 500,
            height: 700,
            show: false,
            parent: !!this.mainWindow,
            modal: false,
            resizable: false,
            minimizable: false,
            maximizable: false
        });
        
        const authWindow = new BrowserWindow({
            width: 500,
            height: 700,
            show: false,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                webSecurity: true,
                allowRunningInsecureContent: false,
                experimentalFeatures: false
            },
            parent: this.mainWindow,
            modal: false, // Changed from true to false to prevent blocking
            resizable: false,
            minimizable: false,
            maximizable: false,
            title: `Sign in with ${provider.charAt(0).toUpperCase() + provider.slice(1)}`
        });
        
        console.log('✅ DEBUGGING: OAuth window created successfully');
        
        // Set custom user agent for OAuth window to identify as WC Arena Core
        authWindow.webContents.setUserAgent('WC_Arena_Core/1.0 (Electron)');
        
        console.log('✅ DEBUGGING: Embedded browser window created successfully');
        
        // Set custom user agent for OAuth window to identify as WC Arena Core
        authWindow.webContents.setUserAgent('WC_Arena_Core/1.0 (Electron)');

        // Generate state for CSRF protection and Electron detection
        const clientState = this.generateRandomString(32);
        this.pendingState = clientState;
        
        // Create state object for backend to detect Electron
        const stateObject = {
          isElectron: true,
          clientState: clientState,
          embedded: true
        };
        const stateParam = JSON.stringify(stateObject);

        // Build OAuth URL for embedded window
        const oauthUrl = `${this.serverUrl}/auth/${provider}?electron=true&embedded=true&state=${encodeURIComponent(stateParam)}`;
        
        console.log(`🌐 DEBUGGING: Loading OAuth URL in embedded window: ${oauthUrl}`);
        console.log(`🔍 DEBUGGING: Embedded parameter included: ${oauthUrl.includes('embedded=true')}`);
        
        // Set up event handlers BEFORE loading URL
        // Prevent external browser redirects
        authWindow.webContents.setWindowOpenHandler((details) => {
            console.log('🚫 DEBUGGING: Blocked external window open to:', details.url);
            return { action: 'deny' };
        });

        // Monitor navigation attempts
        authWindow.webContents.on('will-navigate', (event, navigationUrl) => {
            console.log('🧭 DEBUGGING: OAuth window will navigate to:', navigationUrl);
            
            // Allow OAuth provider domains and our server
            const allowedDomains = [
                '127.0.0.1:3001',
                'localhost:3001', 
                'accounts.google.com',
                'discord.com',
                'twitch.tv',
                'id.twitch.tv'
            ];
            
            const urlObj = new URL(navigationUrl);
            const isAllowed = allowedDomains.some(domain => 
                urlObj.hostname.includes(domain) || domain.includes(urlObj.hostname)
            );
            
            if (!isAllowed) {
                console.log('🚫 DEBUGGING: Blocked navigation to disallowed domain:', urlObj.hostname);
                event.preventDefault();
            } else {
                console.log('✅ DEBUGGING: Allowing navigation to:', urlObj.hostname);
            }
        });

        // Monitor for OAuth completion
        authWindow.webContents.on('did-navigate', (event, url) => {
            console.log('🔄 DEBUGGING: OAuth window navigated to:', url);
            console.log('🔍 DEBUGGING: URL contains embedded param:', url.includes('embedded=true'));
            this.handleEmbeddedOAuthUrl(url, authWindow);
        });

        authWindow.webContents.on('will-redirect', (event, url) => {
            console.log('🔄 DEBUGGING: OAuth window redirecting to:', url);
            console.log('🔍 DEBUGGING: Redirect URL contains embedded param:', url.includes('embedded=true'));
            this.handleEmbeddedOAuthUrl(url, authWindow);
        });
        
        // Handle window closed before completion
        authWindow.on('closed', () => {
            console.log('🔒 OAuth window closed event triggered');
            // Only reject if we haven't already resolved or rejected
            if (!this.pendingResolved && !this.pendingRejected) {
                console.log('❌ OAuth window closed before completion');
                this.pendingReject(new Error('OAuth window closed'));
            }
        });

        // Handle window errors
        authWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
            console.error('❌ OAuth window failed to load:', {
                errorCode,
                errorDescription,
                url: validatedURL
            });
        });

        // Load OAuth page
        console.log('🔄 DEBUGGING: About to load OAuth URL:', oauthUrl);
        authWindow.loadURL(oauthUrl);
        
        console.log('🔄 DEBUGGING: OAuth URL load initiated, waiting for window ready...');
        
        // Show window when ready
        authWindow.once('ready-to-show', () => {
            console.log('👁️ DEBUGGING: Embedded browser window ready, showing window');
            authWindow.show();
        });

        // Wait for OAuth completion
        const result = await this.waitForEmbeddedOAuth(authWindow);
        
        return result;

    } catch (error) {
        console.error(`❌ ${provider} OAuth error:`, error);
        return { success: false, error: error.message };
    }
  }

  async waitForEmbeddedOAuth(authWindow) {
    return new Promise((resolve, reject) => {
        console.log('⏳ DEBUGGING: Starting OAuth wait with 5-minute timeout');
        
        const timeout = setTimeout(() => {
            console.log('⏰ OAuth timeout - closing window');
            this.pendingRejected = true;
            authWindow.close();
            reject(new Error('OAuth timeout'));
        }, 300000); // 5 minute timeout

        // Store resolve/reject functions on the auth manager for access from event handlers
        this.pendingResolve = resolve;
        this.pendingReject = reject;
        this.pendingTimeout = timeout;
        this.pendingResolved = false;
        this.pendingRejected = false;

        // Handle window closed
        authWindow.on('closed', () => {
            console.log('🔒 OAuth window closed event triggered in waitForEmbeddedOAuth');
            clearTimeout(timeout);
            if (!this.pendingResolved && !this.pendingRejected) {
                console.log('❌ OAuth window closed before completion in waitForEmbeddedOAuth');
                this.pendingRejected = true;
                reject(new Error('OAuth window closed'));
            }
        });
        
        // Add a fallback check for when the page loads but token extraction fails
        authWindow.webContents.on('did-finish-load', () => {
            console.log('📄 OAuth window finished loading');
            
            // Wait a bit for any JavaScript to execute
            setTimeout(() => {
                if (!this.pendingResolved && !this.pendingRejected) {
                    console.log('🔍 Performing fallback token check...');
                    authWindow.webContents.executeJavaScript(`
                        // Try to extract token one more time
                        const urlParams = new URLSearchParams(window.location.search);
                        const token = urlParams.get('token') || 
                                     (window.oauthResult && window.oauthResult.token) ||
                                     window.oauthToken;
                        
                        if (token) {
                            console.log('🎫 Fallback token extraction successful');
                            require('electron').ipcRenderer.send('oauth-token', { token });
                        } else {
                            console.log('❌ Fallback token extraction failed');
                        }
                    `);
                }
            }, 1000);
        });
    });
  }

  handleEmbeddedOAuthUrl(url, authWindow) {
    try {
        console.log('🔍 Handling OAuth URL:', url);
        
        // Check for success callback (both /auth/success and /auth/electron/success)
        if (url.includes('/auth/success') || url.includes('/auth/electron/success')) {
            console.log('✅ OAuth success detected in embedded window');
            
            // Extract token from URL or wait for token message
            const urlParams = new URLSearchParams(url.split('?')[1] || '');
            const token = urlParams.get('token');
            const state = urlParams.get('state');
            
            console.log('🔍 URL parameters:', { token: token ? 'PRESENT' : 'MISSING', state: state ? 'PRESENT' : 'MISSING' });
            
            // Parse state parameter (could be JSON object or simple string)
            let clientState = state;
            try {
                if (state) {
                    const parsedState = JSON.parse(decodeURIComponent(state));
                    clientState = parsedState.clientState || state;
                }
            } catch (error) {
                // If parsing fails, use state as-is (backward compatibility)
                clientState = state;
            }
            
            // Skip state check if pendingState is empty (some OAuth flows don't use it)
            if (this.pendingState && clientState !== this.pendingState) {
                console.error('❌ OAuth state mismatch', {
                    expected: this.pendingState,
                    received: clientState,
                    rawState: state
                });
                // Don't reject immediately, give token extraction a chance
                console.log('⚠️ State mismatch, but continuing with token extraction...');
            }
            
            if (token) {
                console.log('🎫 Token found in URL, processing immediately');
                this.handleSuccessfulAuth(token, authWindow, this.pendingResolve, this.pendingTimeout);
            } else {
                console.log('⏳ No token in URL, waiting for token message from page');
                // Wait for postMessage with token
                this.waitForTokenMessage(authWindow, this.pendingResolve, this.pendingReject, this.pendingTimeout);
            }
        }
        // Check for error
        else if (url.includes('/auth/error') || url.includes('error=')) {
            console.error('❌ OAuth error detected in embedded window');
            clearTimeout(this.pendingTimeout);
            authWindow.close();
            this.pendingReject(new Error('OAuth authentication failed'));
        }
        // Check for other OAuth-related URLs
        else if (url.includes('/auth/') && !url.includes('/auth/google') && !url.includes('/auth/discord') && !url.includes('/auth/twitch')) {
            console.log('🔍 OAuth-related URL detected, monitoring for success...');
        }
        // Check for Google OAuth callback
        else if (url.includes('accounts.google.com') && url.includes('code=')) {
            console.log('🔍 Google OAuth callback detected, waiting for redirect...');
        }
    } catch (error) {
        console.error('❌ Error handling embedded OAuth URL:', error);
        clearTimeout(this.pendingTimeout);
        authWindow.close();
        this.pendingReject(error);
    }
  }

  waitForTokenMessage(authWindow, resolve, reject, timeout) {
    console.log('🔍 Waiting for OAuth token message...');
    
    // Listen for postMessage from the OAuth success page
    authWindow.webContents.on('ipc-message', (event, channel, data) => {
        if (channel === 'oauth-token') {
            console.log('🎫 Received OAuth token via postMessage');
            this.handleSuccessfulAuth(data.token, authWindow, resolve, timeout);
        }
    });

    // Listen for alternative token message method
    authWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
        if (message.includes('OAUTH_TOKEN')) {
            console.log('🎫 Received OAuth token via console message');
            try {
                const tokenMatch = message.match(/token['"]?\s*:\s*['"]([^'"]+)['"]/);
                if (tokenMatch) {
                    this.handleSuccessfulAuth(tokenMatch[1], authWindow, resolve, timeout);
                }
            } catch (e) {
                console.error('❌ Error parsing token from console message:', e);
            }
        }
    });

    // Inject script to extract token from the OAuth success page
    authWindow.webContents.executeJavaScript(`
        console.log('🔍 Extracting OAuth token from page...');
        
        // Function to extract token from various sources
        function extractToken() {
            // 1. Try URL parameters first
            const urlParams = new URLSearchParams(window.location.search);
            let token = urlParams.get('token');
            
            if (token) {
                console.log('✅ Token found in URL parameters');
                return token;
            }
            
            // 2. Try window.oauthResult (from embedded browser success page)
            if (window.oauthResult && window.oauthResult.token) {
                console.log('✅ Token found in window.oauthResult');
                return window.oauthResult.token;
            }
            
            // 3. Try data attributes
            const tokenElement = document.querySelector('[data-token]');
            if (tokenElement && tokenElement.dataset.token) {
                console.log('✅ Token found in data attribute');
                return tokenElement.dataset.token;
            }
            
            // 4. Try global oauthToken variable
            if (window.oauthToken) {
                console.log('✅ Token found in window.oauthToken');
                return window.oauthToken;
            }
            
            // 5. Try localStorage (fallback)
            try {
                const storedToken = localStorage.getItem('authToken');
                if (storedToken) {
                    console.log('✅ Token found in localStorage');
                    return storedToken;
                }
            } catch (e) {
                console.log('⚠️ Could not access localStorage');
            }
            
            console.log('❌ No token found in current page');
            return null;
        }
        
        // Try to extract token immediately
        let token = extractToken();
        
        if (token) {
            console.log('🎫 Sending token to Electron main process');
            // Try multiple ways to send the token
            try {
                require('electron').ipcRenderer.send('oauth-token', { token });
            } catch (e) {
                console.log('⚠️ IPC send failed, trying alternative method');
                // Alternative: post message to parent
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({ type: 'OAUTH_TOKEN', token }, '*');
                }
            }
        } else {
            // Keep checking for token (in case it's loaded asynchronously)
            console.log('⏳ Token not found, will keep checking...');
            let checkCount = 0;
            const maxChecks = 100; // 10 seconds max (increased)
            
            const checkForToken = () => {
                checkCount++;
                token = extractToken();
                
                if (token) {
                    console.log('🎫 Token found after checking, sending to Electron main process');
                    try {
                        require('electron').ipcRenderer.send('oauth-token', { token });
                    } catch (e) {
                        console.log('⚠️ IPC send failed, trying alternative method');
                        if (window.parent && window.parent !== window) {
                            window.parent.postMessage({ type: 'OAUTH_TOKEN', token }, '*');
                        }
                    }
                } else if (checkCount < maxChecks) {
                    setTimeout(checkForToken, 100);
                } else {
                    console.error('❌ Token not found after maximum checks');
                }
            };
            
            setTimeout(checkForToken, 100);
        }
    `);
  }

  async handleSuccessfulAuth(token, authWindow, resolve, timeout) {
    try {
        clearTimeout(timeout);
        this.pendingResolved = true;
        
        console.log('🎫 Processing OAuth token...');
        
        // Validate token with server
        const response = await axios.get(`${this.serverUrl}/api/me`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'X-Electron-App': 'true'
            }
        });

        if (response.data && response.data.username) {
            const userData = response.data;
            
            console.log('✅ Token validated, saving user data:', userData.username);
            
            // Store authentication data
            this.store.set('authToken', token);
            this.store.set('user', userData);
            this.store.set('rememberLogin', true);
            this._isAuthenticated = true;
            this.user = userData;

            authWindow.close();
            resolve({ success: true, user: userData });
        } else {
            throw new Error('Invalid token or user data');
        }
    } catch (error) {
        console.error('❌ Token validation failed:', error);
        clearTimeout(timeout);
        this.pendingRejected = true;
        authWindow.close();
        resolve({ success: false, error: 'Token validation failed' });
    }
  }

  async logout() {
    try {
      console.log('🔓 Starting logout process...');
      
      // Clear OAuth state
      this.currentOAuthState = null;
      
      // First, notify server to clear sessions (before clearing local data)
      try {
        console.log('🌐 Clearing server sessions...');
        await axios.post(`${this.serverUrl}/auth/logout/clear-sessions`, {}, {
          headers: { 
            'X-Electron-Client': 'true',
            'X-Session-Isolation': 'true',
            'Content-Type': 'application/json'
          },
          timeout: 10000
        });
        console.log('✅ Server sessions cleared successfully');
      } catch (error) {
        console.log('⚠️ Server session clearing failed (continuing anyway):', error.message);
      }
      
      // Use consistent cleanup method
      this.clearAllAuthData();

      console.log('✅ User logged out successfully - all auth data cleared');
      return true;
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  async storeAuthToken(token) {
    try {
      console.log('💾 Storing authentication token securely');
      
      // Validate the token with the server first
      console.log('🔍 Validating token with server...');
      const response = await axios.get(`${this.serverUrl}/auth/electron/verify`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Electron-Client': 'true',
          'X-Session-Isolation': 'true'
        },
        timeout: 10000
      });

      if (response.data.success && response.data.user) {
        console.log('✅ Token validated successfully with server');
        
        this.user = response.data.user;
        this._isAuthenticated = true;
        
        // Store token and user data securely
        if (this.store) {
          this.store.set('authToken', token);
          this.store.set('user', this.user);
          console.log('✅ Token and user data stored successfully');
        }
        
        return true;
      } else {
        console.error('❌ Server rejected the token');
        return false;
      }
    } catch (error) {
      console.error('❌ Error storing/validating auth token:', error);
      return false;
    }
  }

  async getStoredToken() {
    try {
      const token = this.store.get('authToken');
      if (token) {
        console.log('🔑 Retrieved stored authentication token');
        return token;
      } else {
        console.log('ℹ️ No authentication token found in store');
        return null;
      }
    } catch (error) {
      console.error('❌ Error retrieving stored token:', error);
      return null;
    }
  }

  async getOAuthUrl(provider) {
    try {
      // Generate a secure random state for CSRF protection
      const state = crypto.randomBytes(32).toString('hex');
      this.currentOAuthState = state;
      
      // Construct the OAuth URL
      const authUrl = `${this.serverUrl}/auth/${provider}?electron=true&state=${state}`;
      
      console.log(`Generated OAuth URL for ${provider}:`, authUrl);
      return authUrl;
    } catch (error) {
      console.error('Error generating OAuth URL:', error);
      throw error;
    }
  }

  // Method version of isAuthenticated property (for IPC handlers)
  getIsAuthenticated() {
    return this._isAuthenticated;
  }

  // Get current user data
  async getCurrentUser() {
    return this.user;
  }

  // Get stored token
  getToken() {
    return this.store.get('authToken');
  }

  // Set server URL (alias for updateServerUrl)
  setServerUrl(url) {
    this.updateServerUrl(url);
  }

  // Set main window reference for OAuth popup
  setMainWindow(mainWindow) {
    this.mainWindow = mainWindow;
  }

  // Basic login method (for compatibility)
  async login(username, password) {
    // This would need to be implemented based on your authentication flow
    throw new Error('Username/password login not implemented - use OAuth login');
  }

  // Clean up method
  destroy() {
    console.log('🧹 Destroying AuthManager and cleaning up resources...');
    
    // Clear OAuth state
    this.currentOAuthState = null;
    this.pendingAuthPromise = null;
    
    // Clear authentication state
    this._isAuthenticated = false;
    this.user = null;
  }

  generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}

module.exports = { AuthManager }; 