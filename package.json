{"name": "wc-arena-project", "version": "1.0.0", "description": "WC Arena - Complete gaming platform with web interface and desktop companion", "main": "electron-app/main.js", "private": true, "workspaces": ["backend", "electron-app"], "scripts": {"start": "npm run start:backend", "start:backend": "cd backend && npm start", "start:electron": "cd electron-app && npm start", "start:dev": "concurrently \"npm run dev:backend\" \"npm run dev:electron\"", "dev:backend": "cd backend && npm run dev", "dev:electron": "cd electron-app && npm run start-dev", "build": "npm run build:electron", "build:electron": "cd electron-app && npm run build", "install:all": "npm install && npm run install:backend && npm run install:electron", "install:backend": "cd backend && npm install", "install:electron": "cd electron-app && npm install", "clean": "npm run clean:backend && npm run clean:electron", "clean:backend": "cd backend && rm -rf node_modules package-lock.json", "clean:electron": "cd electron-app && rm -rf node_modules package-lock.json dist", "deps:check": "node scripts/dependency-manager.js --check", "deps:fix": "node scripts/dependency-manager.js --fix", "deps:report": "node scripts/dependency-manager.js --report", "lint": "echo '<PERSON><PERSON> not configured yet'", "test": "echo 'Tests not configured yet'"}, "author": "WC Arena Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}