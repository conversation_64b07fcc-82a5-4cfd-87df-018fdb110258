<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background: #28a745;
        }
        .danger {
            background: #dc3545;
        }
        .warning {
            background: #ffc107;
            color: #000;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 OAuth Debug Tool</h1>
        <p>This tool helps diagnose OAuth authentication issues in the Electron app.</p>

        <div class="test-section">
            <h2>1. Protocol Registration Test</h2>
            <p>Test if the warcraftarena:// protocol is properly registered.</p>
            <button onclick="testProtocolRegistration()">Test Protocol Registration</button>
            <div id="protocolStatus" class="status"></div>
        </div>

        <div class="test-section">
            <h2>2. Protocol URL Handler Test</h2>
            <p>Test if the protocol URL handler is working correctly.</p>
            <button onclick="testProtocolHandler()">Test Protocol Handler</button>
            <button class="warning" onclick="testProtocolHandlerWithRealToken()">Test with Real Token</button>
            <div id="handlerStatus" class="status"></div>
        </div>

        <div class="test-section">
            <h2>3. OAuth Flow Test</h2>
            <p>Test the complete OAuth flow with a real provider.</p>
            <button class="success" onclick="testOAuthFlow('google')">Test Google OAuth</button>
            <button class="success" onclick="testOAuthFlow('discord')">Test Discord OAuth</button>
            <button class="success" onclick="testOAuthFlow('twitch')">Test Twitch OAuth</button>
            <div id="oauthStatus" class="status"></div>
        </div>

        <div class="test-section">
            <h2>4. Token Validation Test</h2>
            <p>Test if stored tokens can be validated with the server.</p>
            <button onclick="testTokenValidation()">Test Token Validation</button>
            <button class="warning" onclick="testCurrentUser()">Test Get Current User</button>
            <div id="tokenStatus" class="status"></div>
        </div>

        <div class="test-section">
            <h2>5. Server Connection Test</h2>
            <p>Test connection to the backend server.</p>
            <button onclick="testServerConnection()">Test Server Connection</button>
            <div id="serverStatus" class="status"></div>
        </div>

        <div class="test-section">
            <h2>6. Manual Protocol URL Test</h2>
            <p>Manually trigger a protocol URL to test the complete flow.</p>
            <input type="text" id="manualUrl" placeholder="warcraftarena://oauth-success?token=..." style="width: 500px; padding: 8px; margin: 5px;">
            <button onclick="testManualProtocolUrl()">Test Manual URL</button>
            <div id="manualStatus" class="status"></div>
        </div>

        <div class="test-section">
            <h2>📋 Debug Log</h2>
            <button onclick="clearLog()">Clear Log</button>
            <button onclick="exportLog()">Export Log</button>
            <div id="debugLog" class="log"></div>
        </div>
    </div>

    <script>
        let debugLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            
            const logElement = document.getElementById('debugLog');
            logElement.innerHTML += logEntry + '\n';
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(message);
        }

        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function clearLog() {
            debugLog = [];
            document.getElementById('debugLog').innerHTML = '';
        }

        function exportLog() {
            const logText = debugLog.join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `oauth-debug-${Date.now()}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        async function testProtocolRegistration() {
            log('🔍 Testing protocol registration...');
            setStatus('protocolStatus', 'Testing...', 'info');
            
            try {
                if (window.electronAPI) {
                    // Test if we can invoke IPC calls
                    const result = await window.electronAPI.invoke('test-protocol-url', 'warcraftarena://test');
                    log('✅ Protocol registration test successful: ' + JSON.stringify(result));
                    setStatus('protocolStatus', 'Protocol is registered and working', 'success');
                } else {
                    throw new Error('electronAPI not available');
                }
            } catch (error) {
                log('❌ Protocol registration test failed: ' + error.message);
                setStatus('protocolStatus', 'Protocol registration failed: ' + error.message, 'error');
            }
        }

        async function testProtocolHandler() {
            log('🔍 Testing protocol handler...');
            setStatus('handlerStatus', 'Testing...', 'info');
            
            try {
                const testUrl = 'warcraftarena://oauth-success?token=test-token-123&state=test-state-456';
                const result = await window.electronAPI.invoke('test-protocol-url', testUrl);
                log('✅ Protocol handler test successful: ' + JSON.stringify(result));
                setStatus('handlerStatus', 'Protocol handler is working', 'success');
            } catch (error) {
                log('❌ Protocol handler test failed: ' + error.message);
                setStatus('handlerStatus', 'Protocol handler failed: ' + error.message, 'error');
            }
        }

        async function testProtocolHandlerWithRealToken() {
            log('🔍 Testing protocol handler with real token format...');
            setStatus('handlerStatus', 'Testing with real token...', 'info');
            
            try {
                // Generate a JWT-like token for testing
                const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
                const testUrl = `warcraftarena://oauth-success?token=${testToken}&state=real-test-state`;
                const result = await window.electronAPI.invoke('test-protocol-url', testUrl);
                log('✅ Real token test successful: ' + JSON.stringify(result));
                setStatus('handlerStatus', 'Real token test passed', 'success');
            } catch (error) {
                log('❌ Real token test failed: ' + error.message);
                setStatus('handlerStatus', 'Real token test failed: ' + error.message, 'error');
            }
        }

        async function testOAuthFlow(provider) {
            log(`🔍 Testing ${provider} OAuth flow...`);
            setStatus('oauthStatus', `Testing ${provider} OAuth...`, 'info');
            
            try {
                const result = await window.electronAPI.auth.login(provider);
                log(`✅ ${provider} OAuth initiated: ` + JSON.stringify(result));
                setStatus('oauthStatus', `${provider} OAuth initiated successfully`, 'success');
            } catch (error) {
                log(`❌ ${provider} OAuth failed: ` + error.message);
                setStatus('oauthStatus', `${provider} OAuth failed: ` + error.message, 'error');
            }
        }

        async function testTokenValidation() {
            log('🔍 Testing token validation...');
            setStatus('tokenStatus', 'Testing...', 'info');

            try {
                // Get stored token
                const tokenResult = await window.electronAPI.auth.getToken();
                if (!tokenResult || !tokenResult.token) {
                    throw new Error('No stored token found');
                }

                log('✅ Found stored token: ' + tokenResult.token.substring(0, 20) + '...');

                // Test validation endpoint directly
                const response = await fetch('http://127.0.0.1:3001/auth/electron/verify', {
                    headers: {
                        'Authorization': `Bearer ${tokenResult.token}`,
                        'X-Electron-Client': 'true'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log('✅ Token validation successful: ' + JSON.stringify(data));
                    setStatus('tokenStatus', 'Token is valid and user authenticated', 'success');
                } else {
                    const errorData = await response.text();
                    throw new Error(`Token validation failed: ${response.status} - ${errorData}`);
                }
            } catch (error) {
                log('❌ Token validation failed: ' + error.message);
                setStatus('tokenStatus', 'Token validation failed: ' + error.message, 'error');
            }
        }

        async function testCurrentUser() {
            log('🔍 Testing get current user...');
            setStatus('tokenStatus', 'Testing current user...', 'info');

            try {
                const result = await window.electronAPI.auth.getCurrentUser();
                if (result && result.user) {
                    log('✅ Current user retrieved: ' + JSON.stringify(result.user));
                    setStatus('tokenStatus', 'Current user retrieved successfully', 'success');
                } else {
                    throw new Error('No current user data returned');
                }
            } catch (error) {
                log('❌ Get current user failed: ' + error.message);
                setStatus('tokenStatus', 'Get current user failed: ' + error.message, 'error');
            }
        }

        async function testServerConnection() {
            log('🔍 Testing server connection...');
            setStatus('serverStatus', 'Testing...', 'info');

            try {
                const response = await fetch('http://127.0.0.1:3001/auth/config');
                if (response.ok) {
                    const data = await response.json();
                    log('✅ Server connection successful: ' + JSON.stringify(data));
                    setStatus('serverStatus', 'Server is online and responding', 'success');
                } else {
                    throw new Error(`Server responded with status ${response.status}`);
                }
            } catch (error) {
                log('❌ Server connection failed: ' + error.message);
                setStatus('serverStatus', 'Server connection failed: ' + error.message, 'error');
            }
        }

        async function testManualProtocolUrl() {
            const url = document.getElementById('manualUrl').value;
            if (!url) {
                setStatus('manualStatus', 'Please enter a URL', 'error');
                return;
            }
            
            log('🔍 Testing manual protocol URL: ' + url);
            setStatus('manualStatus', 'Testing...', 'info');
            
            try {
                const result = await window.electronAPI.invoke('test-protocol-url', url);
                log('✅ Manual protocol URL test successful: ' + JSON.stringify(result));
                setStatus('manualStatus', 'Manual test successful', 'success');
            } catch (error) {
                log('❌ Manual protocol URL test failed: ' + error.message);
                setStatus('manualStatus', 'Manual test failed: ' + error.message, 'error');
            }
        }

        // Set up OAuth event listeners
        if (window.electronAPI) {
            window.electronAPI.onOAuthSuccess((data) => {
                log('🎉 OAuth success event received: ' + JSON.stringify(data));
                setStatus('oauthStatus', 'OAuth success received!', 'success');
            });

            window.electronAPI.onOAuthError((data) => {
                log('❌ OAuth error event received: ' + JSON.stringify(data));
                setStatus('oauthStatus', 'OAuth error: ' + data.error, 'error');
            });

            window.electronAPI.onOAuthTimeout((data) => {
                log('⏰ OAuth timeout event received: ' + JSON.stringify(data));
                setStatus('oauthStatus', 'OAuth timeout: ' + data.message, 'error');
            });

            window.electronAPI.on('oauth-debug', (data) => {
                log('🐛 OAuth debug event received: ' + JSON.stringify(data));
            });
        }

        // Initialize
        log('🚀 OAuth Debug Tool initialized');
        log('📱 Electron API available: ' + (window.electronAPI ? 'YES' : 'NO'));
    </script>
</body>
</html>
