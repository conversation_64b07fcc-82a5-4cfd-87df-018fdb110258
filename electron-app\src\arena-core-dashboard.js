/**
 * Arena Core Dashboard
 * 
 * Enhanced dashboard for WC Arena Core with:
 * - Warcraft 1/2/3 game detection
 * - Automatic game result detection
 * - Match reporting integration
 */
const { GameResultDetector, GameProcessDetector } = require('./game-result-detector');
const { ApiClient } = require('./api-client');
const Store = require('electron-store');
class ArenCoreDashboard {
  constructor() {
    this.store = new Store({
      name: 'wc-arena-core-config',
      encryptionKey: 'wc-arena-core-secret-key'
    });
    this.apiClient = new ApiClient();
    this.gameDetector = new GameProcessDetector();
    this.resultDetector = new GameResultDetector(this.gameDetector, this);
    this.currentUser = null;
    this.gameStats = {
      warcraft1: { played: 0, won: 0, lost: 0 },
      warcraft2: { played: 0, won: 0, lost: 0 },
      warcraft3: { played: 0, won: 0, lost: 0 }
    };
    this.setupEventListeners();
  }
  /**
   * Initialize the dashboard
   */
  async init() {
    try {
      // Load user authentication
      await this.loadUserAuth();
      // Start game detection
      await this.startGameDetection();
      // Load user stats
      await this.loadUserStats();
      // Setup UI
      this.setupUI();
    } catch (error) {
      this.showError('Failed to initialize Arena Core Dashboard');
    }
  }
  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Game detection events
    this.gameDetector.on('gameStarted', (gameInfo) => {
      this.onGameStarted(gameInfo);
    });
    this.gameDetector.on('gameEnded', (gameInfo) => {
      this.onGameEnded(gameInfo);
    });
    // Game result events
    this.resultDetector.on('gameResult', (gameSession) => {
      this.onGameResult(gameSession);
    });
    // Load stored user data
    this.loadStoredUser();
  }
  /**
   * Load stored user data
   */
  loadStoredUser() {
    try {
      this.currentUser = this.store.get('user');
      const authToken = this.store.get('authToken');
      if (this.currentUser && authToken) {
        this.apiClient.setAuthToken(authToken);
      } else {
      }
    } catch (error) {
    }
  }
  /**
   * Load user authentication
   */
  async loadUserAuth() {
    // User auth is loaded from store in loadStoredUser()
    if (this.currentUser) {
    } else {
    }
  }
  /**
   * Start game detection systems
   */
  async startGameDetection() {
    this.gameDetector.start();
    await this.resultDetector.startMonitoring();
  }
  /**
   * Load user statistics
   */
  async loadUserStats() {
    if (!this.currentUser) return;
    try {
      const stats = await this.apiClient.get(`/api/users/${this.currentUser.id}/game-stats`);
      this.gameStats = { ...this.gameStats, ...stats };
      this.updateStatsDisplay();
    } catch (error) {
    }
  }
  /**
   * Setup the dashboard UI
   */
  setupUI() {
    this.updateUserDisplay();
    this.updateGameStatusDisplay();
    this.updateStatsDisplay();
    this.setupGameButtons();
  }
  /**
   * Handle game started event
   */
  onGameStarted(gameInfo) {
    this.updateGameStatusDisplay({
      status: 'playing',
      game: gameInfo.name,
      type: gameInfo.type,
      startTime: gameInfo.startTime
    });
    this.showNotification(`Started playing ${gameInfo.name}`, 'info');
  }
  /**
   * Handle game ended event
   */
  onGameEnded(gameInfo) {
    this.updateGameStatusDisplay({
      status: 'idle',
      lastGame: gameInfo.name,
      endTime: Date.now()
    });
  }
  /**
   * Handle game result detected
   */
  async onGameResult(gameSession) {
    try {
      // Update local stats
      this.updateLocalStats(gameSession);
      // Show result notification
      this.showGameResultNotification(gameSession);
      // Auto-report if user is logged in and has enabled auto-reporting
      if (this.currentUser && this.shouldAutoReport()) {
        await this.autoReportMatch(gameSession);
      } else {
        // Show manual report option
        this.showManualReportOption(gameSession);
      }
    } catch (error) {
    }
  }
  /**
   * Update local game statistics
   */
  updateLocalStats(gameSession) {
    const gameType = gameSession.type;
    if (!this.gameStats[gameType]) return;
    this.gameStats[gameType].played++;
    if (gameSession.result.outcome === 'victory') {
      this.gameStats[gameType].won++;
    } else if (gameSession.result.outcome === 'defeat') {
      this.gameStats[gameType].lost++;
    }
    this.updateStatsDisplay();
  }
  /**
   * Auto-report match to the arena
   */
  async autoReportMatch(gameSession) {
    if (!this.currentUser) return;
    try {
      const matchData = {
        gameType: gameSession.type,
        result: gameSession.result.outcome,
        duration: gameSession.endTime - gameSession.startTime,
        detectionMethod: gameSession.detectionMethods.join(', '),
        timestamp: gameSession.endTime,
        autoReported: true
      };
      const response = await this.apiClient.post('/api/ladder/matches/auto-report', matchData);
      if (response.ok) {
        this.showNotification('Match automatically reported to Arena!', 'success');
      } else {
        throw new Error('Failed to auto-report match');
      }
    } catch (error) {
      this.showNotification('Auto-report failed. Please report manually.', 'warning');
      this.showManualReportOption(gameSession);
    }
  }
  /**
   * Show manual report option
   */
  showManualReportOption(gameSession) {
    const notification = {
      title: 'Game Result Detected',
      message: `${gameSession.result.outcome.toUpperCase()} in ${gameSession.name}`,
      actions: [
        {
          text: 'Report to Arena',
          action: () => this.openManualReportDialog(gameSession)
        },
        {
          text: 'Ignore',
          action: () => {}
        }
      ]
    };
    this.showInteractiveNotification(notification);
  }
  /**
   * Open manual report dialog
   */
  openManualReportDialog(gameSession) {
    // This would open a dialog for manual match reporting
    // For now, just open the web interface
    const { shell } = require('electron');
    shell.openExternal('http://localhost:3001/views/ladder.html#report');
  }
  /**
   * Update user display
   */
  updateUserDisplay() {
    const userElement = document.getElementById('current-user');
    if (userElement) {
      if (this.currentUser) {
        userElement.innerHTML = `
          <div class="user-info">
            <img src="${this.currentUser.avatar || '/assets/img/ranks/emblem.png'}" alt="Avatar" class="user-avatar">
            <div class="user-details">
              <div class="username">${this.currentUser.username}</div>
              <div class="user-level">Level ${this.currentUser.achievementLevel || 1}</div>
            </div>
          </div>
        `;
      } else {
        userElement.innerHTML = `
          <div class="login-prompt">
            <button onclick="window.arenaDashboard.showLogin()" class="btn btn-primary">
              Login to Arena
            </button>
          </div>
        `;
      }
    }
  }
  /**
   * Update game status display
   */
  updateGameStatusDisplay(status = { status: 'idle' }) {
    const statusElement = document.getElementById('game-status');
    if (!statusElement) return;
    if (status.status === 'playing') {
      statusElement.innerHTML = `
        <div class="status-playing">
          <div class="status-indicator playing"></div>
          <div class="status-text">
            <div class="game-name">${status.game}</div>
            <div class="game-time">Started ${this.formatTime(status.startTime)}</div>
          </div>
        </div>
      `;
    } else {
      statusElement.innerHTML = `
        <div class="status-idle">
          <div class="status-indicator idle"></div>
          <div class="status-text">
            <div class="game-name">Ready to play</div>
            ${status.lastGame ? `<div class="last-game">Last: ${status.lastGame}</div>` : ''}
          </div>
        </div>
      `;
    }
  }
  /**
   * Update statistics display
   */
  updateStatsDisplay() {
    const statsElement = document.getElementById('game-stats');
    if (!statsElement) return;
    const statsHTML = Object.entries(this.gameStats).map(([gameType, stats]) => {
      const winRate = stats.played > 0 ? Math.round((stats.won / stats.played) * 100) : 0;
      const gameTitle = this.getGameTitle(gameType);
      return `
        <div class="game-stat-card">
          <div class="game-title">${gameTitle}</div>
          <div class="stat-row">
            <span class="stat-label">Played:</span>
            <span class="stat-value">${stats.played}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">Won:</span>
            <span class="stat-value won">${stats.won}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">Lost:</span>
            <span class="stat-value lost">${stats.lost}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">Win Rate:</span>
            <span class="stat-value">${winRate}%</span>
          </div>
        </div>
      `;
    }).join('');
    statsElement.innerHTML = statsHTML;
  }
  /**
   * Setup game launch buttons
   */
  setupGameButtons() {
    // This would setup buttons to launch games
    // For now, just detect when games are launched externally
  }
  /**
   * Utility functions
   */
  getGameTitle(gameType) {
    const titles = {
      'warcraft1': 'Warcraft I',
      'warcraft2': 'Warcraft II',
      'warcraft3': 'Warcraft III'
    };
    return titles[gameType] || gameType;
  }
  formatTime(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    if (minutes < 1) return 'just now';
    if (minutes === 1) return '1 minute ago';
    return `${minutes} minutes ago`;
  }
  shouldAutoReport() {
    // Check user preferences for auto-reporting
    return this.currentUser && this.currentUser.preferences?.autoReportMatches !== false;
  }
  showNotification(message, type = 'info') {
    console.log(`📢 ${type.toUpperCase()}: ${message}`);
    // Implement actual notification system
  }
  showGameResultNotification(gameSession) {
    const outcome = gameSession.result.outcome;
    const message = `${outcome.toUpperCase()} in ${gameSession.name}!`;
    const type = outcome === 'victory' ? 'success' : 'info';
    this.showNotification(message, type);
  }
  showInteractiveNotification(notification) {
    // Implement interactive notification system
  }
  showError(message) {
    this.showNotification(message, 'error');
  }
  showLogin() {
    // Open login dialog or redirect to web login
    const { shell } = require('electron');
    shell.openExternal('http://localhost:3001/views/login.html');
  }
  /**
   * Cleanup
   */
  destroy() {
    this.gameDetector.stop();
    this.resultDetector.stopMonitoring();
  }
}
module.exports = ArenCoreDashboard;
