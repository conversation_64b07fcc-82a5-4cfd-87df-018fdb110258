const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const chokidar = require('chokidar');
const { desktopCapturer } = require('electron');
const ImageAnalyzer = require('./image-analyzer');
const { EventEmitter } = require('events');

class ScreenshotManager extends EventEmitter {
  constructor(gameDetector) {
    super();
    this.gameDetector = gameDetector;
    this.imageAnalyzer = new ImageAnalyzer();
    this.isMonitoring = false;
    this.watchers = [];
    this.settings = {
      autoAnalysis: true,
      autoReport: false,
      confidenceThreshold: 70,
      gameTypes: ['warcraft1', 'warcraft2', 'warcraft3'],
      organizeFolders: true,
      deleteAfterReport: false,
      notifications: true
    };
    
    // Enhanced screenshot detection patterns
    this.screenshotPatterns = {
      warcraft1: {
        defaultPaths: this.getWC1ScreenshotPaths(),
        filePatterns: [/WC1.*\.png$/i, /warcraft.*1.*\.png$/i, /screenshot.*\.png$/i],
        gameSpecificPaths: ['WC1', 'Warcraft', 'Warcraft 1']
      },
      warcraft2: {
        defaultPaths: this.getWC2ScreenshotPaths(),
        filePatterns: [/WC2.*\.png$/i, /warcraft.*2.*\.png$/i, /war2.*\.png$/i],
        gameSpecificPaths: ['WC2', 'War2', 'Warcraft II']
      },
      warcraft3: {
        defaultPaths: this.getWC3ScreenshotPaths(),
        filePatterns: [/WC3.*\.png$/i, /warcraft.*3.*\.png$/i, /war3.*\.png$/i],
        gameSpecificPaths: ['WC3', 'War3', 'Warcraft III']
      }
    };

    // Screenshot analysis queue
    this.analysisQueue = [];
    this.isProcessingQueue = false;
    this.maxQueueSize = 50;

    // Statistics tracking
    this.stats = {
      detected: 0,
      analyzed: 0,
      victories: 0,
      defeats: 0,
      autoReported: 0,
      averageConfidence: 0,
      sessionStart: new Date()
    };

    console.log('🔧 Enhanced ScreenshotManager initialized');
  }

  getWC1ScreenshotPaths() {
    const userHome = os.homedir();
    return [
      path.join(userHome, 'Documents', 'Warcraft', 'Screenshots'),
      path.join(userHome, 'Documents', 'WC1', 'Screenshots'),
      path.join(userHome, 'Pictures', 'Warcraft 1'),
      path.join(userHome, 'Pictures', 'Screenshots'),
      'C:\\Games\\Warcraft\\Screenshots',
      'C:\\GOG Games\\Warcraft\\Screenshots'
    ];
  }

  getWC2ScreenshotPaths() {
    const userHome = os.homedir();
    return [
      path.join(userHome, 'Documents', 'Warcraft II BNE', 'Screenshots'),
      path.join(userHome, 'Documents', 'WC2', 'Screenshots'),
      path.join(userHome, 'Pictures', 'Warcraft 2'),
      path.join(userHome, 'Pictures', 'Screenshots'),
      'C:\\Games\\Warcraft II BNE\\Screenshots',
      'C:\\GOG Games\\Warcraft II\\Screenshots'
    ];
  }

  getWC3ScreenshotPaths() {
    const userHome = os.homedir();
    return [
      path.join(userHome, 'Documents', 'Warcraft III', 'Screenshots'),
      path.join(userHome, 'Documents', 'WC3', 'Screenshots'),
      path.join(userHome, 'Pictures', 'Warcraft 3'),
      path.join(userHome, 'Pictures', 'Screenshots'),
      'C:\\Games\\Warcraft III\\Screenshots',
      'C:\\Program Files\\Warcraft III\\Screenshots',
      'C:\\Program Files (x86)\\Warcraft III\\Screenshots'
    ];
  }

  async startMonitoring() {
    if (this.isMonitoring) {
      console.log('⚠️ Screenshot monitoring already active');
      return;
    }

    console.log('🔍 Starting enhanced screenshot monitoring...');
    this.isMonitoring = true;
    this.stats.sessionStart = new Date();

    try {
      // Monitor all configured game screenshot directories
      for (const gameType of this.settings.gameTypes) {
        if (this.screenshotPatterns[gameType]) {
          await this.setupGameMonitoring(gameType);
        }
      }

      // Monitor common screenshot directories
      await this.setupCommonMonitoring();
      
      console.log(`✅ Monitoring ${this.watchers.length} screenshot directories`);
      console.log('📸 Auto-analysis:', this.settings.autoAnalysis ? 'ENABLED' : 'DISABLED');
      console.log('📊 Auto-reporting:', this.settings.autoReport ? 'ENABLED' : 'DISABLED');

    } catch (error) {
      console.error('❌ Error starting screenshot monitoring:', error);
      this.isMonitoring = false;
    }
  }

  async startWatching() {
    // Alias for startMonitoring for compatibility
    return this.startMonitoring();
  }

  async setupGameMonitoring(gameType) {
    const pattern = this.screenshotPatterns[gameType];
    const validPaths = [];

    // Check which paths exist
    for (const screenshotPath of pattern.defaultPaths) {
      try {
        await fs.access(screenshotPath);
        validPaths.push(screenshotPath);
        console.log(`✅ Found ${gameType} screenshot directory: ${screenshotPath}`);
      } catch {
        // Directory doesn't exist, skip silently
      }
    }

    // Create watchers for valid paths
    for (const validPath of validPaths) {
      this.createWatcher(validPath, gameType);
    }

    return validPaths.length;
  }

  async setupCommonMonitoring() {
    const userHome = os.homedir();
    const commonPaths = [
      path.join(userHome, 'Pictures'),
      path.join(userHome, 'Desktop'),
      path.join(userHome, 'Documents')
      // Removed Downloads folder to prevent browser download interference
    ];

    for (const commonPath of commonPaths) {
      try {
        await fs.access(commonPath);
        this.createWatcher(commonPath, 'auto-detect', { depth: 2 });
        console.log(`👀 Watching common directory: ${commonPath}`);
      } catch {
        // Skip inaccessible directories
      }
    }
  }

  createWatcher(watchPath, gameType, options = {}) {
    const watcher = chokidar.watch(watchPath, {
      ignored: [
        /(^|[\/\\])\../, // Ignore hidden files
        /node_modules/,
        /\.tmp$/,
        /\.partial$/
      ],
          persistent: true,
      ignoreInitial: true,
      depth: options.depth || 3,
          awaitWriteFinish: {
        stabilityThreshold: 2000,
            pollInterval: 100
      }
        });
        
        watcher.on('add', (filePath) => {
      this.handleNewFile(filePath, gameType);
        });
        
        watcher.on('error', (error) => {
      console.error(`❌ Watcher error for ${watchPath}:`, error);
    });

    this.watchers.push({
      watcher,
      path: watchPath,
      gameType,
      createdAt: new Date()
    });

    return watcher;
  }

  async handleNewFile(filePath, detectedGameType) {
    try {
      const fileExt = path.extname(filePath).toLowerCase();
      const fileName = path.basename(filePath).toLowerCase();

      // Only process image files
      if (!['.png', '.jpg', '.jpeg', '.bmp', '.gif'].includes(fileExt)) {
        return;
      }
      
      // Skip files that are clearly not screenshots (browser downloads, etc.)
      if (this.isLikelyBrowserDownload(filePath, fileName)) {
        console.log(`🚫 Skipping likely browser download: ${fileName}`);
        return;
      }
      
      console.log(`📸 New screenshot detected: ${fileName}`);
      this.stats.detected++;

      // Enhanced game type detection
      const gameType = await this.detectGameType(filePath, detectedGameType);
      
      if (!gameType) {
        console.log('⚠️ Could not determine game type, skipping analysis');
        return;
      }
      
      // Check if file is recent (within last 60 seconds) for game result detection
      const stats = await fs.stat(filePath);
      const ageSeconds = (Date.now() - stats.mtime.getTime()) / 1000;
      const isRecent = ageSeconds < 60;

      console.log(`🎮 Detected game: ${gameType}, Age: ${Math.round(ageSeconds)}s, Recent: ${isRecent}`);

      // Organize file if enabled
      let organizedPath = filePath;
      if (this.settings.organizeFolders) {
        organizedPath = await this.organizeScreenshot(filePath, gameType);
      }

      // Queue for analysis if auto-analysis is enabled and file is recent
      if (this.settings.autoAnalysis && isRecent) {
        this.queueForAnalysis({
          filePath: organizedPath,
          gameType,
          isRecent,
          timestamp: new Date()
        });
      }
      
    } catch (error) {
      console.error('❌ Error handling new screenshot:', error);
    }
  }

  isLikelyBrowserDownload(filePath, fileName) {
    // Check if file is in Downloads folder (most common browser download location)
    const pathLower = filePath.toLowerCase();
    if (pathLower.includes('downloads')) {
      return true;
    }
    
    // Check for common browser download patterns
    const browserDownloadPatterns = [
      /^image_\d+\.(png|jpg|jpeg)$/i,  // Chrome/Firefox image downloads
      /^download_\d+\.(png|jpg|jpeg)$/i,  // Generic download patterns
      /^img_\d+\.(png|jpg|jpeg)$/i,  // Image download patterns
      /^screenshot_\d+\.(png|jpg|jpeg)$/i,  // Browser screenshot patterns
      /^webp_\d+\.(png|jpg|jpeg)$/i,  // WebP conversion patterns
      /^untitled\.(png|jpg|jpeg)$/i,  // Untitled downloads
      /^image\.(png|jpg|jpeg)$/i,  // Generic image names
    ];
    
    for (const pattern of browserDownloadPatterns) {
      if (pattern.test(fileName)) {
        return true;
      }
    }
    
    // Check for very short or generic names (likely browser downloads)
    if (fileName.length < 10 && !fileName.includes('warcraft') && !fileName.includes('wc')) {
      return true;
    }
    
    return false;
  }

  async detectGameType(filePath, suggestedType) {
    // Try multiple detection methods
    
    // 1. Use suggested type if valid
    if (suggestedType && suggestedType !== 'auto-detect') {
      return suggestedType;
    }

    // 2. Check file path for game indicators
    const pathLower = filePath.toLowerCase();
    for (const [gameType, pattern] of Object.entries(this.screenshotPatterns)) {
      for (const gameSpecificPath of pattern.gameSpecificPaths) {
        if (pathLower.includes(gameSpecificPath.toLowerCase())) {
          console.log(`🎯 Game detected from path: ${gameType}`);
          return gameType;
        }
      }
    }

    // 3. Check filename patterns
    const fileName = path.basename(filePath).toLowerCase();
    for (const [gameType, pattern] of Object.entries(this.screenshotPatterns)) {
      for (const filePattern of pattern.filePatterns) {
        if (filePattern.test(fileName)) {
          console.log(`🎯 Game detected from filename: ${gameType}`);
          return gameType;
        }
      }
    }

    // 4. Check if any Warcraft game is currently running
    if (this.gameDetector) {
      const runningGames = await this.gameDetector.getDetectedGames();
      const warcraftGames = runningGames.filter(game => 
        game.name.toLowerCase().includes('warcraft')
      );
      
      if (warcraftGames.length === 1) {
        const detectedGame = this.mapGameToType(warcraftGames[0].name);
        if (detectedGame) {
          console.log(`🎯 Game detected from running process: ${detectedGame}`);
          return detectedGame;
        }
      }
    }

    // 5. Only default to Warcraft 3 if we're in a game-specific directory
    const filePathLower = filePath.toLowerCase();
    const isInGameDirectory = filePathLower.includes('warcraft') || 
                             filePathLower.includes('wc1') || 
                             filePathLower.includes('wc2') || 
                             filePathLower.includes('wc3') ||
                             filePathLower.includes('war2') ||
                             filePathLower.includes('war3');
    
    if (isInGameDirectory) {
      console.log('🤔 Could not detect specific game type, but in game directory, defaulting to warcraft3');
      return 'warcraft3';
    }
    
    console.log('🚫 Not in game directory and no game type detected, skipping');
    return null;
  }

  mapGameToType(gameName) {
    const nameLower = gameName.toLowerCase();
    if (nameLower.includes('warcraft iii') || nameLower.includes('warcraft 3') || nameLower.includes('wc3')) {
      return 'warcraft3';
    }
    if (nameLower.includes('warcraft ii') || nameLower.includes('warcraft 2') || nameLower.includes('wc2')) {
      return 'warcraft2';
    }
    if (nameLower.includes('warcraft') && !nameLower.includes('ii') && !nameLower.includes('2') && !nameLower.includes('3')) {
      return 'warcraft1';
    }
    return null;
  }

  async organizeScreenshot(filePath, gameType) {
    try {
      const fileName = path.basename(filePath);
      const userHome = os.homedir();
      const screenshotsBase = path.join(userHome, 'Documents', 'WarcraftArena', 'Screenshots');
      const gameFolder = path.join(screenshotsBase, gameType);
      
      // Create directories if they don't exist
      await fs.mkdir(gameFolder, { recursive: true });
      
      // Create date-based subfolder
      const now = new Date();
      const dateFolder = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
      const targetFolder = path.join(gameFolder, dateFolder);
      await fs.mkdir(targetFolder, { recursive: true });
      
      // Generate unique filename if file already exists
      let targetPath = path.join(targetFolder, fileName);
      let counter = 1;
      while (await this.fileExists(targetPath)) {
        const nameWithoutExt = path.parse(fileName).name;
        const ext = path.parse(fileName).ext;
        targetPath = path.join(targetFolder, `${nameWithoutExt}_${counter}${ext}`);
        counter++;
      }
      
      // Move the file
      await fs.rename(filePath, targetPath);
      console.log(`📁 Organized screenshot: ${fileName} → ${gameType}/${dateFolder}/`);
      
      return targetPath;
      
    } catch (error) {
      console.error('❌ Error organizing screenshot:', error);
      return filePath; // Return original path if organization fails
    }
  }

  queueForAnalysis(screenshotData) {
    if (this.analysisQueue.length >= this.maxQueueSize) {
      console.log('⚠️ Analysis queue full, removing oldest item');
      this.analysisQueue.shift();
    }

    this.analysisQueue.push(screenshotData);
    console.log(`📋 Queued for analysis: ${path.basename(screenshotData.filePath)} (Queue: ${this.analysisQueue.length})`);

    // Start processing if not already running
    if (!this.isProcessingQueue) {
      this.processAnalysisQueue();
    }
  }

  async processAnalysisQueue() {
    if (this.isProcessingQueue) return;
    
    this.isProcessingQueue = true;
    console.log('🔄 Started processing analysis queue...');

    try {
      while (this.analysisQueue.length > 0) {
        const screenshotData = this.analysisQueue.shift();
        await this.analyzeScreenshot(screenshotData);
        
        // Small delay to prevent overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('❌ Error processing analysis queue:', error);
    } finally {
      this.isProcessingQueue = false;
      console.log('✅ Analysis queue processing complete');
    }
  }

  async analyzeScreenshot(screenshotData) {
    try {
      console.log(`🔍 Analyzing screenshot: ${path.basename(screenshotData.filePath)}`);
      
      // Perform image analysis
      const analysis = await this.imageAnalyzer.analyzeImage(screenshotData.filePath, screenshotData.gameType);
      
      // Update stats
      this.stats.analyzed++;
      if (analysis.result === 'victory') this.stats.victories++;
      if (analysis.result === 'defeat') this.stats.defeats++;
      this.stats.averageConfidence = (this.stats.averageConfidence * (this.stats.analyzed - 1) + analysis.confidence) / this.stats.analyzed;
      
      // Emit analysis result
      this.emit('analysisComplete', {
        ...analysis,
        filePath: screenshotData.filePath,
        gameType: screenshotData.gameType,
        timestamp: screenshotData.timestamp || new Date(),
        isGameResult: true
      });
      
      // Send to backend if auto-report is enabled
      if (this.settings.autoReport && analysis.confidence >= this.settings.confidenceThreshold) {
        await this.sendAnalysisToBackend(analysis);
      }
      
      return analysis;
      
    } catch (error) {
      console.error('❌ Error analyzing screenshot:', error);
      return null;
    }
  }

  async sendAnalysisToBackend(analysis) {
    try {
      console.log('📤 Sending analysis to backend...');
      
      const response = await fetch('http://127.0.0.1:3001/api/screenshot-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify({
          imagePath: analysis.imagePath,
          gameType: analysis.gameType,
          result: analysis.result,
          confidence: analysis.confidence,
          players: analysis.players || [],
          mapName: analysis.mapName,
          duration: analysis.duration,
          analysis: analysis.analysis
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Analysis sent to backend successfully');
        
        if (result.data.autoReported) {
          this.stats.autoReported++;
          console.log('🚀 Match auto-reported to ladder');
        }

        // Show notification if enabled
        if (this.settings.notifications) {
          this.showNotification(analysis, result.data);
        }

      } else {
        console.error('❌ Failed to send analysis to backend:', response.status);
      }

    } catch (error) {
      console.error('❌ Error sending analysis to backend:', error);
    }
  }

  showNotification(analysis, backendResult) {
    try {
      const { result, confidence, players } = analysis;
      const title = result === 'victory' ? '🏆 Victory Detected!' : '💔 Defeat Detected';
      const opponent = players.find(p => p.name !== 'player')?.name || 'Unknown';
      
      let body = `${confidence}% confidence`;
      if (opponent !== 'Unknown') {
        body += `\nvs ${opponent}`;
      }
      if (backendResult.autoReported) {
        body += '\n✅ Automatically reported to ladder';
      }

      new Notification(title, {
        body,
        icon: path.join(__dirname, '../assets/icon.png'),
        silent: false
      });
      
    } catch (error) {
      console.error('❌ Error showing notification:', error);
    }
  }

  getAuthToken() {
    // Get JWT token from localStorage or electron store
    try {
      // This would depend on how authentication is handled in the app
      return localStorage.getItem('jwt') || '';
    } catch {
      return '';
    }
  }
  
  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
  
  async stopMonitoring() {
    if (!this.isMonitoring) return;

    console.log('🛑 Stopping screenshot monitoring...');
    this.isMonitoring = false;

    // Close all watchers
    for (const watcherInfo of this.watchers) {
      try {
        await watcherInfo.watcher.close();
      } catch (error) {
        console.error('Error closing watcher:', error);
      }
    }

    this.watchers = [];
    console.log('✅ Screenshot monitoring stopped');
  }

  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    console.log('⚙️ Screenshot settings updated:', newSettings);
    
    // Restart monitoring if game types changed
    if (newSettings.gameTypes && this.isMonitoring) {
      this.stopMonitoring();
      setTimeout(() => this.startMonitoring(), 1000);
    }
  }

  getStats() {
    const uptime = new Date() - this.stats.sessionStart;
    return {
      ...this.stats,
      uptime: Math.round(uptime / 1000), // seconds
      queueLength: this.analysisQueue.length,
      isMonitoring: this.isMonitoring,
      watchedDirectories: this.watchers.length
    };
  }

  getScreenshotCount() {
    return this.stats.detected;
  }

  getLastScreenshotTime() {
    return this.stats.sessionStart;
  }

  // Enhanced manual screenshot capture
  async captureManualScreenshot(gameType = 'warcraft3') {
    try {
      console.log('📸 Capturing manual screenshot...');
      
      const sources = await desktopCapturer.getSources({
        types: ['screen'],
        thumbnailSize: { width: 1920, height: 1080 }
      });

      if (sources.length === 0) {
        throw new Error('No screen sources available');
      }

      // Use primary screen
      const primarySource = sources[0];
      const timestamp = new Date().toISOString().replace(/:/g, '-').split('.')[0];
      const fileName = `manual_screenshot_${timestamp}.png`;
      
      // Organize into appropriate folder
      const userHome = os.homedir();
      const targetFolder = path.join(userHome, 'Documents', 'WarcraftArena', 'Screenshots', gameType, 'manual');
      await fs.mkdir(targetFolder, { recursive: true });
      
      const targetPath = path.join(targetFolder, fileName);
      
      // Convert thumbnail to PNG and save
      const thumbnailURL = primarySource.thumbnail.toDataURL();
      const base64Data = thumbnailURL.replace(/^data:image\/png;base64,/, '');
      await fs.writeFile(targetPath, base64Data, 'base64');
      
      console.log(`✅ Manual screenshot saved: ${fileName}`);
      
      // Queue for analysis
      this.queueForAnalysis({
        filePath: targetPath,
        gameType,
        isRecent: true,
        timestamp: new Date(),
        manual: true
      });
      
      return {
        success: true,
        filePath: targetPath,
        fileName
      };
      
    } catch (error) {
      console.error('❌ Error capturing manual screenshot:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Take screenshot (alias for captureManualScreenshot)
  async takeScreenshot() {
    return this.captureManualScreenshot();
  }

  // Get recent screenshots
  async getRecentScreenshots(limit = 10) {
    try {
      const userHome = os.homedir();
      const screenshotsBase = path.join(userHome, 'Documents', 'WarcraftArena', 'Screenshots');
      
      const screenshots = [];
      
      // Scan all game type folders
      for (const gameType of this.settings.gameTypes) {
        const gameFolder = path.join(screenshotsBase, gameType);
        
        try {
          const gameFiles = await fs.readdir(gameFolder, { recursive: true });
          
          for (const file of gameFiles) {
            if (typeof file === 'string' && /\.(png|jpg|jpeg|bmp|gif)$/i.test(file)) {
              const filePath = path.join(gameFolder, file);
              const stats = await fs.stat(filePath);
              
              screenshots.push({
                id: path.basename(file, path.extname(file)),
                filePath: filePath,
                gameType: gameType,
                timestamp: stats.mtime,
                size: stats.size,
                result: null, // Would be populated from analysis results
                confidence: null
              });
            }
          }
        } catch (error) {
          // Game folder doesn't exist, skip
          continue;
        }
      }
      
      // Sort by timestamp (newest first) and limit
      screenshots.sort((a, b) => b.timestamp - a.timestamp);
      return screenshots.slice(0, limit);
      
    } catch (error) {
      console.error('❌ Error getting recent screenshots:', error);
      return [];
    }
  }

  // Open screenshots folder
  async openScreenshotsFolder() {
    try {
      const userHome = os.homedir();
      const screenshotsBase = path.join(userHome, 'Documents', 'WarcraftArena', 'Screenshots');
      
      // Create folder if it doesn't exist
      await fs.mkdir(screenshotsBase, { recursive: true });
      
      // Open folder using system default
      const { shell } = require('electron');
      await shell.openPath(screenshotsBase);
      
      return { success: true };
    } catch (error) {
      console.error('❌ Error opening screenshots folder:', error);
      return { success: false, error: error.message };
    }
  }

  // Get analysis tools info
  async getAnalysisToolsInfo() {
    try {
      return [
        {
          name: 'Image Analysis',
          available: true,
          description: 'AI-powered screenshot analysis'
        },
        {
          name: 'OCR Text Recognition',
          available: true,
          description: 'Text extraction from screenshots'
        },
        {
          name: 'Game Result Detection',
          available: true,
          description: 'Victory/defeat detection'
        },
        {
          name: 'Player Name Recognition',
          available: true,
          description: 'Player name extraction'
        }
      ];
    } catch (error) {
      console.error('❌ Error getting analysis tools info:', error);
      return [];
    }
  }

  // Analyze specific image
  async analyzeImage(imagePath) {
    try {
      console.log(`🔍 Analyzing image: ${imagePath}`);
      
      // Detect game type from path
      const gameType = await this.detectGameType(imagePath);
      
      // Perform analysis
      const analysis = await this.imageAnalyzer.analyzeImage(imagePath, gameType);
      
      return {
        success: true,
        result: analysis
      };
    } catch (error) {
      console.error('❌ Error analyzing image:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = ScreenshotManager; 