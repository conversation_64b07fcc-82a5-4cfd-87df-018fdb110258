<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Enter the Arena - WC Arena</title>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="stylesheet" href="../css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Uncial+Antiqua&display=swap" rel="stylesheet">
  
  <style>
    /* ===== EPIC WARCRAFT LOGIN STYLING ===== */
    body {
      margin: 0;
      padding: 0;
      min-height: 100vh;
      background: linear-gradient(
        135deg,
        #0a0a0a 0%,
        #1a1a2e 15%,
        #16213e 30%,
        #0f3460 45%,
        #8b4513 60%,
        #2d1810 75%,
        #0a0a0a 100%
      );
      background-size: 400% 400%;
      animation: epicGradientShift 20s ease infinite;
      font-family: 'Cinzel', serif;
      overflow-x: hidden;
      position: relative;
    }

    @keyframes epicGradientShift {
      0% { background-position: 0% 50%; }
      25% { background-position: 100% 50%; }
      50% { background-position: 100% 100%; }
      75% { background-position: 0% 100%; }
      100% { background-position: 0% 50%; }
    }

    /* Animated Background Elements */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
        radial-gradient(3px 3px at 20px 30px, rgba(255, 215, 0, 0.6), transparent),
        radial-gradient(3px 3px at 40px 70px, rgba(255, 255, 255, 0.4), transparent),
        radial-gradient(2px 2px at 90px 40px, rgba(255, 215, 0, 0.8), transparent),
        radial-gradient(2px 2px at 130px 80px, rgba(255, 255, 255, 0.3), transparent),
        radial-gradient(1px 1px at 160px 30px, rgba(255, 215, 0, 0.9), transparent);
      background-repeat: repeat;
      background-size: 250px 150px;
      animation: magicalSparkle 6s linear infinite;
      pointer-events: none;
      z-index: 1;
    }

    @keyframes magicalSparkle {
      0% { transform: translateY(0) rotate(0deg) scale(1); opacity: 1; }
      100% { transform: translateY(-100vh) rotate(360deg) scale(0.5); opacity: 0; }
    }

    /* Floating Runes */
    body::after {
      content: '⚔️ 🛡️ 🏰 ⚡ 🔥 💎 👑 🗡️';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      font-size: clamp(20px, 3vw, 40px);
      letter-spacing: 100px;
      line-height: 200px;
      color: rgba(255, 215, 0, 0.2);
      animation: floatingRunes 30s linear infinite;
      pointer-events: none;
      z-index: 1;
    }

    @keyframes floatingRunes {
      0% { transform: translateY(100vh) rotate(0deg); }
      100% { transform: translateY(-100vh) rotate(360deg); }
    }

    /* Main Login Container */
    .login-container {
      position: relative;
      z-index: 10;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      background: rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(10px);
    }

    /* Epic Login Card */
    .login-card {
      position: relative;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 3rem 2.5rem;
      box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.6),
        0 0 100px rgba(255, 215, 0, 0.1);
      border: 2px solid transparent;
      background-clip: padding-box;
      position: relative;
      z-index: 3;
      width: 100%;
      max-width: 450px;
      margin: 2rem auto 8rem auto;
      animation: cardPulse 6s ease-in-out infinite, cardEntrance 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    @keyframes cardEntrance {
      0% {
        transform: translateY(100px) scale(0.9);
        opacity: 0;
        filter: blur(10px);
      }
      100% {
        transform: translateY(0) scale(1);
        opacity: 1;
        filter: blur(0);
      }
    }

    /* Animated Border */
    .login-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 25px;
      background: linear-gradient(45deg, 
        #ffd700, #ff6b35, #8b4513, #d4af37, 
        #ffd700, #ff6b35, #8b4513, #d4af37);
      background-size: 400% 400%;
      animation: borderGlow 4s ease infinite;
      z-index: -1;
      filter: blur(2px);
    }

    @keyframes borderGlow {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    /* Logo Section */
    .login-logo {
      text-align: center;
      margin-bottom: 2rem;
      position: relative;
    }

    .logo-emblem {
      width: 100px;
      height: 100px;
      margin: 0 auto 1.5rem;
      position: relative;
      animation: logoFloat 3s ease-in-out infinite;
    }

    @keyframes logoFloat {
      0%, 100% { transform: translateY(0) rotate(0deg); }
      50% { transform: translateY(-10px) rotate(5deg); }
    }

    .logo-emblem img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
    }

    /* Floating Decorations */
    .logo-decoration {
      position: absolute;
      width: 20px;
      height: 20px;
      background: radial-gradient(circle, #ffd700, transparent);
      border-radius: 50%;
      animation: decoration-float 4s ease-in-out infinite;
    }

    .logo-decoration:nth-child(2) { top: 10px; left: -30px; animation-delay: -1s; }
    .logo-decoration:nth-child(3) { top: 10px; right: -30px; animation-delay: -2s; }
    .logo-decoration:nth-child(4) { bottom: 10px; left: -20px; animation-delay: -3s; }
    .logo-decoration:nth-child(5) { bottom: 10px; right: -20px; animation-delay: -4s; }

    @keyframes decoration-float {
      0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.6; }
      50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
    }

    /* Login Title */
    .login-title {
      font-size: clamp(2rem, 5vw, 3rem);
      font-weight: 800;
      color: #ffd700;
      text-shadow: 
        0 0 20px rgba(255, 215, 0, 0.8),
        0 0 40px rgba(255, 215, 0, 0.6),
        0 0 60px rgba(255, 215, 0, 0.4);
      margin-bottom: 1rem;
      text-align: center;
      letter-spacing: 2px;
      animation: titleGlow 4s ease-in-out infinite;
    }

    @keyframes titleGlow {
      0%, 100% { text-shadow: 
        0 0 20px rgba(255, 215, 0, 0.8),
        0 0 40px rgba(255, 215, 0, 0.6),
        0 0 60px rgba(255, 215, 0, 0.4); }
      50% { text-shadow: 
        0 0 30px rgba(255, 215, 0, 1),
        0 0 50px rgba(255, 215, 0, 0.8),
        0 0 70px rgba(255, 215, 0, 0.6); }
    }

    /* Login Subtitle */
    .login-subtitle {
      font-size: clamp(0.9rem, 2vw, 1.1rem);
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
      margin-bottom: 2.5rem;
      line-height: 1.6;
      font-weight: 400;
      letter-spacing: 1px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
    }

    /* Login Buttons */
    .login-buttons {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .login-btn {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 1rem;
      padding: 1.2rem 2rem;
      border: 2px solid transparent;
      border-radius: 15px;
      font-size: 1.1rem;
      font-weight: 600;
      text-decoration: none;
      color: white;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      overflow: hidden;
      cursor: pointer;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
      letter-spacing: 1px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
      backdrop-filter: blur(10px);
    }

    .login-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.5s ease;
    }

    .login-btn:hover::before {
      left: 100%;
    }

    .login-btn:hover {
      transform: translateY(-3px) scale(1.02);
      box-shadow: 
        0 15px 30px rgba(0, 0, 0, 0.4),
        0 0 40px rgba(255, 215, 0, 0.3);
      border-color: rgba(255, 215, 0, 0.5);
    }

    .login-btn:active {
      transform: translateY(-1px) scale(0.98);
    }

    .login-btn img {
      width: 24px;
      height: 24px;
      object-fit: contain;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
    }

    /* Specific button styles */
    .login-btn.btn-google {
      background: linear-gradient(135deg, #4285f4, #1a73e8);
      border: 2px solid rgba(66, 133, 244, 0.5);
    }

    .login-btn.btn-discord {
      background: linear-gradient(135deg, #7289da, #5865f2);
      border: 2px solid rgba(114, 137, 218, 0.5);
    }

    .login-btn.btn-twitch {
      background: linear-gradient(135deg, #9146ff, #6441a5);
      border: 2px solid rgba(145, 70, 255, 0.5);
    }

    /* Loading states */
    .login-btn.loading {
      pointer-events: none;
      opacity: 0.8;
    }

    .login-btn.loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 20px;
      height: 20px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: translate(-50%, -50%) rotate(0deg); }
      100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Login Footer */
    .login-footer {
      text-align: center;
      font-size: 0.85rem;
      color: rgba(255, 255, 255, 0.6);
      line-height: 1.6;
      margin-top: 1.5rem;
    }

    .login-footer a {
      color: #ffd700;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    }

    .login-footer a:hover {
      color: #ffed4e;
      text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
    }

    /* Epic Quote */
    .epic-quote {
      position: absolute;
      bottom: 2rem;
      left: 50%;
      transform: translateX(-50%);
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
      font-size: clamp(0.8rem, 1.5vw, 1rem);
      font-style: italic;
      max-width: 600px;
      padding: 0 2rem;
      z-index: 2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
    }

    /* Electron-specific elements */
    .server-status {
      margin-top: 1rem;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      font-size: 0.85rem;
      display: none;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
    }

    .server-status.show {
      display: flex;
    }

    .server-status.online {
      color: #4caf50;
      border: 1px solid rgba(76, 175, 80, 0.3);
    }

    .server-status.offline {
      color: #f44336;
      border: 1px solid rgba(244, 67, 54, 0.3);
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }

    .server-status.online .status-dot {
      background: #4caf50;
    }

    .server-status.offline .status-dot {
      background: #f44336;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

    .error-message {
      background: rgba(220, 53, 69, 0.1);
      border: 1px solid rgba(220, 53, 69, 0.3);
      color: #dc3545;
      padding: 1rem;
      border-radius: 8px;
      margin: 1rem 0;
      text-align: center;
      opacity: 0;
      transform: translateY(-10px);
      transition: all 0.3s ease;
      font-weight: 500;
    }

    .error-message.show {
      opacity: 1;
      transform: translateY(0);
    }

    .error-message.success {
      background: rgba(40, 167, 69, 0.1);
      border: 1px solid rgba(40, 167, 69, 0.3);
      color: #28a745;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .login-card {
        padding: 2rem 1.5rem;
        margin: 1rem auto 4rem auto;
      }

      .login-title {
        font-size: 2rem;
      }

      .login-btn {
        padding: 1rem 1.5rem;
        font-size: 1rem;
      }

      .epic-quote {
        font-size: 0.8rem;
        bottom: 1rem;
      }
    }
  </style>
  <!-- Universal Navbar Consistency - MUST be loaded last for proper override -->
  <link rel="stylesheet" href="/css/navbar-universal.css" />
</head>
<body>
  <div class="login-container">
    <div class="login-card">
      <div class="login-logo">
        <div class="logo-emblem">
          <img src="../assets/img/ranks/emblem.png" alt="WC Arena Emblem" />
          <div class="logo-decoration"></div>
          <div class="logo-decoration"></div>
          <div class="logo-decoration"></div>
          <div class="logo-decoration"></div>
        </div>
      </div>

      <h1 class="login-title">WC Arena</h1>
      <p class="login-subtitle">Enter the legendary battlegrounds where champions are forged and legends are born</p>

      <div class="login-buttons">
        <div class="login-btn btn-google" data-provider="google">
          <img src="../assets/img/socials/google.png" alt="Google" />
          <span>Forge Your Legacy with Google</span>
        </div>
        <div class="login-btn btn-discord" data-provider="discord">
          <img src="../assets/img/socials/discord.png" alt="Discord" />
          <span>Join the Alliance via Discord</span>
        </div>
        <div class="login-btn btn-twitch" data-provider="twitch">
          <img src="../assets/img/socials/twitch.png" alt="Twitch" />
          <span>Stream to Glory with Twitch</span>
        </div>
      </div>

      <div class="server-status" id="serverStatus">
        <div class="status-dot"></div>
        <span>Checking server connection...</span>
      </div>

      <div class="error-message" id="errorMessage">
        Error message will appear here
      </div>

      <div class="login-footer">
        <p>By entering the arena, you pledge to uphold our <a href="#" onclick="showTerms()">Sacred Code</a> and <a href="#" onclick="showPrivacy()">Honor's Promise</a></p>
      </div>
    </div>
    
    <div class="epic-quote">
      "Victory is reserved for those who are willing to pay its price."<br>
      <em>— Sun Tzu, The Art of War</em>
    </div>
  </div>

  <script>
    // ===== UNIFIED LOGIN SYSTEM =====
    // Detects environment and uses appropriate authentication method
    
    class UnifiedAuth {
      constructor() {
        this.isElectron = this.detectElectron();
        this.isLoading = false;
        this.availableProviders = [];
        this.serverUrl = 'http://127.0.0.1:3001';  // Use port 3001 for OAuth compatibility
        
        console.log('🎮 UnifiedAuth initialized - Environment:', this.isElectron ? 'Electron' : 'Web Browser');
        this.init();
      }

      detectElectron() {
        return typeof window !== 'undefined' && 
               typeof window.electronAPI !== 'undefined' && 
               window.electronAPI !== null;
      }

      async init() {
        try {
          if (this.isElectron) {
            await this.initElectron();
          } else {
            await this.initWeb();
          }
          
          this.setupEventListeners();
          this.setupAnimations();
          console.log('✅ UnifiedAuth initialization complete');
        } catch (error) {
          console.error('❌ UnifiedAuth initialization failed:', error);
          this.showError('Failed to initialize authentication system');
        }
      }

      async initElectron() {
        console.log('🖥️ Initializing Electron authentication...');
        
        // Get configuration from main process
        try {
          const configResult = await window.electronAPI.config.get();
          if (configResult.success && configResult.config) {
            this.serverUrl = configResult.config.serverUrl || 'http://127.0.0.1:3001';  // Use port 3001 for API calls
          }
        } catch (error) {
          console.warn('⚠️ Could not get Electron config:', error);
        }
        
        // Show server status for Electron
        this.showServerStatus();
        
        // Check server status and available providers
        await this.checkServerStatus();
        
        // Add always-visible test button for debugging
        const alwaysVisibleTestButton = document.createElement('button');
        alwaysVisibleTestButton.textContent = 'Test Protocol URL';
        alwaysVisibleTestButton.style.cssText = 'position: fixed; top: 10px; left: 10px; z-index: 10000; padding: 5px; background: green; color: white; border: none; cursor: pointer; font-size: 12px;';
        alwaysVisibleTestButton.onclick = async () => {
          console.log('🧪 Testing protocol URL handling (always visible)...');
          const testUrl = 'warcraftarena://oauth-success?token=test-token&state=test-state';
          try {
            console.log('🧪 Calling test-protocol-url IPC handler...');
            const result = await window.electronAPI.invoke('test-protocol-url', testUrl);
            console.log('🧪 Test protocol URL result:', result);
          } catch (error) {
            console.error('❌ Test protocol URL error:', error);
          }
        };
        document.body.appendChild(alwaysVisibleTestButton);
      }

      async initWeb() {
        console.log('🌐 Initializing Web authentication...');
        
        // For web, all providers are typically available
        this.availableProviders = ['google', 'discord', 'twitch'];
        
        // Convert buttons to proper links for web
        this.setupWebButtons();
      }

      setupWebButtons() {
        const buttons = document.querySelectorAll('.login-btn');
        buttons.forEach(button => {
          const provider = button.dataset.provider;
          
          // Convert div to anchor for web
          const anchor = document.createElement('a');
          anchor.href = `/auth/${provider}`;
          anchor.className = button.className;
          anchor.dataset.provider = provider;
          anchor.innerHTML = button.innerHTML;
          
          // Add loading effect on click
          anchor.addEventListener('click', function() {
            this.classList.add('loading');
          });
          
          button.parentNode.replaceChild(anchor, button);
        });
      }

      showServerStatus() {
        const statusElement = document.getElementById('serverStatus');
        statusElement.classList.add('show');
      }

      async checkServerStatus() {
        if (!this.isElectron) return;
        
        try {
          console.log(`🔍 Checking server status at: ${this.serverUrl}/auth/config`);
          const response = await fetch(`${this.serverUrl}/auth/config`);
          
          if (response.ok) {
            const data = await response.json();
            this.availableProviders = Object.keys(data.availableStrategies)
              .filter(key => data.availableStrategies[key]);
            
            console.log('✅ Available providers:', this.availableProviders);
            
            this.updateServerStatus(true);
            this.updateButtonAvailability();
            this.hideError();
          } else {
            throw new Error(`Server responded with status ${response.status}`);
          }
        } catch (error) {
          console.error('❌ Server check failed:', error);
          this.updateServerStatus(false);
          this.disableAllButtons();
          this.showError(`Cannot connect to server: ${error.message}`);
        }
      }

      updateServerStatus(isOnline) {
        const statusElement = document.getElementById('serverStatus');
        
        if (isOnline) {
          statusElement.className = 'server-status show online';
          statusElement.innerHTML = '<div class="status-dot"></div><span>Server online</span>';
        } else {
          statusElement.className = 'server-status show offline';
          statusElement.innerHTML = '<div class="status-dot"></div><span>Server offline</span>';
        }
      }

      updateButtonAvailability() {
        const buttons = document.querySelectorAll('.login-btn');
        buttons.forEach(button => {
          const provider = button.dataset.provider;
          const isAvailable = this.availableProviders.includes(provider);
          
          if (!isAvailable) {
            button.style.opacity = '0.4';
            button.style.cursor = 'not-allowed';
            button.title = `${provider.charAt(0).toUpperCase() + provider.slice(1)} OAuth is not configured`;
          } else {
            button.style.opacity = '1';
            button.style.cursor = 'pointer';
            button.title = '';
          }
        });
      }

      disableAllButtons() {
        const buttons = document.querySelectorAll('.login-btn');
        buttons.forEach(button => {
          button.style.opacity = '0.4';
          button.style.cursor = 'not-allowed';
          button.title = 'Server is offline';
        });
      }

      setupEventListeners() {
        const buttons = document.querySelectorAll('.login-btn');
        buttons.forEach(button => {
          // Only add click handlers for Electron (web uses direct links)
          if (this.isElectron) {
            button.addEventListener('click', (e) => {
              e.preventDefault();
              const provider = button.dataset.provider;
              this.handleAuth(provider);
            });
          }
        });

        // Window focus event for Electron
        if (this.isElectron && window.electronAPI.onWindowFocus) {
          window.electronAPI.onWindowFocus = () => {
            this.checkServerStatus();
          };
        }
      }

      async handleAuth(provider) {
        if (this.isLoading) return;
        
        // Check if provider is available
        if (!this.availableProviders.includes(provider)) {
          this.showError(`${provider.charAt(0).toUpperCase() + provider.slice(1)} authentication is not available`);
          return;
        }
        
        try {
          this.setLoading(true, provider);
          this.hideError();
          
          console.log(`🔐 Starting ${provider} authentication...`);
          
          if (this.isElectron) {
            await this.handleElectronAuth(provider);
          } else {
            // Web authentication is handled by direct links
            window.location.href = `/auth/${provider}`;
          }
        } catch (error) {
          console.error(`❌ Authentication failed:`, error);
          this.showError(error.message || 'Authentication failed. Please try again.');
          this.setLoading(false);
        }
      }

      async handleElectronAuth(provider) {
        try {
          // Set up event listeners for OAuth results
          this.setupOAuthEventListeners();
          
          const result = await window.electronAPI.auth.login(provider);
          
          if (result.success) {
            console.log('✅ OAuth initiated successfully:', result.message);
            // Keep loading state - the actual completion will be handled by event listeners
            this.showMessage('Opening login in your default browser...');
          } else {
            throw new Error(result.error || 'Authentication failed');
          }
        } catch (error) {
          console.error('❌ OAuth initiation failed:', error);
          this.setLoading(false);
          throw error;
        }
      }

            setupOAuthEventListeners() {
        console.log('🔧 Setting up OAuth event listeners...');
        
        // Listen for OAuth success
        window.electronAPI.onOAuthSuccess((data) => {
          console.log('🎉 OAuth success received:', data);
          this.setLoading(false);
          this.showMessage('Login successful! Redirecting...');
          
          // Trigger page transition to authenticated user page
          setTimeout(async () => {
            try {
              console.log('🔄 Triggering page transition to authenticated user page...');
              const result = await window.electronAPI.navigation.loadPage('fullscreen');
              console.log('✅ Page transition result:', result);
            } catch (error) {
              console.error('❌ Page transition failed:', error);
              this.showError('Failed to load main application. Please try again.');
            }
          }, 1500);
        });
        
        // Listen for OAuth errors
        window.electronAPI.onOAuthError((data) => {
          console.error('❌ OAuth error received:', data);
          this.setLoading(false);
          this.showError(data.error || 'Authentication failed');
        });

        // Listen for OAuth timeout
        window.electronAPI.onOAuthTimeout((data) => {
          console.log('⏰ OAuth timeout received:', data);
          this.setLoading(false);
          this.showError(data.message || 'Login timed out. Please try again.');
        });
        
        console.log('✅ OAuth event listeners set up successfully');
        
        // Add fallback authentication check for protocol URL issues
        this.startAuthCheckFallback();
        
        // Add test button for protocol URL handling (for debugging)
        if (this.isElectron) {
          const testButton = document.createElement('button');
          testButton.textContent = 'Test Protocol URL';
          testButton.style.cssText = 'position: fixed; top: 10px; left: 10px; z-index: 10000; padding: 5px; background: red; color: white; border: none; cursor: pointer;';
          testButton.onclick = () => {
            console.log('🧪 Testing protocol URL handling...');
            // Simulate a protocol URL callback
            const testUrl = 'warcraftarena://oauth-success?token=test-token&state=test-state';
            window.electronAPI.invoke('test-protocol-url', testUrl);
          };
          document.body.appendChild(testButton);
          
          // Also add a test button that's always visible (for debugging)
          const alwaysVisibleTestButton = document.createElement('button');
          alwaysVisibleTestButton.textContent = 'Always Test Protocol';
          alwaysVisibleTestButton.style.cssText = 'position: fixed; top: 40px; left: 10px; z-index: 10000; padding: 5px; background: blue; color: white; border: none; cursor: pointer;';
          alwaysVisibleTestButton.onclick = () => {
            console.log('🧪 Testing protocol URL handling (always visible)...');
            const testUrl = 'warcraftarena://oauth-success?token=test-token&state=test-state';
            window.electronAPI.invoke('test-protocol-url', testUrl);
          };
          document.body.appendChild(alwaysVisibleTestButton);
        }
      }

      showMessage(message) {
        const errorElement = document.getElementById('errorMessage');
        errorElement.textContent = message;
        errorElement.classList.add('show', 'success');
        
        // Auto-hide after 5 seconds for success messages
        setTimeout(() => {
          this.hideMessage();
        }, 5000);
      }

      hideMessage() {
        const errorElement = document.getElementById('errorMessage');
        errorElement.classList.remove('show', 'success');
      }

      setLoading(loading, provider = null) {
        this.isLoading = loading;
        
        if (provider) {
          const button = document.querySelector(`[data-provider="${provider}"]`);
          if (button) {
            if (loading) {
              button.classList.add('loading');
            } else {
              button.classList.remove('loading');
            }
          }
        } else {
          // Set loading state for all buttons
          const buttons = document.querySelectorAll('.login-btn');
          buttons.forEach(button => {
            if (loading) {
              button.classList.add('loading');
            } else {
              button.classList.remove('loading');
            }
          });
        }
      }

      showError(message) {
        const errorElement = document.getElementById('errorMessage');
        errorElement.textContent = message;
        errorElement.classList.add('show');
        
        // Auto-hide after 10 seconds
        setTimeout(() => {
          this.hideError();
        }, 10000);
      }

      hideError() {
        const errorElement = document.getElementById('errorMessage');
        errorElement.classList.remove('show');
      }

      setupAnimations() {
        // Epic entrance animations
        const buttons = document.querySelectorAll('.login-btn');
        buttons.forEach((button, index) => {
          button.style.animationDelay = `${0.6 + index * 0.2}s`;
          button.style.animation = 'cardEntrance 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) both';
        });

        // Add click sound effect
        buttons.forEach(button => {
          button.addEventListener('click', function() {
            this.style.transform = 'translateY(-1px) scale(0.98)';
            setTimeout(() => {
              this.style.transform = '';
            }, 150);
          });
        });

        // Easter egg: Konami code
        this.setupKonamiCode();
        
        // Mouse parallax effect
        this.setupParallax();
      }

      setupKonamiCode() {
        let konamiCode = [];
        const targetCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];
        
        document.addEventListener('keydown', (e) => {
          konamiCode.push(e.code);
          if (konamiCode.length > targetCode.length) {
            konamiCode.shift();
          }
          
          if (konamiCode.join(',') === targetCode.join(',')) {
            this.activateEasterEgg();
          }
        });
      }

      activateEasterEgg() {
        document.body.style.animation = 'epicGradientShift 1s ease infinite';
        const title = document.querySelector('.login-title');
        title.textContent = 'LEGENDARY WARRIOR DETECTED';
        title.style.fontSize = '2rem';
        
        setTimeout(() => {
          title.textContent = 'WC Arena';
          title.style.fontSize = '';
          document.body.style.animation = '';
        }, 3000);
      }

      setupParallax() {
        document.addEventListener('mousemove', (e) => {
          const card = document.querySelector('.login-card');
          const x = (e.clientX / window.innerWidth) * 2 - 1;
          const y = (e.clientY / window.innerHeight) * 2 - 1;
          
          card.style.transform = `perspective(1000px) rotateY(${x * 5}deg) rotateX(${-y * 5}deg)`;
        });

        document.addEventListener('mouseleave', () => {
          const card = document.querySelector('.login-card');
          card.style.transform = '';
        });
      }
      
      // Fallback authentication check for protocol URL issues
      startAuthCheckFallback() {
        if (!this.isElectron) return;
        
        console.log('🔄 Starting fallback authentication check...');
        
        // Check for authentication every 2 seconds for up to 2 minutes
        let checkCount = 0;
        const maxChecks = 60; // 2 minutes (60 * 2 seconds)
        
        const checkAuth = async () => {
          if (checkCount >= maxChecks) {
            console.log('⏰ Fallback auth check timeout reached');
            return;
          }
          
          checkCount++;
          
          try {
            console.log(`🔍 Fallback auth check ${checkCount}/${maxChecks}...`);
            
            // Check if user is authenticated by calling the API
            const response = await fetch(`${this.serverUrl}/api/user/profile`, {
              credentials: 'include',
              headers: {
                'Accept': 'application/json'
              }
            });
            
            console.log(`🔍 Fallback auth response status: ${response.status}`);
            
            if (response.ok) {
              const userData = await response.json();
              console.log(`🔍 Fallback auth response data:`, userData);
              
              if (userData && userData.username) {
                console.log('✅ Fallback auth check successful - user is authenticated:', userData.username);
                
                // Mark authentication as established to allow API calls
                window.electronAuthEstablished = true;
                console.log('✅ Electron authentication established via fallback - API calls now allowed');
                
                // Notify the main process that authentication was successful
                try {
                  console.log('🔄 Calling auth:fallback-success IPC handler...');
                  const result = await window.electronAPI.invoke('auth:fallback-success', {
                    user: userData,
                    token: null // Token is handled by session cookies
                  });
                  console.log('✅ auth:fallback-success IPC result:', result);
                } catch (error) {
                  console.error('❌ Failed to notify main process of fallback auth success:', error);
                }
                
                return; // Stop checking
              } else {
                console.log('❌ Fallback auth check failed - no username in response');
              }
            } else {
              console.log(`❌ Fallback auth check failed - response not ok: ${response.status}`);
            }
            
            // If not authenticated, continue checking
            setTimeout(checkAuth, 2000);
            
          } catch (error) {
            console.log(`⚠️ Fallback auth check ${checkCount} failed:`, error.message);
            // Continue checking even if this attempt failed
            setTimeout(checkAuth, 2000);
          }
        };
        
        // Start the first check after a short delay
        setTimeout(checkAuth, 2000);
      }
    }

    // ===== DIALOG FUNCTIONS =====
    
    function showTerms() {
      showSimpleAlert(
        'Sacred Code of Honor ⚔️',
        `🏆 WARRIOR'S CREED:\n\n• Fight with honor and respect thy opponents\n• Victory through skill, not through deception\n• Protect the realm from trolls and griefers\n• Share knowledge and lift up fellow warriors\n\n⚖️ ARENA LAWS:\n\n• No cheating, hacking, or exploits\n• Respect match results and sportsmanship\n• Keep discussions civil and constructive\n• Report misconduct to maintain honor\n\n"May the best warrior win, and may all battles be legendary!"`
      );
    }

    function showPrivacy() {
      showSimpleAlert(
        'Honor\'s Promise 🛡️',
        `🔒 GUARDIAN'S OATH:\n\nYour battle secrets and personal data are protected by the strongest magical wards.\n\n🛡️ WHAT WE GUARD:\n\n• Match histories and strategic preferences\n• Account information and social connections\n• Chat logs (encrypted and secure)\n• Performance analytics and rankings\n\n⚡ HOW WE PROTECT:\n\n• End-to-end encryption for sensitive data\n• No sharing with third-party merchants\n• Secure authentication through trusted allies\n• Regular security audits by our best mages\n\n"We guard your data as fiercely as our own fortress!"`
      );
    }

    function showSimpleAlert(title, message) {
      const overlay = document.createElement('div');
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        font-family: Arial, sans-serif;
      `;

      const alertBox = document.createElement('div');
      alertBox.style.cssText = `
        background: #1a1a2e;
        border: 3px solid #ffd700;
        border-radius: 15px;
        padding: 2rem;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
      `;

      const titleElement = document.createElement('h2');
      titleElement.textContent = title;
      titleElement.style.cssText = `
        color: #ffd700;
        margin: 0 0 1rem 0;
        font-size: 1.5rem;
        font-weight: bold;
        text-align: center;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
      `;

      const messageElement = document.createElement('pre');
      messageElement.textContent = message;
      messageElement.style.cssText = `
        color: #ffffff;
        font-size: 1rem;
        line-height: 1.6;
        margin: 0 0 2rem 0;
        white-space: pre-wrap;
        font-family: Arial, sans-serif;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 1);
      `;

      const closeButton = document.createElement('button');
      closeButton.textContent = 'I Understand';
      closeButton.style.cssText = `
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #1a1a2e;
        border: none;
        padding: 0.8rem 2rem;
        border-radius: 10px;
        font-weight: bold;
        font-size: 1rem;
        cursor: pointer;
        display: block;
        margin: 0 auto;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      `;

      closeButton.onclick = () => {
        document.body.removeChild(overlay);
      };

      overlay.onclick = (e) => {
        if (e.target === overlay) {
          document.body.removeChild(overlay);
        }
      };

      const handleEscape = (e) => {
        if (e.key === 'Escape') {
          document.body.removeChild(overlay);
          document.removeEventListener('keydown', handleEscape);
        }
      };
      document.addEventListener('keydown', handleEscape);

      alertBox.appendChild(titleElement);
      alertBox.appendChild(messageElement);
      alertBox.appendChild(closeButton);
      overlay.appendChild(alertBox);
      document.body.appendChild(overlay);
    }

    // ===== INITIALIZATION =====
    
    document.addEventListener('DOMContentLoaded', () => {
      new UnifiedAuth();
    });
  </script>
</body>
</html>
