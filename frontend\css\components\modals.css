/* ==========================================================================
   WARCRAFT ARENA - OPTIMIZED PLAYER MODAL SYSTEM
   Streamlined and compact modal styling for better UX
   ========================================================================== */

/* Reset and base modal setup */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: none;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Active modal states */
.modal.show,
.modal.active,
.modal[data-visible="true"] {
  display: flex;
  opacity: 1;
}

/* Modal content container - Optimized for player modals */
.modal-content {
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.98), 
    rgba(30, 41, 59, 0.95));
  border: 2px solid var(--primary-gold, #D4AF37);
  border-radius: var(--radius-xl, 16px);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.8),
    0 0 30px rgba(212, 175, 55, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  max-width: 90vw;
  max-height: 90vh;
  width: auto;
  position: relative;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal.show .modal-content,
.modal.active .modal-content,
.modal[data-visible="true"] .modal-content {
  transform: scale(1);
}

/* Player Modal Specific - Compact Design */
.player-stats-modal .modal-content {
  max-width: 800px;
  width: 90vw;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
}

/* Modal header - Compact and integrated */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem 0.75rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.1), 
    rgba(212, 175, 55, 0.05));
  position: relative;
}

.modal-title {
  font-family: var(--font-display, 'Cinzel', serif);
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-gold, #D4AF37);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Close button - Fixed positioning and visibility */
.close-modal,
.modal-close,
.close {
  position: absolute;
  top: 12px;
  right: 15px;
  background: rgba(220, 38, 38, 0.15);
  border: 2px solid rgba(220, 38, 38, 0.4);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc2626;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1001;
  text-decoration: none;
  line-height: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.close-modal:hover,
.modal-close:hover,
.close:hover {
  background: rgba(220, 38, 38, 0.3);
  border-color: rgba(220, 38, 38, 0.7);
  color: #ffffff;
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.5);
}

/* Modal body - No padding for player modals */
.modal-body {
  padding: 0;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Player Modal Header - Enhanced and prominent */
.player-modal-header {
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.12), 
    rgba(212, 175, 55, 0.06));
  border-radius: 0;
  padding: 1.25rem 1.5rem;
  margin: 0;
  border-bottom: 2px solid rgba(212, 175, 55, 0.3);
  border-top: none;
  border-left: none;
  border-right: none;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.player-info {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  flex: 1;
  margin-right: 3rem; /* Space for close button */
}

.player-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  flex-shrink: 0;
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.15), 
    rgba(212, 175, 55, 0.08));
  border-radius: 12px;
  border: 2px solid rgba(212, 175, 55, 0.4);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 0 8px rgba(212, 175, 55, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.player-rank::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    transparent 30%, 
    rgba(255, 255, 255, 0.1) 50%, 
    transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.player-rank:hover::before {
  transform: translateX(100%);
}

.player-rank:hover {
  border-color: rgba(212, 175, 55, 0.7);
  transform: scale(1.08);
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.4),
    0 0 15px rgba(212, 175, 55, 0.3);
}

.rank-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.player-details {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  flex: 1;
}

.player-name {
  font-family: var(--font-display, 'Cinzel', serif);
  font-size: 1.6rem;
  font-weight: 700;
  color: var(--primary-gold, #D4AF37);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.player-race-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.race-display {
  font-size: 0.85rem;
  color: var(--neutral-300, #cbd5e1);
  font-weight: 500;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}



/* Enhanced close button for player modal */
.player-modal-header .close-modal {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(220, 38, 38, 0.15);
  border: 2px solid rgba(220, 38, 38, 0.4);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc2626;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1001;
  text-decoration: none;
  line-height: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
}

.player-modal-header .close-modal:hover {
  background: rgba(220, 38, 38, 0.3);
  border-color: rgba(220, 38, 38, 0.7);
  color: #ffffff;
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.5);
}

.player-rank-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.rank-name {
  font-weight: 600;
  color: var(--neutral-200, #e2e8f0);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.player-mmr {
  font-weight: 500;
  color: var(--primary-gold, #D4AF37);
}

.mmr-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-gold, #D4AF37);
  display: block;
  line-height: 1;
}

.mmr-label {
  font-size: 0.7rem;
  color: var(--neutral-400, #94a3b8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Tab system - Compact design */
.modal-tabs {
  display: flex;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  margin: 0;
  gap: 0;
  background: rgba(0, 0, 0, 0.2);
}

.modal-tab {
  background: transparent;
  border: none;
  padding: 0.75rem 1.25rem;
  color: var(--neutral-400, #94a3b8);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  font-size: 0.8rem;
  flex: 1;
  text-align: center;
}

.modal-tab:hover {
  color: var(--neutral-200, #e2e8f0);
  background: rgba(255, 255, 255, 0.05);
}

.modal-tab.active {
  color: var(--primary-gold, #D4AF37);
  border-bottom-color: var(--primary-gold, #D4AF37);
  background: rgba(212, 175, 55, 0.1);
}

.modal-tab-content {
  display: none;
  flex: 1;
  overflow: hidden;
}

.modal-tab-content.active {
  display: flex;
  flex-direction: column;
}

/* Tab content containers */
.overview-content,
.matches-content,
.performance-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 1.5rem;
}

/* Stats Grid - Compact design */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg, 12px);
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(212, 175, 55, 0.3);
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 1.5rem;
  color: var(--primary-gold, #D4AF37);
  margin-bottom: 0.5rem;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--neutral-100, #f8fafc);
}

.stat-label {
  font-size: 0.75rem;
  color: var(--neutral-400, #94a3b8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Match Items - Enhanced with better styling and UX */
.match-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03), rgba(255, 255, 255, 0.01));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  z-index: 1;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.match-item:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
  border-color: rgba(212, 175, 55, 0.4);
  transform: translateY(-2px);
  z-index: 2;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.match-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.5), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.match-item:hover::before {
  opacity: 1;
}

/* Match outcome styling */
.match-item.match-win {
  border-left: 4px solid #22c55e;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05), rgba(255, 255, 255, 0.01));
}

.match-item.match-win:hover {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(255, 255, 255, 0.04));
  border-color: rgba(34, 197, 94, 0.4);
}

.match-item.match-loss {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(255, 255, 255, 0.01));
}

.match-item.match-loss:hover {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(255, 255, 255, 0.04));
  border-color: rgba(239, 68, 68, 0.4);
}

/* Enhanced match header */
.match-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.match-outcome {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.match-win .match-outcome {
  color: #22c55e;
}

.match-loss .match-outcome {
  color: #ef4444;
}

.match-outcome i {
  font-size: 1rem;
}

.match-type {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: 6px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--primary-gold, #D4AF37);
}

.match-date {
  color: var(--text-secondary, #94a3b8);
  font-size: 0.8rem;
  font-weight: 500;
}

/* Enhanced expand icon with better UX */
.match-expand-icon {
  color: var(--neutral-400, #94a3b8);
  transition: all 0.3s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
  position: relative;
}

.match-expand-icon::before {
  content: 'View Details';
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 10;
}

.match-expand-icon::after {
  content: '';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.match-item:hover .match-expand-icon::before,
.match-item:hover .match-expand-icon::after {
  opacity: 1;
  visibility: visible;
}

.match-expand-icon i {
  font-size: 0.9rem;
  color: var(--primary-gold, #D4AF37);
}

.match-item:hover .match-expand-icon {
  background: rgba(212, 175, 55, 0.2);
  border-color: rgba(212, 175, 55, 0.4);
  transform: scale(1.1);
}

.match-item:hover .match-expand-icon i {
  color: var(--primary-gold, #D4AF37);
}

/* Enhanced match details section */
.match-details {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 1rem;
  align-items: center;
}

.match-map {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary, #94a3b8);
  font-size: 0.85rem;
}

.match-map i {
  color: var(--primary-gold, #D4AF37);
  font-size: 0.8rem;
}

/* Enhanced players container */
.match-players-container {
  grid-column: 1 / -1;
}

.match-players {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.match-players.team-match {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 1rem;
  align-items: center;
}

.team {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.5rem;
  transition: all 0.3s ease;
}

.team.winning-team {
  border-color: rgba(34, 197, 94, 0.3);
  background: rgba(34, 197, 94, 0.05);
}

.team.losing-team {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(239, 68, 68, 0.05);
}

.team:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.team-label {
  color: var(--text-secondary, #94a3b8);
}

.team-result {
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  font-size: 0.6rem;
}

.team-result.winner {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.team-result.loser {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.team-players {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex-wrap: wrap;
}

.team-vs-separator {
  color: var(--primary-gold, #D4AF37);
  font-weight: bold;
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  background: rgba(212, 175, 55, 0.1);
  border-radius: 4px;
}

.match-type-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(212, 175, 55, 0.2);
  color: var(--primary-gold, #D4AF37);
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  font-size: 0.6rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced player links */
.player-link,
.current-player {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.player-link:hover,
.current-player:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.player-link.winner,
.current-player.winner {
  color: #22c55e;
}

.player-link.loser,
.current-player.loser {
  color: #ef4444;
}

.current-player {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.current-player .fa-star {
  color: var(--primary-gold, #D4AF37);
  font-size: 0.7rem;
}

.player-separator {
  color: var(--text-secondary, #94a3b8);
  font-size: 0.6rem;
}

.vs-separator {
  color: var(--primary-gold, #D4AF37);
  font-weight: bold;
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  background: rgba(212, 175, 55, 0.1);
  border-radius: 4px;
}

.winner-icon {
  color: #22c55e;
  font-size: 0.7rem;
}

.loser-icon {
  color: #ef4444;
  font-size: 0.7rem;
}

/* Enhanced MMR change */
.mmr-change {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.85rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.mmr-change.positive {
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.3);
  background: rgba(34, 197, 94, 0.05);
}

.mmr-change.negative {
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(239, 68, 68, 0.05);
}

.mmr-change i {
  font-size: 0.8rem;
}

/* Click for details notification */
.match-item::after {
  content: 'View Details';
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  background: rgba(212, 175, 55, 0.9);
  color: var(--text-dark, #000);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.6rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.match-item:hover::after {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive improvements for match items */
@media (max-width: 768px) {
  .match-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
  }
  
  .match-header {
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }
  
  .match-outcome {
    font-size: 0.75rem;
  }
  
  .match-type {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
  
  .match-date {
    font-size: 0.7rem;
  }
  
  .match-expand-icon {
    width: 24px;
    height: 24px;
  }
  
  .match-expand-icon i {
    font-size: 0.8rem;
  }
  
  .match-details {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .match-players.team-match {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .team-vs-separator {
    text-align: center;
    margin: 0.25rem 0;
  }
  
  .team {
    padding: 0.4rem;
  }
  
  .team-header {
    font-size: 0.6rem;
  }
  
  .team-players {
    gap: 0.2rem;
  }
  
  .player-link,
  .current-player {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
  
  .mmr-change {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
  }
  
  .match-type-badge {
    top: 0.25rem;
    right: 0.25rem;
    font-size: 0.5rem;
    padding: 0.1rem 0.2rem;
  }
  
  /* Hide tooltip on mobile to save space */
  .match-expand-icon::before,
  .match-expand-icon::after {
    display: none;
  }
  
  /* Adjust click for details notification */
  .match-item::after {
    font-size: 0.5rem;
    padding: 0.15rem 0.4rem;
    bottom: 0.25rem;
    right: 0.25rem;
  }
}

@media (max-width: 480px) {
  .match-item {
    padding: 0.5rem;
    border-radius: 8px;
  }
  
  .match-header {
    margin-bottom: 0.4rem;
    padding-bottom: 0.4rem;
  }
  
  .match-outcome {
    font-size: 0.7rem;
  }
  
  .match-type {
    font-size: 0.65rem;
    padding: 0.15rem 0.3rem;
  }
  
  .match-date {
    font-size: 0.65rem;
  }
  
  .match-expand-icon {
    width: 20px;
    height: 20px;
  }
  
  .match-expand-icon i {
    font-size: 0.7rem;
  }
  
  .team {
    padding: 0.3rem;
  }
  
  .team-header {
    font-size: 0.55rem;
  }
  
  .player-link,
  .current-player {
    font-size: 0.65rem;
    padding: 0.15rem 0.3rem;
  }
  
  .mmr-change {
    padding: 0.3rem 0.5rem;
    font-size: 0.7rem;
  }
  
  .match-type-badge {
    font-size: 0.45rem;
    padding: 0.05rem 0.15rem;
  }
  
  .match-item::after {
    font-size: 0.45rem;
    padding: 0.1rem 0.3rem;
  }
}

/* Enhanced animations for better UX */
@keyframes matchItemPulse {
  0% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 4px 16px rgba(212, 175, 55, 0.2);
  }
  100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.match-item:active {
  transform: translateY(-1px);
  animation: matchItemPulse 0.3s ease;
}

/* Improved focus states for accessibility */
.match-item:focus {
  outline: 2px solid var(--primary-gold, #D4AF37);
  outline-offset: 2px;
}

.match-item:focus-visible {
  outline: 2px solid var(--primary-gold, #D4AF37);
  outline-offset: 2px;
}

/* Additional visual enhancements */
.match-item {
  position: relative;
  overflow: hidden;
}

/* Subtle background pattern for match items */
.match-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.02) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.match-item:hover::before {
  opacity: 1;
}

/* Enhanced team styling with gradients */
.team.winning-team {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05), rgba(34, 197, 94, 0.02));
  border: 1px solid rgba(34, 197, 94, 0.3);
  position: relative;
  overflow: hidden;
}

.team.winning-team::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.5), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.team.winning-team:hover::before {
  opacity: 1;
}

.team.losing-team {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(239, 68, 68, 0.02));
  border: 1px solid rgba(239, 68, 68, 0.3);
  position: relative;
  overflow: hidden;
}

.team.losing-team::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(239, 68, 68, 0.5), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.team.losing-team:hover::before {
  opacity: 1;
}

/* Enhanced player link animations */
.player-link,
.current-player {
  position: relative;
  overflow: hidden;
}

.player-link::before,
.current-player::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.player-link:hover::before,
.current-player:hover::before {
  left: 100%;
}

/* Enhanced MMR change styling */
.mmr-change {
  position: relative;
  overflow: hidden;
}

.mmr-change.positive::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(34, 197, 94, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mmr-change.positive:hover::before {
  opacity: 1;
}

.mmr-change.negative::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(239, 68, 68, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mmr-change.negative:hover::before {
  opacity: 1;
}

/* Enhanced match type badge */
.match-type-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
  color: var(--primary-gold, #D4AF37);
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  font-size: 0.6rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.match-item:hover .match-type-badge {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(212, 175, 55, 0.2));
  border-color: rgba(212, 175, 55, 0.5);
  transform: scale(1.05);
}

/* Enhanced VS separator */
.team-vs-separator {
  color: var(--primary-gold, #D4AF37);
  font-weight: bold;
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.05));
  border-radius: 4px;
  border: 1px solid rgba(212, 175, 55, 0.2);
  position: relative;
  overflow: hidden;
}

.team-vs-separator::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.2), transparent);
  transition: left 0.6s ease;
}

.match-item:hover .team-vs-separator::before {
  left: 100%;
}

/* Enhanced match map styling */
.match-map {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary, #94a3b8);
  font-size: 0.85rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.match-item:hover .match-map {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.match-map i {
  color: var(--primary-gold, #D4AF37);
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.match-item:hover .match-map i {
  transform: scale(1.1);
}

/* Enhanced date styling */
.match-date {
  color: var(--text-secondary, #94a3b8);
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.match-item:hover .match-date {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--text-primary, #ffffff);
}

/* Enhanced outcome styling */
.match-outcome {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.match-win .match-outcome {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.05);
  border-color: rgba(34, 197, 94, 0.2);
}

.match-win:hover .match-outcome {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
}

.match-loss .match-outcome {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
}

.match-loss:hover .match-outcome {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.match-outcome i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.match-item:hover .match-outcome i {
  transform: scale(1.1);
}

/* Enhanced match type styling */
.match-type {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.05));
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: 6px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--primary-gold, #D4AF37);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.match-type::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.2), transparent);
  transition: left 0.5s ease;
}

.match-item:hover .match-type::before {
  left: 100%;
}

.match-item:hover .match-type {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(212, 175, 55, 0.08));
  border-color: rgba(212, 175, 55, 0.3);
  transform: translateY(-1px);
}

/* Loading state for match items */
.match-item.loading {
  opacity: 0.7;
  pointer-events: none;
}

.match-item.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-top: 2px solid var(--primary-gold, #D4AF37);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced accessibility for screen readers */
.match-item[role="button"] {
  cursor: pointer;
}

.match-item[role="button"]:focus {
  outline: 2px solid var(--primary-gold, #D4AF37);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .match-item,
  .match-item::before,
  .match-item::after,
  .player-link,
  .current-player,
  .team,
  .mmr-change,
  .match-type-badge,
  .team-vs-separator,
  .match-map,
  .match-date,
  .match-outcome,
  .match-type {
    transition: none;
    animation: none;
  }
  
  .match-item::before,
  .match-item::after,
  .player-link::before,
  .current-player::before,
  .team::before,
  .mmr-change::before,
  .team-vs-separator::before,
  .match-type::before {
    display: none;
  }
}

/* Pagination - Compact styling */
.matches-pagination {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.75rem;
  color: var(--neutral-400, #94a3b8);
}

.matches-range {
  font-weight: 500;
}

.page-info {
  font-weight: 600;
  color: var(--primary-gold, #D4AF37);
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.25rem;
}

.page-numbers {
  display: flex;
  gap: 0.15rem;
  align-items: center;
}

.page-numbers .btn {
  min-width: 1.8rem;
  height: 1.8rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.7rem;
}

.page-numbers .btn-primary {
  background: var(--primary-gold, #D4AF37);
  color: #000;
  border-color: var(--primary-gold, #D4AF37);
}

.page-numbers .btn-primary:hover {
  background: #FFD700;
  border-color: #FFD700;
  transform: translateY(-1px);
}

.page-numbers .btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--neutral-200, #e2e8f0);
  border-color: rgba(255, 255, 255, 0.2);
}

.page-numbers .btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.page-numbers .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.page-ellipsis {
  color: var(--neutral-400, #94a3b8);
  font-weight: 600;
  padding: 0 0.3rem;
  font-size: 0.7rem;
}

/* Previous/Next buttons - smaller */
.pagination-controls .btn {
  font-size: 0.7rem;
  padding: 0.3rem 0.6rem;
  min-height: auto;
  border-radius: 4px;
}

.pagination-controls .btn i {
  font-size: 0.6rem;
}

/* Responsive pagination */
@media (max-width: 768px) {
  .pagination-info {
    flex-direction: column;
    gap: 0.25rem;
    text-align: center;
    margin-bottom: 0.4rem;
  }
  
  .pagination-controls {
    flex-direction: column;
    gap: 0.4rem;
  }
  
  .page-numbers {
    justify-content: center;
  }
  
  .page-numbers .btn {
    min-width: 1.6rem;
    height: 1.6rem;
    font-size: 0.65rem;
  }
  
  .pagination-controls .btn {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
  }
}

@media (max-width: 480px) {
  .pagination-info {
    font-size: 0.7rem;
  }
  
  .page-numbers .btn {
    min-width: 1.4rem;
    height: 1.4rem;
    font-size: 0.6rem;
  }
  
  .pagination-controls .btn {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
  }
  
  .page-ellipsis {
    font-size: 0.6rem;
    padding: 0 0.2rem;
  }
}

/* ==========================================================================
   MATCH DETAILS MODAL - Enhanced Styling
   ========================================================================== */

/* Match Details Modal Specific */
.match-details-modal-content {
  max-width: 900px;
  width: 95vw;
  max-height: 90vh;
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.98), 
    rgba(30, 41, 59, 0.95));
  border: 2px solid var(--primary-gold, #D4AF37);
  border-radius: 16px;
  box-shadow: 
    0 25px 80px rgba(0, 0, 0, 0.9),
    0 0 40px rgba(212, 175, 55, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.match-details-modal-content .modal-header {
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.15), 
    rgba(212, 175, 55, 0.08));
  border-bottom: 2px solid rgba(212, 175, 55, 0.3);
  padding: 1.5rem 2rem;
  border-radius: 14px 14px 0 0;
}

.match-details-modal-content .modal-header h2 {
  font-family: 'Cinzel', serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-gold, #D4AF37);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.match-details-modal-content .modal-header h2 i {
  font-size: 1.25rem;
  color: rgba(212, 175, 55, 0.8);
}

.match-details-modal-content .modal-body {
  padding: 2rem;
  overflow-y: auto;
  max-height: calc(90vh - 120px);
}

/* Match Details Container */
.match-details-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Match Details Sections */
.match-details-section {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.05), 
    rgba(255, 255, 255, 0.02));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.match-details-section:hover {
  border-color: rgba(212, 175, 55, 0.3);
  box-shadow: 0 6px 30px rgba(0, 0, 0, 0.4);
}

.match-details-section h4 {
  font-family: 'Cinzel', serif;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-gold, #D4AF37);
  margin: 0 0 1.25rem 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-bottom: 1px solid rgba(212, 175, 55, 0.2);
  padding-bottom: 0.75rem;
}

.match-details-section h4 i {
  font-size: 1rem;
  color: rgba(212, 175, 55, 0.7);
}

/* Players & Teams Section */
.match-players-detailed {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Team Details */
.team-detail {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.08), 
    rgba(255, 255, 255, 0.04));
  border: 2px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  padding: 1.25rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.team-detail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.2), 
    rgba(255, 255, 255, 0.1));
}

.team-detail.winning-team {
  border-color: rgba(34, 197, 94, 0.4);
  background: linear-gradient(135deg, 
    rgba(34, 197, 94, 0.1), 
    rgba(34, 197, 94, 0.05));
}

.team-detail.winning-team::before {
  background: linear-gradient(90deg, 
    rgba(34, 197, 94, 0.8), 
    rgba(34, 197, 94, 0.4));
}

.team-detail.losing-team {
  border-color: rgba(239, 68, 68, 0.4);
  background: linear-gradient(135deg, 
    rgba(239, 68, 68, 0.1), 
    rgba(239, 68, 68, 0.05));
}

.team-detail.losing-team::before {
  background: linear-gradient(90deg, 
    rgba(239, 68, 68, 0.8), 
    rgba(239, 68, 68, 0.4));
}

.team-detail:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Team Header */
.team-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.team-label {
  font-family: 'Cinzel', serif;
  font-size: 1.1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.team-result {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.team-result.winner {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.4);
}

.team-result.loser {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.4);
}

/* Team Players */
.team-players-detailed {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Player Detail */
.player-detail {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.06), 
    rgba(255, 255, 255, 0.03));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
  position: relative;
}

.player-detail:hover {
  border-color: rgba(212, 175, 55, 0.3);
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.08), 
    rgba(212, 175, 55, 0.04));
  transform: translateX(4px);
}

.player-detail.current-player {
  border-color: rgba(212, 175, 55, 0.4);
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.12), 
    rgba(212, 175, 55, 0.06));
}

.player-detail.current-player::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, 
    var(--primary-gold, #D4AF37), 
    rgba(212, 175, 55, 0.6));
  border-radius: 0 2px 2px 0;
}

/* Player Header */
.player-detail-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
}

.player-detail-header i.fa-user {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

.player-detail-header i.fa-star {
  color: var(--primary-gold, #D4AF37);
  font-size: 0.875rem;
  animation: starPulse 2s ease-in-out infinite;
}

@keyframes starPulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 1; }
}

/* Result Badge */
.result-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.result-badge.winner {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.4);
}

.result-badge.loser {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.4);
}

/* Player Info */
.player-detail-info {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.player-race {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  text-transform: capitalize;
}

.player-mmr {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

/* Team VS Separator */
.team-vs-separator-detailed {
  text-align: center;
  font-family: 'Cinzel', serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-gold, #D4AF37);
  text-transform: uppercase;
  letter-spacing: 2px;
  margin: 1rem 0;
  padding: 1rem;
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.1), 
    rgba(212, 175, 55, 0.05));
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: 8px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  position: relative;
}

.team-vs-separator-detailed::before,
.team-vs-separator-detailed::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 30%;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 175, 55, 0.4), 
    transparent);
}

.team-vs-separator-detailed::before {
  left: 0;
}

.team-vs-separator-detailed::after {
  right: 0;
}

/* Match Information Section */
.match-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.match-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.06), 
    rgba(255, 255, 255, 0.03));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.match-info-item:hover {
  border-color: rgba(212, 175, 55, 0.3);
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.08), 
    rgba(212, 175, 55, 0.04));
  transform: translateY(-1px);
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-align: right;
}

.info-value.negative {
  color: #ef4444;
}

.info-value.positive {
  color: #22c55e;
}

/* No Match Details */
.no-match-details {
  text-align: center;
  padding: 2rem;
  color: rgba(255, 255, 255, 0.7);
}

.no-match-details i {
  font-size: 3rem;
  color: rgba(255, 255, 255, 0.3);
  margin-bottom: 1rem;
}

.no-match-details h4 {
  font-family: 'Cinzel', serif;
  font-size: 1.25rem;
  color: var(--primary-gold, #D4AF37);
  margin-bottom: 0.5rem;
}

.no-match-details p {
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .match-details-modal-content {
    width: 98vw;
    max-height: 95vh;
  }
  
  .match-details-modal-content .modal-header {
    padding: 1rem 1.5rem;
  }
  
  .match-details-modal-content .modal-header h2 {
    font-size: 1.25rem;
  }
  
  .match-details-modal-content .modal-body {
    padding: 1.5rem;
  }
  
  .match-details-section {
    padding: 1rem;
  }
  
  .match-details-section h4 {
    font-size: 1.1rem;
  }
  
  .team-detail {
    padding: 1rem;
  }
  
  .player-detail {
    padding: 0.75rem;
  }
  
  .player-detail-header {
    gap: 0.5rem;
  }
  
  .player-detail-info {
    gap: 0.75rem;
  }
  
  .match-info-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .team-vs-separator-detailed {
    font-size: 1.25rem;
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .match-details-modal-content .modal-header {
    padding: 0.75rem 1rem;
  }
  
  .match-details-modal-content .modal-body {
    padding: 1rem;
  }
  
  .match-details-section {
    padding: 0.75rem;
  }
  
  .team-detail {
    padding: 0.75rem;
  }
  
  .player-detail {
    padding: 0.5rem;
  }
  
  .player-detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .player-detail-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .match-info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .info-value {
    text-align: left;
  }
}

/* ==========================================================================
   MATCH DETAILS CONTENT - Enhanced Styling
   ========================================================================== */

/* Match Details Content Container */
.match-details-content {
  padding: 1.5rem;
  max-height: 400px;
  overflow-y: auto;
}

/* Match Details Sections */
.match-details-section {
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  padding: 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.match-details-section:last-child {
  margin-bottom: 0;
}

.match-details-section h4 {
  color: var(--primary-gold, #D4AF37);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.match-details-section h4 i {
  font-size: 1rem;
  opacity: 0.8;
}

/* FFA Match Styling */
.ffa-players-detailed {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.ffa-header {
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.1), 
    rgba(184, 134, 11, 0.05));
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  margin-bottom: 1rem;
}

.ffa-label {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-gold, #D4AF37);
  margin-bottom: 0.25rem;
}

.ffa-description {
  display: block;
  font-size: 0.9rem;
  color: #a0a0a0;
  font-style: italic;
}

.ffa-players-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

/* Team-based Match Styling */
.team-detail {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  margin-bottom: 1rem;
}

.team-detail.winning-team {
  background: linear-gradient(135deg, 
    rgba(76, 175, 80, 0.1), 
    rgba(56, 142, 60, 0.05));
  border-color: rgba(76, 175, 80, 0.3);
}

.team-detail.losing-team {
  background: linear-gradient(135deg, 
    rgba(244, 67, 54, 0.1), 
    rgba(211, 47, 47, 0.05));
  border-color: rgba(244, 67, 54, 0.3);
}

.team-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.team-label {
  font-weight: 600;
  color: #e0e0e0;
  font-size: 1rem;
}

.team-result {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.team-result.winner {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.4);
}

.team-result.loser {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.4);
}

.team-players-detailed {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.team-vs-separator-detailed {
  text-align: center;
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--primary-gold, #D4AF37);
  margin: 1rem 0;
  padding: 0.5rem;
  background: rgba(212, 175, 55, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(212, 175, 55, 0.2);
}

/* Individual Player Details */
.player-detail {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 6px;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
}

.player-detail:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.player-detail.current-player {
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.15), 
    rgba(184, 134, 11, 0.1));
  border-color: rgba(212, 175, 55, 0.4);
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.2);
}

.player-detail.winner {
  background: linear-gradient(135deg, 
    rgba(76, 175, 80, 0.15), 
    rgba(56, 142, 60, 0.1));
  border-color: rgba(76, 175, 80, 0.4);
}

.player-detail.loser {
  background: linear-gradient(135deg, 
    rgba(244, 67, 54, 0.15), 
    rgba(211, 47, 47, 0.1));
  border-color: rgba(244, 67, 54, 0.4);
}

.player-detail-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.player-detail-header i.fas.fa-user {
  color: #888;
  font-size: 0.9rem;
}

.player-detail-header i.fas.fa-star {
  color: var(--primary-gold, #D4AF37);
  font-size: 0.8rem;
  margin-left: auto;
}

.player-name {
  font-weight: 600;
  color: #e0e0e0;
  font-size: 1rem;
}

.result-badge {
  padding: 0.2rem 0.5rem;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: auto;
}

.result-badge.winner {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.4);
}

.result-badge.loser {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.4);
}

.player-detail-info {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  font-size: 0.85rem;
}

.player-race {
  color: #b0b0b0;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.player-mmr {
  color: #4caf50;
  font-weight: 600;
  background: rgba(76, 175, 80, 0.1);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.player-mmr:not(.positive) {
  color: #f44336;
  background: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.2);
}

.player-resources {
  color: #ff9800;
  background: rgba(255, 152, 0, 0.1);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(255, 152, 0, 0.2);
}

/* Match Information Grid */
.match-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.match-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.info-label {
  color: #a0a0a0;
  font-size: 0.9rem;
  font-weight: 500;
}

.info-value {
  color: #e0e0e0;
  font-weight: 600;
  font-size: 0.9rem;
}

.info-value.positive {
  color: #4caf50;
}

.info-value.negative {
  color: #f44336;
}

/* No Data State */
.no-detailed-data {
  text-align: center;
  padding: 2rem;
  color: #888;
}

.no-detailed-data i {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-detailed-data p {
  margin: 0.5rem 0;
}

.match-summary {
  font-size: 0.9rem;
  color: #666;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .match-details-content {
    padding: 1rem;
  }
  
  .ffa-players-list {
    grid-template-columns: 1fr;
  }
  
  .match-info-grid {
    grid-template-columns: 1fr;
  }
  
  .player-detail-info {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .player-detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .result-badge {
    margin-left: 0;
    align-self: flex-end;
  }
}

/* Scrollbar Styling */
.match-details-content::-webkit-scrollbar {
  width: 6px;
}

.match-details-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.match-details-content::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.3);
  border-radius: 3px;
}

.match-details-content::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.5);
}
