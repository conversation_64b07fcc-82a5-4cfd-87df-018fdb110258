const fs = require('fs').promises;
const path = require('path');
const chokidar = require('chokidar');
const { EventEmitter } = require('events');

class ReplayMonitor extends EventEmitter {
  constructor(gameDetector) {
    super();
    this.gameDetector = gameDetector;
    this.watchers = [];
    this.isMonitoring = false;
    this.processedReplays = new Set();
    
    // Define replay file locations for each game
    this.replayPaths = {
      warcraft2: [
        // War2 BNE replay locations
        path.join(require('os').homedir(), 'Documents', 'Warcraft II BNE', 'Replays'),
        path.join(require('os').homedir(), 'Documents', 'Warcraft II BNE', 'Save'),
        path.join(require('os').homedir(), 'Documents', 'Warcraft II', 'Replays'),
        'C:\\Program Files (x86)\\Warcraft II BNE\\Replays',
        'C:\\Program Files\\Warcraft II BNE\\Replays',
        // GOG locations
        path.join(require('os').homedir(), 'Documents', 'GOG Galaxy', 'Games', 'Warcraft II', 'Replays'),
      ],
      warcraft3: [
        // WC3 Standard replay locations
        path.join(require('os').homedir(), 'Documents', 'Warcraft III', 'Replays'),
        path.join(require('os').homedir(), 'Documents', 'Warcraft III Reforged', 'Replays'),
        'C:\\Program Files (x86)\\Warcraft III\\Replays',
        'C:\\Program Files\\Warcraft III\\Replays',
        // Battle.net locations
        path.join(require('os').homedir(), 'Documents', 'Battle.net', 'Warcraft III', 'Replays'),
        // Custom game locations  
        path.join(require('os').homedir(), 'Documents', 'W3Champions', 'Replays'),
        // Common custom locations
        path.join(require('os').homedir(), 'Documents', 'WC3', 'Replays'),
      ]
    };
    
    // File extensions for replay files
    this.replayExtensions = {
      warcraft2: ['.rep', '.w2r', '.save'],
      warcraft3: ['.w3g', '.w3r', '.w3v', '.w3x']
    };
  }
  
  async startMonitoring() {
    if (this.isMonitoring) return;
    
    console.log('🎬 Starting replay file monitoring...');
    this.isMonitoring = true;
    
    // Monitor replay directories for each game type
    for (const [gameType, paths] of Object.entries(this.replayPaths)) {
      for (const replayPath of paths) {
        try {
          const pathExists = await fs.access(replayPath).then(() => true).catch(() => false);
          if (!pathExists) continue;
          
          const watcher = chokidar.watch(replayPath, {
            ignored: /node_modules/,
            persistent: true,
            depth: 2,
            awaitWriteFinish: {
              stabilityThreshold: 3000, // Wait 3 seconds for replay file to finish writing
              pollInterval: 100
            },
            ignorePermissionErrors: true
          });
          
          watcher.on('add', (filePath) => this.handleNewReplayFile(filePath, gameType));
          watcher.on('change', (filePath) => this.handleReplayFileChange(filePath, gameType));
          
          this.watchers.push(watcher);
          console.log(`🎬 Monitoring ${gameType} replays in: ${replayPath}`);
          
        } catch (error) {
          console.error(`Failed to monitor ${gameType} replays in ${replayPath}:`, error);
        }
      }
    }
  }
  
  async handleNewReplayFile(filePath, gameType) {
    const fileName = path.basename(filePath);
    const ext = path.extname(filePath).toLowerCase();
    
    // Check if it's a relevant replay file
    if (this.isRelevantReplayFile(ext, gameType)) {
      console.log(`🎬 New ${gameType} replay detected: ${fileName}`);
      
      // Avoid processing the same replay multiple times
      if (this.processedReplays.has(filePath)) {
        console.log(`⏭️ Replay already processed: ${fileName}`);
        return;
      }
      
      await this.processReplayFile(filePath, gameType);
      this.processedReplays.add(filePath);
    }
  }
  
  async handleReplayFileChange(filePath, gameType) {
    // Usually replays don't change after creation, but handle it just in case
    if (!this.processedReplays.has(filePath)) {
      await this.handleNewReplayFile(filePath, gameType);
    }
  }
  
  isRelevantReplayFile(extension, gameType) {
    const validExtensions = this.replayExtensions[gameType] || [];
    return validExtensions.includes(extension);
  }
  
  async processReplayFile(filePath, gameType) {
    try {
      console.log(`🔍 Processing ${gameType} replay: ${path.basename(filePath)}`);
      
      const stats = await fs.stat(filePath);
      const fileSize = stats.size;
      
      // Skip very small files (likely corrupt or incomplete)
      if (fileSize < 100) {
        console.log(`⚠️ Replay file too small, skipping: ${fileSize} bytes`);
        return;
      }
      
      // Read the replay file
      const buffer = await fs.readFile(filePath);
      
      // Parse replay based on game type
      let replayData = null;
      if (gameType === 'warcraft3') {
        replayData = await this.parseWC3Replay(buffer, filePath);
      } else if (gameType === 'warcraft2') {
        replayData = await this.parseWC2Replay(buffer, filePath);
      }
      
      if (replayData) {
        console.log(`🎯 Match result extracted from replay: ${replayData.result || 'unknown'}`);
        
        // Emit replay result event
        this.emit('replayResult', {
          gameType,
          filePath,
          fileName: path.basename(filePath),
          timestamp: new Date(),
          fileSize,
          ...replayData
        });
      }
      
    } catch (error) {
      console.error(`Error processing replay file ${filePath}:`, error);
    }
  }
  
  async parseWC3Replay(buffer, filePath) {
    try {
      // WC3 replay files have a specific binary format
      // This is a simplified parser - for full parsing, you'd need a complete WC3 replay parser
      
      const replayData = {
        source: 'replay_file',
        confidence: 95,
        players: [],
        matchData: {}
      };
      
      // Read header information
      const headerSize = buffer.readUInt32LE(0);
      const compressedSize = buffer.readUInt32LE(4);
      const version = buffer.readUInt32LE(8);
      
      if (headerSize < 16 || version < 1) {
        console.log('⚠️ Invalid WC3 replay header');
        return null;
      }
      
      // Extract basic replay information from filename and file stats
      const fileName = path.basename(filePath, '.w3g');
      const stats = await fs.stat(filePath);
      
      replayData.matchData = {
        fileName,
        fileSize: buffer.length,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        version
      };
      
      // Try to extract player information from filename patterns
      // Many replays follow naming conventions like "PlayerName_vs_PlayerName_MapName"
      const playerMatches = fileName.match(/(\w+)_vs_(\w+)/i);
      if (playerMatches) {
        replayData.players = [
          { name: playerMatches[1], team: 1 },
          { name: playerMatches[2], team: 2 }
        ];
        replayData.matchData.matchType = '1v1';
      }
      
      // Extract map name from filename if possible
      const mapMatch = fileName.match(/_([^_]+)\.w3g$/i);
      if (mapMatch) {
        replayData.matchData.mapName = mapMatch[1];
      }
      
      // For now, we can't determine the winner without full replay parsing
      // This would require implementing the full WC3 replay format specification
      replayData.result = 'unknown';
      replayData.confidence = 70; // Lower confidence since we can't determine winner
      
      return replayData;
      
    } catch (error) {
      console.error('Error parsing WC3 replay:', error);
      return null;
    }
  }
  
  async parseWC2Replay(buffer, filePath) {
    try {
      // WC2 replay files (.rep) have a different format
      const replayData = {
        source: 'replay_file',
        confidence: 95,
        players: [],
        matchData: {}
      };
      
      // WC2 replay format is simpler but still binary
      const fileName = path.basename(filePath);
      const stats = await fs.stat(filePath);
      
      replayData.matchData = {
        fileName,
        fileSize: buffer.length,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      };
      
      // Try to extract information from filename
      const nameWithoutExt = path.basename(filePath, path.extname(filePath));
      
      // Look for common naming patterns
      const playerMatches = nameWithoutExt.match(/(\w+)_vs_(\w+)/i);
      if (playerMatches) {
        replayData.players = [
          { name: playerMatches[1], team: 1 },
          { name: playerMatches[2], team: 2 }
        ];
        replayData.matchData.matchType = '1v1';
      }
      
      // Extract map name if present in filename
      const mapMatch = nameWithoutExt.match(/_([^_]+)$/i);
      if (mapMatch && !playerMatches) {
        replayData.matchData.mapName = mapMatch[1];
      }
      
      // For WC2, we'd need to implement the full replay parser to get winner
      replayData.result = 'unknown';
      replayData.confidence = 70;
      
      return replayData;
      
    } catch (error) {
      console.error('Error parsing WC2 replay:', error);
      return null;
    }
  }
  
  stopMonitoring() {
    if (!this.isMonitoring) return;
    
    console.log('🛑 Stopping replay file monitoring...');
    
    for (const watcher of this.watchers) {
      watcher.close();
    }
    
    this.watchers = [];
    this.isMonitoring = false;
    this.processedReplays.clear();
  }
  
  getMonitoringStats() {
    return {
      isMonitoring: this.isMonitoring,
      watchedPaths: this.watchers.length,
      processedReplays: this.processedReplays.size,
      supportedGames: Object.keys(this.replayPaths),
      supportedExtensions: this.replayExtensions
    };
  }
  
  // Method to manually process a replay file
  async processManualReplay(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    
    // Determine game type by extension
    let gameType = null;
    for (const [game, extensions] of Object.entries(this.replayExtensions)) {
      if (extensions.includes(ext)) {
        gameType = game;
        break;
      }
    }
    
    if (!gameType) {
      throw new Error(`Unsupported replay file type: ${ext}`);
    }
    
    await this.processReplayFile(filePath, gameType);
  }
}

module.exports = ReplayMonitor; 