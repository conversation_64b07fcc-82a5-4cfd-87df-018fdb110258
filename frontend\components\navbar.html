<!-- Modern Gaming App Navbar -->
<nav class="navbar-modern" id="navbar-modern" role="navigation" aria-label="Main navigation">
  <div class="navbar-container">
    <!-- Left Section - Logo -->
    <div class="navbar-brand">
      <a href="/" class="brand-link" aria-label="Home" data-nav-link="true">
        <i class="fas fa-hammer brand-icon"></i>
        <div class="brand-content">
          <span class="brand-text">BLACKSMITH</span>
          <span class="brand-middle">WC Arena</span>
          <span class="brand-subtitle">Supported By Heroes</span>
        </div>
      </a>
    </div>

    <!-- Center Section - Main Navigation (Desktop) -->
    <div class="navbar-nav-desktop" role="menubar">
      <div class="nav-primary">
        <a href="/views/ladder.html" class="nav-item primary" role="menuitem" data-nav-link="true">
          <i class="fas fa-trophy"></i>
          <span>Arena</span>
        </a>
        <a href="/views/tournaments.html" class="nav-item primary" role="menuitem" data-nav-link="true">
          <i class="fas fa-crown"></i>
          <span>Tournaments</span>
        </a>
        <a href="/views/content.html" class="nav-item primary" role="menuitem" data-nav-link="true">
          <i class="fas fa-broadcast-tower"></i>
          <span>Watch Tower</span>
        </a>
        <a href="/views/maps.html" class="nav-item primary" role="menuitem" data-nav-link="true">
          <i class="fas fa-map"></i>
          <span>ATLAS</span>
        </a>
        <a href="/views/arena-core.html" class="nav-item primary" role="menuitem" data-nav-link="true">
          <i class="fas fa-cogs"></i>
          <span>ARENA CORE</span>
        </a>
        <a href="/views/hero.html" class="nav-item primary" role="menuitem" data-nav-link="true">
          <i class="fas fa-shield-alt"></i>
          <span>BE A HERO</span>
        </a>
        
        <!-- Music Control - Artistic Redesign -->
        <div class="music-control-artistic nav-music-control">
          <div class="music-orb-container">
            <div class="music-orb">
              <div class="orb-core">
                <i class="fas fa-music orb-icon"></i>
              </div>
              <div class="orb-rings">
                <div class="ring ring-1"></div>
                <div class="ring ring-2"></div>
                <div class="ring ring-3"></div>
              </div>
            </div>

            <div class="music-options">
              <fieldset class="music-toggle-enhanced" aria-label="Background music controls">
                <legend class="sr-only">Background Music Options</legend>
                <input type="radio" id="music-mute" name="music" checked>
                <input type="radio" id="music-orc" name="music">
                <input type="radio" id="music-human" name="music">

                <label for="music-mute" class="music-btn mute-btn" title="Silence" aria-label="Mute music">
                  <div class="btn-glow"></div>
                  <i class="fas fa-volume-mute" aria-hidden="true"></i>
                  <span class="btn-text">Silence</span>
                </label>
                <label for="music-orc" class="music-btn orc-btn" title="Orc Drums" aria-label="Play Orc music">
                  <div class="btn-glow"></div>
                  <i class="fas fa-drum" aria-hidden="true"></i>
                  <span class="btn-text">Horde</span>
                </label>
                <label for="music-human" class="music-btn human-btn" title="Alliance Hymns" aria-label="Play Human music">
                  <div class="btn-glow"></div>
                  <i class="fas fa-music" aria-hidden="true"></i>
                  <span class="btn-text">Alliance</span>
                </label>
              </fieldset>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Section - User & Controls -->
    <div class="navbar-controls">
      <!-- Town Hall Dropdown -->
      <div class="user-profile-modern">
        <div class="nav-dropdown profile-dropdown">
          <button class="profile-dropdown-toggle" data-dropdown-toggle="true" aria-expanded="false" aria-haspopup="true" aria-label="Town Hall menu">
            <div class="profile-avatar">
              <img id="profile-image" src="/assets/img/ranks/emblem.png" alt="Profile">
              <div class="profile-status"></div>
            </div>
            <div class="profile-info" id="profile-info-desktop">
              <span class="profile-title">TOWN HALL</span>
              <span id="navbar-username" class="profile-username">Loading...</span>
            </div>
            <i class="fas fa-chevron-down profile-chevron" aria-hidden="true"></i>
          </button>
          
          <div class="nav-dropdown-menu profile-menu" role="menu" aria-label="Town Hall options">
            <a href="/views/myprofile.html" class="nav-dropdown-item" role="menuitem" data-nav-link="true">
              <i class="fas fa-university"></i>
              Home
            </a>
            <div class="dropdown-divider" role="separator"></div>
            <a href="/views/campaigns.html" class="nav-dropdown-item" role="menuitem" data-nav-link="true">
              <i class="fas fa-flag"></i>
              War Table
            </a>
            <a href="/views/forum.html" class="nav-dropdown-item" role="menuitem" data-nav-link="true">
              <i class="fas fa-scroll"></i>
              Stone Tablet
            </a>
            <a href="/views/dark-portal.html" class="nav-dropdown-item" role="menuitem" data-nav-link="true">
              <i class="fas fa-dungeon"></i>
              Dark Portal
            </a>
            <a href="/views/wizards-tower.html" class="nav-dropdown-item" role="menuitem" data-nav-link="true">
              <i class="fas fa-tower-observation"></i>
              Wizard's Tower
            </a>
            <div class="dropdown-divider" role="separator"></div>
            <a href="#" class="nav-dropdown-item logout-item" role="menuitem" onclick="window.handleGlobalLogout(event);">
              <i class="fas fa-sign-out-alt"></i>
              Logout
            </a>
          </div>
        </div>
      </div>



      <!-- Mobile Menu Toggle -->
      <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-expanded="false" aria-label="Toggle mobile menu">
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
      </button>
    </div>
  </div>

  <!-- Mobile Navigation Menu -->
  <div class="mobile-nav" id="mobile-nav" role="menu" aria-label="Mobile navigation menu">
    <div class="mobile-nav-header">
      <div class="mobile-profile">
        <img id="profile-image-mobile" src="/assets/img/ranks/emblem.png" alt="Profile">
        <div class="mobile-profile-info">
          <span class="mobile-profile-title">TOWN HALL</span>
          <span id="navbar-username-mobile" class="mobile-profile-username">Loading...</span>
        </div>
      </div>
    </div>

    <div class="mobile-nav-content">
      <div class="mobile-nav-section">
        <h3 class="mobile-nav-title">Main</h3>
        <a href="/views/ladder.html" class="mobile-nav-item" role="menuitem">
          <i class="fas fa-trophy"></i>
          <span>Arena</span>
        </a>
        <a href="/views/tournaments.html" class="mobile-nav-item" role="menuitem">
          <i class="fas fa-crown"></i>
          <span>Tournaments</span>
        </a>
        <a href="/views/content.html" class="mobile-nav-item" role="menuitem">
          <i class="fas fa-broadcast-tower"></i>
          <span>Watch Tower</span>
        </a>
        <a href="/views/maps.html" class="mobile-nav-item" role="menuitem">
          <i class="fas fa-map"></i>
          <span>ATLAS</span>
        </a>
        <a href="/views/arena-core.html" class="mobile-nav-item" role="menuitem">
          <i class="fas fa-cogs"></i>
          <span>ARENA CORE</span>
        </a>
      </div>

      <div class="mobile-nav-section">
        <h3 class="mobile-nav-title">Town Hall</h3>
        <a href="/views/myprofile.html" class="mobile-nav-item" role="menuitem">
          <i class="fas fa-university"></i>
          <span>Home</span>
        </a>
        <a href="/views/campaigns.html" class="mobile-nav-item" role="menuitem">
          <i class="fas fa-flag"></i>
          <span>War Table</span>
        </a>
        <a href="/views/forum.html" class="mobile-nav-item" role="menuitem">
          <i class="fas fa-scroll"></i>
          <span>Stone Tablet</span>
        </a>
        <a href="/views/wizards-tower.html" class="mobile-nav-item" role="menuitem">
          <i class="fas fa-tower-observation"></i>
          <span>Wizard's Tower</span>
        </a>
        <a href="/views/dark-portal.html" class="mobile-nav-item" role="menuitem">
          <i class="fas fa-dungeon"></i>
          <span>Dark Portal</span>
        </a>
        <div class="mobile-nav-section-divider"></div>
        <a href="#" class="mobile-nav-item logout-item" role="menuitem" onclick="window.handleGlobalLogout(event);">
          <i class="fas fa-sign-out-alt"></i>
          <span>Logout</span>
        </a>
      </div>
    </div>
  </div>
</nav>

<!-- Screen Reader Only Content -->
<style>
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>

<!-- Background Music (managed by music.js) -->
<audio id="orc-music" preload="none" loop>
  <source src="/assets/AUDIO/ORC/orcmusic.mp3" type="audio/mpeg">
</audio>
<audio id="human-music" preload="none" loop>
  <source src="/assets/AUDIO/HUMAN/humanmusic.mp3" type="audio/mpeg">
</audio>

<!-- Global logout handler fallback -->
<script>
// Global logout handler for when navbar isn't loaded yet
window.handleGlobalLogout = function(event) {
  event.preventDefault();
  console.log('🚪 Global logout handler called');
  
  // Check if we're in Electron iframe
  const isInElectronIframe = window !== window.top && 
                             (window.location.search.includes('electron=true') || 
                              window.location.search.includes('electronApp='));
  
  if (isInElectronIframe) {
    console.log('🛡️ Electron iframe logout via global handler');
    // Send logout message to parent
    window.parent.postMessage({
      type: 'ELECTRON_LOGOUT',
      timestamp: Date.now()
    }, '*');
    return;
  }
  
  // Fallback for web browser
  console.log('🌐 Web browser logout via global handler');
  window.location.href = '/auth/logout';
};

// Update logout links to use global handler
document.addEventListener('DOMContentLoaded', function() {
  const logoutLinks = document.querySelectorAll('.logout-item');
  logoutLinks.forEach(link => {
    if (!link.onclick) {
      link.onclick = window.handleGlobalLogout;
    }
  });
});
</script>
