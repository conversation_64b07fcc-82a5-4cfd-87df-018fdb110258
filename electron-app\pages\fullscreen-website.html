<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WC Arena</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a2e;
            height: 100vh;
            overflow: hidden;
        }

        .website-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: white;
        }

        .top-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 999999; /* Much higher z-index to stay above website content */
            display: flex;
            gap: 8px;
            transition: all 0.3s ease;
            opacity: 0.7; /* Semi-transparent by default to be less intrusive */
        }

        .top-controls:hover {
            opacity: 1; /* Full opacity on hover */
        }

        .control-btn {
            background: rgba(20, 20, 30, 0.9);
            border: 1px solid rgba(100, 100, 150, 0.4);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .control-btn:hover {
            background: rgba(40, 40, 60, 0.95);
            border-color: rgba(150, 150, 200, 0.6);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        }

        /* Toggle button for minimizing controls */
        .controls-toggle {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 999999;
            background: rgba(20, 20, 30, 0.9);
            border: 1px solid rgba(100, 100, 150, 0.4);
            color: white;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            display: none; /* Initially hidden */
        }

        .controls-toggle:hover {
            background: rgba(40, 40, 60, 0.95);
            transform: scale(1.1);
        }

        .top-controls.minimized {
            transform: translateX(100px);
            opacity: 0;
            pointer-events: none;
        }

        .website-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            text-align: center;
            z-index: 999;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="website-container">
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Loading website...</p>
        </div>
        
        <iframe id="websiteIframe" class="website-iframe" src="" style="display: none;"></iframe>
    </div>

    <script>
        let currentUser = null;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', async () => {
            if (window.electronAPI) {
                // Check authentication
                const authResult = await checkAuthentication();
                if (!authResult) {
                    await window.electronAPI.navigation.loadPage('login');
                    return;
                }
                
                // Load website
                await loadWebsite();
            }
        });

        async function checkAuthentication() {
            try {
                const result = await window.electronAPI.auth.getCurrentUser();
                if (result.success && result.user) {
                    currentUser = result.user;
                    return true;
                } else {
                    console.log('Authentication check failed:', result.error);
                    return false;
                }
            } catch (error) {
                console.error('Authentication check error:', error);
                return false;
            }
        }

        async function loadWebsite() {
            try {
                // Get server URL from settings
                const configResult = await window.electronAPI.config.get();
                const serverUrl = configResult.success ? configResult.config.serverUrl : 'http://127.0.0.1:3000';
                
                const loading = document.getElementById('loading');
                loading.innerHTML = '<div class="spinner"></div><p>Authenticating with server...</p>';
                
                // Get the JWT token from Electron storage
                const tokenResult = await window.electronAPI.auth.getToken();
                if (!tokenResult.success || !tokenResult.token) {
                    loading.innerHTML = '<div style="color: #ff6b6b;"><h3>Authentication Error</h3><p>No authentication token found. Please login again.</p><button onclick="window.electronAPI.navigation.loadPage(\'login\')" style="margin-top: 10px; padding: 8px 16px; background: #4a90e2; color: white; border: none; border-radius: 4px; cursor: pointer;">Login Again</button></div>';
                    return;
                }
                
                console.log('🎫 Found JWT token, setting up authenticated session...');
                
                // Create URL with JWT token for automatic authentication
                const websiteUrl = `${serverUrl}?electron=true&electronApp=WC_Arena_Companion&user=${encodeURIComponent(currentUser.username)}&authToken=${encodeURIComponent(tokenResult.token)}`;
                
                const iframe = document.getElementById('websiteIframe');
                
                // Set up iframe event handlers
                iframe.onload = () => {
                    console.log('✅ Website iframe loaded successfully');
                    loading.style.display = 'none';
                    iframe.style.display = 'block';
                    
                    // Set up message passing for authentication
                    setupIframeAuthentication(iframe, tokenResult.token);
                };
                
                iframe.onerror = (error) => {
                    console.error('❌ Website iframe failed to load:', error);
                    loading.innerHTML = '<div style="color: #ff6b6b;"><h3>Profile System Error</h3><p>Failed to load the website. Please check your connection and try again.</p><button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #4a90e2; color: white; border: none; border-radius: 4px; cursor: pointer;">Refresh Page</button></div>';
                };
                
                console.log('🌐 Loading authenticated website URL:', websiteUrl);
                iframe.src = websiteUrl;
                
            } catch (error) {
                console.error('❌ Failed to load website configuration:', error);
                const loading = document.getElementById('loading');
                loading.innerHTML = '<div style="color: #ff6b6b;"><h3>Configuration Error</h3><p>Failed to load website configuration. Please try again.</p><button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #4a90e2; color: white; border: none; border-radius: 4px; cursor: pointer;">Refresh Page</button></div>';
            }
        }

        function setupIframeAuthentication(iframe, jwtToken) {
            // Listen for authentication requests from the iframe
            window.addEventListener('message', async (event) => {
                console.log('📨 Parent window received message:', event.data);
                console.log('🔍 Message source check:', {
                    eventSource: event.source,
                    iframeContentWindow: iframe.contentWindow,
                    isFromOurIframe: event.source === iframe.contentWindow
                });
                
                // Verify the message is from our iframe
                if (event.source !== iframe.contentWindow) {
                    console.log('❌ Message not from our iframe, ignoring');
                    return; // Only accept messages from our iframe
                }
                
                if (event.data.type === 'REQUEST_AUTH_TOKEN') {
                    console.log('🔐 Iframe requesting authentication token');
                    
                    // Send the JWT token to the iframe with the correct message type
                    iframe.contentWindow.postMessage({
                        type: 'ELECTRON_AUTH',
                        token: jwtToken,
                        user: currentUser,
                        isElectron: true
                    }, '*');
                } else if (event.data.type === 'ELECTRON_LOGOUT') {
                    console.log('🚪 Received logout request from iframe');
                    console.log('🚪 Logout message details:', event.data);
                    await handleElectronLogout();
                } else if (event.data.type === 'ELECTRON_API_CALL') {
                    // Handle API calls from iframe
                    console.log('🔌 Received API call from iframe:', event.data.api, event.data.method);
                    await handleElectronAPICall(event.data, iframe);
                } else if (event.data.type === 'ELECTRON_EVENT_LISTENER') {
                    // Handle event listener registration
                    console.log('🎧 Received event listener request:', event.data.action, event.data.channel);
                    await handleElectronEventListener(event.data, iframe);
                } else {
                    console.log('❓ Unknown message type:', event.data.type);
                }
            });
            
            // Send initial authentication after a short delay
            setTimeout(() => {
                console.log('🔐 Sending initial authentication to iframe');
                iframe.contentWindow.postMessage({
                    type: 'ELECTRON_AUTH',
                    token: jwtToken,
                    user: currentUser,
                    isElectron: true
                }, '*');
            }, 1000);
        }

        async function handleElectronAPICall(data, iframe) {
            try {
                const { requestId, api, method, args = [] } = data;
                let result;

                // Route API calls to the appropriate electronAPI methods
                if (api === 'games') {
                    if (method === 'findAll') {
                        result = await window.electronAPI.games.findAll();
                    } else if (method === 'getDetected') {
                        result = await window.electronAPI.games.getDetected();
                    } else if (method === 'launch') {
                        result = await window.electronAPI.games.launch(args[0]);
                    } else if (method === 'addManual') {
                        result = await window.electronAPI.games.addManual(args[0]);
                    } else if (method === 'removeManual') {
                        result = await window.electronAPI.games.removeManual(args[0]);
                    } else if (method === 'getByType') {
                        result = await window.electronAPI.games.getByType(args[0]);
                    } else if (method === 'getManual') {
                        result = await window.electronAPI.games.getManual();
                    } else if (method === 'updateManual') {
                        result = await window.electronAPI.games.updateManual(args[0], args[1]);
                    } else if (method === 'openMapsFolder') {
                        result = await window.electronAPI.games.openMapsFolder(args[0]);
                    }
                } else if (api === 'screenshots') {
                    if (method === 'openFolder') {
                        result = await window.electronAPI.screenshots.openFolder();
                    } else if (method === 'take') {
                        result = await window.electronAPI.screenshots.take();
                    } else if (method === 'getRecent') {
                        result = await window.electronAPI.screenshots.getRecent(args[0]);
                    } else if (method === 'getStats') {
                        result = await window.electronAPI.screenshots.getStats();
                    } else if (method === 'getAnalysisTools') {
                        result = await window.electronAPI.screenshots.getAnalysisTools();
                    } else if (method === 'analyzeImage') {
                        result = await window.electronAPI.screenshots.analyzeImage(args[0]);
                    }
                } else if (api === 'matches') {
                    if (method === 'getRecent') {
                        result = await window.electronAPI.matches.getRecent(args[0]);
                    } else if (method === 'getStats') {
                        result = await window.electronAPI.matches.getStats();
                    } else if (method === 'processManual') {
                        result = await window.electronAPI.matches.processManual(args[0]);
                    }
                } else if (api === 'dialog') {
                    if (method === 'openFile') {
                        result = await window.electronAPI.dialog.openFile(args[0]);
                    }
                } else if (api === 'settings') {
                    if (method === 'get') {
                        result = await window.electronAPI.settings.get();
                    } else if (method === 'set') {
                        result = await window.electronAPI.settings.set(args[0]);
                    }
                } else if (api === 'core') {
                    // Handle core Electron API methods
                    if (method === 'invoke') {
                        const [channel, ...invokeArgs] = args;
                        result = await window.electronAPI.invoke(channel, ...invokeArgs);
                    } else if (method === 'on') {
                        // Event listeners are handled differently - just acknowledge
                        result = { success: true, message: 'Event listener registered' };
                    } else if (method === 'once') {
                        result = { success: true, message: 'Once event listener registered' };
                    } else if (method === 'removeListener') {
                        result = { success: true, message: 'Event listener removed' };
                    } else if (method === 'removeAllListeners') {
                        result = { success: true, message: 'All event listeners removed' };
                    } else {
                        throw new Error(`Unknown core method: ${method}`);
                    }
                } else if (api === 'arenaCore') {
                    if (method === 'requestData') {
                        // Return arena core specific data in the expected format
                        const gamesResponse = await window.electronAPI.games.findAll();
                        const screenshotsResponse = await window.electronAPI.screenshots.getStats();
                        const matchesResponse = await window.electronAPI.matches.getStats();
                        
                        // Extract games array from response
                        console.log('🎮 Games response:', gamesResponse);
                        console.log('📸 Screenshots response:', screenshotsResponse);
                        console.log('🎯 Matches response:', matchesResponse);
                        
                        const games = gamesResponse && gamesResponse.success ? gamesResponse.games : [];
                        const screenshots = screenshotsResponse && screenshotsResponse.success ? screenshotsResponse.stats : null;
                        const matches = matchesResponse && matchesResponse.success ? matchesResponse.stats : null;
                        
                        console.log('🎮 Extracted games:', games);
                        console.log('🎮 Games type:', typeof games);
                        console.log('🎮 Games is array:', Array.isArray(games));
                        
                        result = {
                            success: true,
                            data: {
                                games: games,
                                stats: {
                                    screenshotsTaken: screenshots ? screenshots.detected || 0 : 0,
                                    matchesReported: matches ? matches.total || 0 : 0,
                                    uptimeHours: Math.floor((Date.now() - (screenshots ? screenshots.sessionStart || Date.now() : Date.now())) / (1000 * 60 * 60)),
                                    gamesDetected: games ? games.length || 0 : 0
                                },
                                systemInfo: {
                                    platform: 'win32',
                                    version: '1.0.0',
                                    serverStatus: 'Connected'
                                }
                            }
                        };
                    } else {
                        throw new Error(`Unknown arenaCore method: ${method}`);
                    }
                } else {
                    throw new Error(`Unknown API: ${api}`);
                }

                // Send success response back to iframe
                iframe.contentWindow.postMessage({
                    type: 'ELECTRON_API_RESPONSE',
                    requestId: requestId,
                    success: true,
                    result: result
                }, '*');

            } catch (error) {
                console.error('❌ API call failed:', error);
                
                // Send error response back to iframe
                iframe.contentWindow.postMessage({
                    type: 'ELECTRON_API_RESPONSE',
                    requestId: data.requestId,
                    success: false,
                    error: error.message
                }, '*');
            }
        }

        async function handleElectronEventListener(data, iframe) {
            try {
                const { action, channel } = data;
                
                // For now, just acknowledge the event listener registration
                // In a full implementation, you would set up actual event listeners
                // and forward events to the iframe when they occur
                
                console.log('🎧 Event listener action:', action, 'for channel:', channel);
                
                // Send acknowledgment back to iframe
                iframe.contentWindow.postMessage({
                    type: 'ELECTRON_EVENT',
                    channel: channel,
                    data: { success: true, message: `Event listener ${action} for ${channel}` }
                }, '*');
                
            } catch (error) {
                console.error('❌ Event listener handling failed:', error);
                
                // Send error response back to iframe
                iframe.contentWindow.postMessage({
                    type: 'ELECTRON_EVENT',
                    channel: data.channel,
                    data: { success: false, error: error.message }
                }, '*');
            }
        }

        async function handleElectronLogout() {
            console.log('🧹 Processing Electron logout request...');
            console.log('🔍 electronAPI available:', !!window.electronAPI);
            console.log('🔍 electronAPI.auth available:', !!(window.electronAPI && window.electronAPI.auth));
            console.log('🔍 electronAPI.navigation available:', !!(window.electronAPI && window.electronAPI.navigation));
            
            try {
                // First, call the main process to clear authentication data
                console.log('🔐 Calling electronAPI.auth.logout()...');
                const logoutResult = await window.electronAPI.auth.logout();
                console.log('✅ Electron authentication cleared:', logoutResult);
                
                // Then, notify the server to clear sessions (optional but good practice)
                try {
                    console.log('⚙️ Getting server URL from config...');
                    const configResult = await window.electronAPI.config.get();
                    const serverUrl = configResult.success ? configResult.config.serverUrl : 'http://127.0.0.1:3000';
                    console.log('🌐 Server URL:', serverUrl);
                    
                    console.log('🌐 Notifying server of logout...');
                    const serverResponse = await fetch(`${serverUrl}/auth/logout`, {
                        method: 'GET',
                        headers: { 'X-Electron-App': 'true' },
                        credentials: 'include'
                    });
                    console.log('✅ Server logout notification sent, status:', serverResponse.status);
                } catch (serverError) {
                    console.warn('⚠️ Server logout notification failed (continuing anyway):', serverError);
                }
                
                // Finally, navigate to login page
                console.log('🔄 Navigating to login page...');
                const navResult = await window.electronAPI.navigation.loadPage('login');
                console.log('✅ Navigation result:', navResult);
                
            } catch (error) {
                console.error('❌ Electron logout failed:', error);
                console.error('❌ Error stack:', error.stack);
                
                // Fallback: navigate to login page anyway
                try {
                    console.log('🔄 Fallback: navigating to login page...');
                    const fallbackResult = await window.electronAPI.navigation.loadPage('login');
                    console.log('✅ Fallback navigation result:', fallbackResult);
                } catch (navError) {
                    console.error('❌ Navigation to login failed:', navError);
                    console.error('❌ Navigation error stack:', navError.stack);
                    // Force reload as last resort
                    console.log('🔄 Last resort: reloading page...');
                    window.location.reload();
                }
            }
        }

        // Control functions moved to Game Manager page
    </script>
</body>
</html> 