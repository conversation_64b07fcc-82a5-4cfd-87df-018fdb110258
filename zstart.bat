@echo off
echo ========================================
echo WC Arena Core - Complete Startup
echo ========================================
echo.

echo [1/4] Killing existing processes on ports 3000 and 3001...
REM Kill any existing Node.js processes first
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im electron.exe >nul 2>&1

REM Wait a moment for processes to fully terminate
timeout /t 2 /nobreak >nul

REM Kill processes using specific ports (improved method)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000 ^| findstr LISTENING') do (
    echo Killing process %%a on port 3000
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3001 ^| findstr LISTENING') do (
    echo Killing process %%a on port 3001
    taskkill /f /pid %%a >nul 2>&1
)
echo ✅ Ports cleared

echo.
echo [2/4] Starting backend server on port 3000...
cd backend
start "Backend Server" cmd /k "set PORT=3000 && npm start"
cd ..

echo.
echo [3/4] Starting proxy server on port 3001...
cd electron-app
start "Electron Proxy Server" cmd /k "node electron-proxy.js"
cd ..

echo.
echo [4/4] Waiting 8 seconds for servers to start...
timeout /t 8 /nobreak >nul

echo.
echo [5/5] Starting Electron app...
cd electron-app
start "WC Arena Core" cmd /k "npm start"
cd ..

echo.
echo ✅ All services started!
echo 📡 Backend Server: http://127.0.0.1:3000
echo 🔄 Proxy Server: http://127.0.0.1:3001
echo 🖥️  Electron App: WC Arena Core
echo.
echo Press any key to stop all services...
pause >nul

echo.
echo 🛑 Stopping all services...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im electron.exe >nul 2>&1
echo ✅ All services stopped. 