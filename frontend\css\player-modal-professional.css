/* ==========================================================================
   PROFESSIONAL PLAYER MODAL STYLING
   Modern, optimized design with enhanced UX and Warcraft theme
   ========================================================================== */

/* ===== CSS VARIABLES ===== */
:root {
  --modal-bg-primary: linear-gradient(145deg, #1a1a2e, #16213e);
  --modal-bg-secondary: linear-gradient(135deg, #0f172a, #1e293b);
  --modal-border: rgba(212, 175, 55, 0.3);
  --modal-border-active: rgba(212, 175, 55, 0.6);
  --modal-text-primary: #ffffff;
  --modal-text-secondary: #cbd5e1;
  --modal-text-muted: #94a3b8;
  --modal-accent: #d4af37;
  --modal-accent-hover: #f4d03f;
  --modal-success: #10b981;
  --modal-danger: #ef4444;
  --modal-warning: #f59e0b;
  --modal-shadow: 0 25px 50px rgba(0, 0, 0, 0.6);
  --modal-shadow-lg: 0 35px 80px rgba(0, 0, 0, 0.8);
  --modal-radius: 16px;
  --modal-radius-sm: 8px;
  --modal-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== MODAL OVERLAY ===== */
.player-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(12px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: var(--modal-transition);
}

.player-details-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

/* ===== MODAL CONTAINER ===== */
.modal-container {
  position: relative;
  max-width: 95vw;
  max-height: 95vh;
  width: 1200px;
  height: 85vh;
  transform: scale(0.9);
  transition: var(--modal-transition);
}

.player-details-modal.show .modal-container {
  transform: scale(1);
}

/* ===== MODAL CONTENT ===== */
.player-modal-content {
  background: var(--modal-bg-primary);
  border-radius: var(--modal-radius);
  border: 2px solid var(--modal-border);
  box-shadow: var(--modal-shadow-lg);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.player-modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--modal-accent), transparent);
  opacity: 0.6;
}

/* ===== MODAL HEADER ===== */
.player-modal-header {
  position: relative;
  background: var(--modal-bg-secondary);
  border-bottom: 2px solid var(--modal-border);
  padding: 2rem;
  flex-shrink: 0;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.1), 
    rgba(212, 175, 55, 0.05),
    transparent);
  opacity: 0.8;
}

.header-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: 2rem;
}

/* ===== PLAYER AVATAR SECTION ===== */
.player-avatar-section {
  flex-shrink: 0;
}

.avatar-container {
  position: relative;
  width: 80px;
  height: 80px;
}

.player-rank-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid var(--modal-accent);
  object-fit: cover;
  transition: var(--modal-transition);
}

.avatar-glow {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--modal-accent), transparent, var(--modal-accent));
  opacity: 0.3;
  animation: avatarGlow 3s ease-in-out infinite;
}

@keyframes avatarGlow {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.05); }
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid var(--modal-bg-primary);
  transition: var(--modal-transition);
}

.status-indicator.online {
  background: var(--modal-success);
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
}

.status-indicator.offline {
  background: #6b7280;
}

/* ===== PLAYER INFO SECTION ===== */
.player-info-section {
  flex: 1;
  min-width: 0;
}

.player-title-area {
  margin-bottom: 1.5rem;
}

.player-name {
  font-family: 'Cinzel', serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--modal-text-primary);
  margin: 0 0 0.5rem 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  letter-spacing: 1px;
}

.player-subtitle {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.rank-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, var(--modal-accent), #b8860b);
  color: #1a1a1a;
  padding: 0.5rem 1rem;
  border-radius: var(--modal-radius-sm);
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 8px rgba(212, 175, 55, 0.3);
}

.mmr-display {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  color: var(--modal-text-primary);
  padding: 0.5rem 1rem;
  border-radius: var(--modal-radius-sm);
  border: 1px solid var(--modal-border);
  font-weight: 600;
  font-size: 0.9rem;
}

/* ===== STATS OVERVIEW ===== */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--modal-radius-sm);
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: var(--modal-transition);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--modal-accent);
  transform: scaleX(0);
  transition: var(--modal-transition);
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--modal-border-active);
  transform: translateY(-2px);
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.stat-card.wins .stat-icon {
  background: linear-gradient(135deg, var(--modal-success), #059669);
  color: white;
}

.stat-card.losses .stat-icon {
  background: linear-gradient(135deg, var(--modal-danger), #dc2626);
  color: white;
}

.stat-card.winrate .stat-icon {
  background: linear-gradient(135deg, var(--modal-warning), #d97706);
  color: white;
}

.stat-card.games .stat-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--modal-text-primary);
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-label {
  display: block;
  font-size: 0.8rem;
  color: var(--modal-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* ===== MODAL CLOSE BUTTON ===== */
.modal-close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid var(--modal-border);
  color: var(--modal-text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--modal-transition);
  z-index: 10;
}

.modal-close-btn:hover {
  background: var(--modal-danger);
  color: white;
  border-color: var(--modal-danger);
  transform: scale(1.1);
}

/* ===== MODAL BODY ===== */
.modal-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* ===== TABS NAVIGATION ===== */
.player-modal-tabs {
  background: var(--modal-bg-secondary);
  border-bottom: 1px solid var(--modal-border);
  padding: 0 1rem;
  flex-shrink: 0;
}

.tabs-container {
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--modal-border) transparent;
}

.tabs-container::-webkit-scrollbar {
  height: 4px;
}

.tabs-container::-webkit-scrollbar-thumb {
  background: var(--modal-border);
  border-radius: 4px;
}

.modal-tab {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: transparent;
  border: none;
  color: var(--modal-text-secondary);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--modal-transition);
  white-space: nowrap;
  border-bottom: 3px solid transparent;
  margin-bottom: -1px;
}

.modal-tab:hover {
  color: var(--modal-accent);
  background: rgba(255, 255, 255, 0.05);
}

.modal-tab.active {
  color: var(--modal-accent);
  border-bottom-color: var(--modal-accent);
  background: rgba(212, 175, 55, 0.05);
}

.tab-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 1rem;
}

.tab-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.tab-label {
  font-weight: 600;
  font-size: 0.9rem;
}

.tab-description {
  font-size: 0.7rem;
  color: var(--modal-text-muted);
  display: none;
}

.modal-tab:hover .tab-description {
  display: block;
}

.tab-indicator {
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--modal-accent);
  transform: scaleX(0);
  transition: var(--modal-transition);
}

.modal-tab.active .tab-indicator {
  transform: scaleX(1);
}

/* ===== TAB CONTENT CONTAINER ===== */
.tab-content-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.tab-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: var(--modal-transition);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--modal-border) transparent;
}

.tab-content.active {
  opacity: 1;
  visibility: visible;
  z-index: 1;
}

.tab-content::-webkit-scrollbar {
  width: 6px;
}

.tab-content::-webkit-scrollbar-track {
  background: transparent;
}

.tab-content::-webkit-scrollbar-thumb {
  background: var(--modal-border);
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb:hover {
  background: var(--modal-border-active);
}

/* ===== OVERVIEW TAB STYLING ===== */
.overview-content {
  padding: 2rem;
}

.overview-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 1.5rem;
  height: 100%;
}

/* ===== OVERVIEW CARDS ===== */
.profile-card,
.activity-card,
.chart-card,
.actions-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--modal-radius-sm);
  overflow: hidden;
  transition: var(--modal-transition);
  position: relative;
}

.profile-card:hover,
.activity-card:hover,
.chart-card:hover,
.actions-card:hover {
  border-color: var(--modal-border-active);
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.profile-header,
.activity-header,
.chart-header,
.actions-header {
  background: linear-gradient(135deg,
    rgba(212, 175, 55, 0.1),
    rgba(212, 175, 55, 0.05));
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-header h3,
.activity-header h3,
.chart-header h3,
.actions-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--modal-accent);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.profile-content,
.activity-content,
.chart-content,
.actions-content {
  padding: 1.5rem;
}

/* ===== PROFILE STATS ===== */
.profile-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.profile-stat {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--modal-radius-sm);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: var(--modal-transition);
}

.profile-stat:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--modal-border);
}

.profile-stat .stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--modal-accent), #b8860b);
  color: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.profile-stat .stat-info {
  flex: 1;
  min-width: 0;
}

.profile-stat .stat-label {
  display: block;
  font-size: 0.8rem;
  color: var(--modal-text-muted);
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.profile-stat .stat-value {
  display: block;
  font-size: 1rem;
  font-weight: 600;
  color: var(--modal-text-primary);
}

/* ===== ACTIVITY SECTION ===== */
.recent-matches {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.matches-streak {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.streak-label {
  font-size: 0.9rem;
  color: var(--modal-text-secondary);
  font-weight: 600;
}

.match-indicators {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.match-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 700;
  border: 2px solid transparent;
  transition: var(--modal-transition);
}

.match-indicator.win {
  background: var(--modal-success);
  color: white;
}

.match-indicator.loss {
  background: var(--modal-danger);
  color: white;
}

.match-indicator:hover {
  transform: scale(1.2);
  border-color: var(--modal-accent);
}

.activity-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.activity-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.activity-stat:last-child {
  border-bottom: none;
}

.activity-label {
  font-size: 0.9rem;
  color: var(--modal-text-secondary);
}

.activity-value {
  font-weight: 600;
  color: var(--modal-text-primary);
}

.rating-change.positive {
  color: var(--modal-success);
}

.rating-change.negative {
  color: var(--modal-danger);
}

/* ===== CHART SECTION ===== */
.mini-chart-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  position: relative;
}

.mini-chart-container canvas {
  max-width: 100%;
  height: auto;
}

/* ===== ACTIONS SECTION ===== */
.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--modal-radius-sm);
  color: var(--modal-text-secondary);
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--modal-transition);
  text-decoration: none;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--modal-border-active);
  color: var(--modal-text-primary);
  transform: translateY(-2px);
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, var(--modal-accent), #b8860b);
  color: #1a1a1a;
  border-color: var(--modal-accent);
}

.action-btn.secondary:hover {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-color: #3b82f6;
}

.action-btn.tertiary:hover {
  background: linear-gradient(135deg, var(--modal-success), #059669);
  color: white;
  border-color: var(--modal-success);
}

.action-btn.quaternary:hover {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border-color: #8b5cf6;
}

.action-btn i {
  font-size: 1.2rem;
}

.action-btn span {
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== MATCHES TAB STYLING ===== */
.matches-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.matches-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
}

.matches-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 2rem;
}

.header-title {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.header-title h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--modal-accent);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.matches-count {
  font-size: 0.9rem;
  color: var(--modal-text-muted);
}

.matches-controls {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-size: 0.8rem;
  color: var(--modal-text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-select.modern,
.filter-input.modern {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--modal-radius-sm);
  padding: 0.5rem 0.75rem;
  color: var(--modal-text-primary);
  font-size: 0.9rem;
  transition: var(--modal-transition);
  min-width: 120px;
}

.filter-select.modern:focus,
.filter-input.modern:focus {
  outline: none;
  border-color: var(--modal-accent);
  background: rgba(255, 255, 255, 0.08);
}

.filter-clear-btn {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--modal-radius-sm);
  padding: 0.5rem 1rem;
  color: #ef4444;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--modal-transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-clear-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
}

/* ===== MATCHES CONTAINER ===== */
.matches-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: var(--modal-radius-sm);
}

.match-history-list {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--modal-border) transparent;
  padding: 1rem;
}

.match-history-list::-webkit-scrollbar {
  width: 6px;
}

.match-history-list::-webkit-scrollbar-track {
  background: transparent;
}

.match-history-list::-webkit-scrollbar-thumb {
  background: var(--modal-border);
  border-radius: 3px;
}

.match-history-list::-webkit-scrollbar-thumb:hover {
  background: var(--modal-border-active);
}

/* ===== MATCH ITEM ===== */
.match-item {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: var(--modal-radius-sm);
  margin-bottom: 0.75rem;
  overflow: hidden;
  transition: var(--modal-transition);
  cursor: pointer;
}

.match-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--modal-border);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.match-item.win {
  border-left: 3px solid var(--modal-success);
}

.match-item.loss {
  border-left: 3px solid var(--modal-danger);
}

.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.05),
    rgba(255, 255, 255, 0.02));
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.match-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.match-type {
  font-size: 1rem;
  font-weight: 600;
  color: var(--modal-text-primary);
}

.match-date {
  font-size: 0.8rem;
  color: var(--modal-text-muted);
}

.match-result {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.match-result.win {
  background: rgba(16, 185, 129, 0.1);
  color: var(--modal-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.match-result.loss {
  background: rgba(239, 68, 68, 0.1);
  color: var(--modal-danger);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.match-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.match-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.match-map {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--modal-text-secondary);
}

.match-map i {
  color: var(--modal-accent);
}

.match-resources {
  font-size: 0.8rem;
  color: var(--modal-text-muted);
}

.match-players {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.player-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  color: var(--modal-text-secondary);
  transition: var(--modal-transition);
}

.player-tag:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--modal-border);
  color: var(--modal-text-primary);
}

.player-tag.current {
  background: rgba(212, 175, 55, 0.1);
  border-color: var(--modal-border);
  color: var(--modal-accent);
}

.player-tag.winner {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
  color: var(--modal-success);
}

.player-tag.loser {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: var(--modal-danger);
}

.vs-separator {
  font-size: 0.8rem;
  color: var(--modal-text-muted);
  margin: 0 0.25rem;
}

.match-mmr {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--modal-text-secondary);
}

.mmr-change {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.mmr-change.positive {
  background: rgba(16, 185, 129, 0.1);
  color: var(--modal-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.mmr-change.negative {
  background: rgba(239, 68, 68, 0.1);
  color: var(--modal-danger);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* ===== LOADING STATES ===== */
.loading-matches {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--modal-text-muted);
  gap: 1rem;
}

.loading-spinner {
  font-size: 2rem;
  color: var(--modal-accent);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== PERFORMANCE TAB STYLING ===== */
.performance-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.performance-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
}

.performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.performance-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--modal-accent);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.time-range-selector {
  display: flex;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--modal-radius-sm);
  overflow: hidden;
}

.time-btn {
  background: transparent;
  border: none;
  padding: 0.5rem 1rem;
  color: var(--modal-text-secondary);
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--modal-transition);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.time-btn:last-child {
  border-right: none;
}

.time-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--modal-text-primary);
}

.time-btn.active {
  background: var(--modal-accent);
  color: #1a1a1a;
}

.performance-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.chart-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--modal-radius-sm);
  overflow: hidden;
  transition: var(--modal-transition);
}

.chart-card:hover {
  border-color: var(--modal-border-active);
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.chart-card.large {
  /* Takes full width in grid */
}

.chart-card.medium {
  /* Normal grid cell */
}

.chart-header {
  background: linear-gradient(135deg,
    rgba(212, 175, 55, 0.1),
    rgba(212, 175, 55, 0.05));
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--modal-accent);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chart-legend {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
}

.chart-container {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 250px;
}

.chart-container canvas {
  max-width: 100%;
  height: auto;
}

.performance-tables {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.table-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--modal-radius-sm);
  overflow: hidden;
}

.table-header {
  background: linear-gradient(135deg,
    rgba(212, 175, 55, 0.1),
    rgba(212, 175, 55, 0.05));
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--modal-accent);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.export-btn {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  padding: 0.25rem 0.75rem;
  color: #3b82f6;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--modal-transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.export-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
}

.table-container {
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--modal-border) transparent;
}

.table-container::-webkit-scrollbar {
  width: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: transparent;
}

.table-container::-webkit-scrollbar-thumb {
  background: var(--modal-border);
  border-radius: 3px;
}

.loading-table {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--modal-text-muted);
  gap: 0.5rem;
}

/* ===== MATCH DETAILS MODAL ===== */
#match-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(12px);
  z-index: 10001; /* Higher than player modal */
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: var(--modal-transition);
}

#match-details-modal.show {
  opacity: 1;
  visibility: visible;
}

.match-details-modal-content {
  background: var(--modal-bg-primary);
  border-radius: var(--modal-radius);
  border: 2px solid var(--modal-border);
  box-shadow: var(--modal-shadow-lg);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transform: scale(0.9);
  transition: var(--modal-transition);
}

#match-details-modal.show .match-details-modal-content {
  transform: scale(1);
}

.match-details-container {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.match-details-section {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--modal-radius-sm);
  padding: 1.5rem;
  transition: var(--modal-transition);
}

.match-details-section:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--modal-border);
}

.match-details-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--modal-accent);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.match-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.match-info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-label {
  font-size: 0.8rem;
  color: var(--modal-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--modal-text-primary);
}

.match-players-detailed {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.team-detail {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: var(--modal-radius-sm);
  padding: 1rem;
}

.team-detail.winning-team {
  background: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.3);
}

.team-detail.losing-team {
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.3);
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.team-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--modal-text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.team-result {
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
}

.team-result.winner {
  background: rgba(16, 185, 129, 0.1);
  color: var(--modal-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.team-result.loser {
  background: rgba(239, 68, 68, 0.1);
  color: var(--modal-danger);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.team-players {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .modal-container {
    width: 95vw;
    height: 90vh;
  }

  .player-name {
    font-size: 2rem;
  }

  .performance-grid,
  .performance-tables {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 992px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }

  .overview-layout {
    grid-template-columns: 1fr;
  }

  .matches-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .matches-controls {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .player-modal-header {
    padding: 1.5rem;
  }

  .player-name {
    font-size: 1.5rem;
  }

  .player-subtitle {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .tab-description {
    display: none;
  }

  .modal-tab {
    padding: 0.75rem 1rem;
  }

  .match-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .match-info {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-group {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .stats-overview {
    grid-template-columns: 1fr;
  }

  .modal-tab {
    padding: 0.5rem 0.75rem;
  }

  .tab-icon {
    margin-right: 0.25rem;
  }

  .match-details-modal-content {
    width: 95vw;
  }

  .match-info-grid {
    grid-template-columns: 1fr;
  }
}
