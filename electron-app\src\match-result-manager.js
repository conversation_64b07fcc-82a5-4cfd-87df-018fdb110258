const { EventEmitter } = require('events');
const LogFileMonitor = require('./log-file-monitor');
const ReplayMonitor = require('./replay-monitor');
const ImageAnalyzer = require('./image-analyzer');
const axios = require('axios');
const path = require('path');

class MatchResultManager extends EventEmitter {
  constructor(gameDetector, screenshotManager, authManager, serverUrl) {
    super();
    this.gameDetector = gameDetector;
    this.screenshotManager = screenshotManager;
    this.authManager = authManager;
    this.serverUrl = serverUrl;
    
    // Initialize monitoring systems
    this.logFileMonitor = new LogFileMonitor(gameDetector);
    this.replayMonitor = new ReplayMonitor(gameDetector);
    this.imageAnalyzer = new ImageAnalyzer();
    
    // Match result correlation system
    this.recentResults = new Map(); // Store recent results for correlation
    this.correlationWindow = 5 * 60 * 1000; // 5 minutes
    
    // Settings
    this.settings = {
      autoReportEnabled: true,
      minimumConfidence: 70,
      requireMultipleConfirmation: false,
      maxResultsPerHour: 20,
      enableLogMonitoring: true,
      enableReplayMonitoring: true,
      enableScreenshotAnalysis: true
    };
    
    // Rate limiting
    this.resultCount = 0;
    this.hourlyResetTimer = null;
    
    this.initializeEventHandlers();
  }
  
  initializeEventHandlers() {
    // Log file monitoring events
    this.logFileMonitor.on('matchResult', (result) => {
      this.handleMatchResult(result, 'log_file');
    });
    
    // Replay monitoring events
    this.replayMonitor.on('replayResult', (result) => {
      this.handleMatchResult(result, 'replay_file');
    });
    
    // Screenshot analysis events (integrate with existing screenshot manager)
    this.screenshotManager.on('analysisComplete', (result) => {
      if (result.isGameResult) {
        this.handleMatchResult(result, 'screenshot_analysis');
      }
    });
    
    // Start hourly reset timer
    this.startHourlyReset();
  }
  
  async startMonitoring() {
    console.log('🚀 Starting comprehensive match result monitoring...');
    
    try {
      if (this.settings.enableLogMonitoring) {
        await this.logFileMonitor.startMonitoring();
      }
      
      if (this.settings.enableReplayMonitoring) {
        await this.replayMonitor.startMonitoring();
      }
      
      if (this.settings.enableScreenshotAnalysis) {
        // Screenshot monitoring is handled by the screenshot manager
        console.log('📸 Screenshot analysis integration ready');
      }
      
      console.log('✅ All match result monitoring systems started');
      
    } catch (error) {
      console.error('❌ Error starting match result monitoring:', error);
    }
  }
  
  async handleMatchResult(result, source) {
    try {
      console.log(`🎯 Match result detected via ${source}:`, {
        gameType: result.gameType,
        result: result.result,
        confidence: result.confidence,
        source
      });
      
      // Rate limiting check
      if (this.resultCount >= this.settings.maxResultsPerHour) {
        console.log('⚠️ Rate limit reached, skipping result processing');
        return;
      }
      
      // Confidence check
      if (result.confidence < this.settings.minimumConfidence) {
        console.log(`⚠️ Result confidence too low (${result.confidence}%), skipping`);
        return;
      }
      
      // Create correlation key for this result
      const correlationKey = this.createCorrelationKey(result);
      
      // Check if we require multiple confirmations
      if (this.settings.requireMultipleConfirmation) {
        const correlatedResult = await this.correlateResult(result, correlationKey);
        if (!correlatedResult) {
          console.log('⏳ Waiting for additional confirmation of match result');
          return;
        }
        result = correlatedResult;
      }
      
      // Process the match result
      await this.processMatchResult(result, source);
      
    } catch (error) {
      console.error('❌ Error handling match result:', error);
    }
  }
  
  createCorrelationKey(result) {
    // Create a key that can be used to correlate results from different sources
    const gameType = result.gameType || 'unknown';
    const timestamp = Math.floor(Date.now() / (60 * 1000)); // Round to minute
    const resultType = result.result || 'unknown';
    
    return `${gameType}_${timestamp}_${resultType}`;
  }
  
  async correlateResult(result, correlationKey) {
    // Store this result for correlation
    if (!this.recentResults.has(correlationKey)) {
      this.recentResults.set(correlationKey, []);
    }
    
    const existingResults = this.recentResults.get(correlationKey);
    existingResults.push(result);
    
    // Clean up old results
    this.cleanupOldResults();
    
    // Check if we have enough confirmations
    if (existingResults.length >= 2) {
      // Merge results from multiple sources
      const mergedResult = this.mergeResults(existingResults);
      
      // Clear this correlation group
      this.recentResults.delete(correlationKey);
      
      return mergedResult;
    }
    
    return null; // Need more confirmations
  }
  
  mergeResults(results) {
    // Merge multiple results into a single, high-confidence result
    const mergedResult = {
      gameType: results[0].gameType,
      result: results[0].result,
      confidence: 0,
      sources: [],
      players: [],
      matchData: {}
    };
    
    // Average confidence from all sources
    let totalConfidence = 0;
    for (const result of results) {
      totalConfidence += result.confidence;
      mergedResult.sources.push(result.source);
      
      // Merge player data
      if (result.players && result.players.length > 0) {
        mergedResult.players = [...mergedResult.players, ...result.players];
      }
      
      // Merge match data
      if (result.matchData) {
        mergedResult.matchData = { ...mergedResult.matchData, ...result.matchData };
      }
    }
    
    mergedResult.confidence = Math.min(95, totalConfidence / results.length + 10); // Bonus for multiple sources
    
    // Remove duplicate players
    const uniquePlayers = new Map();
    for (const player of mergedResult.players) {
      if (!uniquePlayers.has(player.name)) {
        uniquePlayers.set(player.name, player);
      }
    }
    mergedResult.players = Array.from(uniquePlayers.values());
    
    return mergedResult;
  }
  
  cleanupOldResults() {
    const now = Date.now();
    const cutoff = now - this.correlationWindow;
    
    for (const [key, results] of this.recentResults.entries()) {
      const filteredResults = results.filter(result => {
        const resultTime = result.timestamp ? result.timestamp.getTime() : now;
        return resultTime > cutoff;
      });
      
      if (filteredResults.length === 0) {
        this.recentResults.delete(key);
      } else {
        this.recentResults.set(key, filteredResults);
      }
    }
  }
  
  async processMatchResult(result, source) {
    try {
      console.log(`📊 Processing match result from ${source}:`, result);
      
      // Check if user is authenticated
      if (!this.authManager.isAuthenticated()) {
        console.log('ℹ️ User not authenticated, skipping automatic result reporting');
        this.emit('matchResult', { ...result, status: 'not_authenticated' });
        return;
      }
      
      // Check if auto-reporting is enabled
      if (!this.settings.autoReportEnabled) {
        console.log('ℹ️ Auto-reporting disabled, emitting result for manual handling');
        this.emit('matchResult', { ...result, status: 'auto_report_disabled' });
        return;
      }
      
      // Increment result count for rate limiting
      this.resultCount++;
      
      // Prepare data for server
      const reportData = await this.prepareReportData(result, source);
      
      // Send to server
      await this.reportToServer(reportData);
      
      // Emit success event
      this.emit('matchResult', { ...result, status: 'reported_successfully' });
      
    } catch (error) {
      console.error('❌ Error processing match result:', error);
      this.emit('matchResult', { ...result, status: 'error', error: error.message });
    }
  }
  
  async prepareReportData(result, source) {
    // Get current running game for additional context
    const runningGame = this.gameDetector.getCurrentRunningWarcraftGame();
    
    const reportData = {
      // Required fields for /api/game-results endpoint
      game: runningGame ? runningGame.name : `${result.gameType} (detected)`,
      gameType: result.gameType,
      result: result.result,
      confidence: result.confidence,
      
      // Additional data
      timestamp: result.timestamp ? result.timestamp.toISOString() : new Date().toISOString(),
      source: source,
      
      // Analysis data
      analysis: {
        sources: result.sources || [source],
        players: result.players || [],
        matchData: result.matchData || {},
        correlationUsed: this.settings.requireMultipleConfirmation
      }
    };
    
    // Add screenshot if available
    if (result.imagePath) {
      reportData.screenshot = result.imagePath;
    }
    
    return reportData;
  }
  
  async reportToServer(reportData) {
    if (!this.serverUrl) {
      throw new Error('Server URL not configured');
    }
    
    const token = this.authManager.getToken();
    if (!token) {
      throw new Error('Authentication token not available');
    }
    
    const response = await axios.post(
      `${this.serverUrl}/api/game-results`,
      reportData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'X-Electron-Client': 'true'
        },
        timeout: 10000
      }
    );
    
    if (response.status !== 200) {
      throw new Error(`Server responded with status ${response.status}`);
    }
    
    console.log('✅ Match result reported successfully:', response.data);
    return response.data;
  }
  
  stopMonitoring() {
    console.log('🛑 Stopping match result monitoring...');
    
    this.logFileMonitor.stopMonitoring();
    this.replayMonitor.stopMonitoring();
    
    if (this.hourlyResetTimer) {
      clearInterval(this.hourlyResetTimer);
    }
    
    this.recentResults.clear();
  }
  
  startHourlyReset() {
    // Reset result count every hour
    this.hourlyResetTimer = setInterval(() => {
      this.resultCount = 0;
      console.log('🔄 Hourly result count reset');
    }, 60 * 60 * 1000);
  }
  
  // Configuration methods
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    console.log('⚙️ Match result manager settings updated:', this.settings);
  }
  
  getSettings() {
    return { ...this.settings };
  }
  
  // Statistics and monitoring
  getStats() {
    return {
      settings: this.settings,
      monitoring: {
        logFileMonitor: this.logFileMonitor.getMonitoringStats(),
        replayMonitor: this.replayMonitor.getMonitoringStats(),
        screenshotAnalysis: this.screenshotManager ? this.screenshotManager.getStats() : null
      },
      correlation: {
        recentResults: this.recentResults.size,
        correlationWindow: this.correlationWindow
      },
      rateLimit: {
        resultCount: this.resultCount,
        maxResultsPerHour: this.settings.maxResultsPerHour
      }
    };
  }

  getMatchCount() {
    return this.resultCount;
  }

  getLastMatchTime() {
    // Return the timestamp of the last match result
    // This would need to be tracked in the class
    return new Date();
  }
  
  // Manual processing methods
  async processManualScreenshot(imagePath) {
    const gameType = this.gameDetector.getCurrentRunningWarcraftGame()?.type || 'unknown';
    const result = await this.imageAnalyzer.analyzeScreenshot(imagePath, gameType);
    
    if (result && result.isGameResult) {
      await this.handleMatchResult(result, 'manual_screenshot');
    }
    
    return result;
  }
  
  async processManualReplay(filePath) {
    await this.replayMonitor.processManualReplay(filePath);
  }

  async getRecentMatches(limit = 10) {
    console.log(`🏆 Getting recent matches (limit: ${limit})`);
    
    try {
      // Get recent results from the correlation system
      const recentResults = Array.from(this.recentResults.entries())
        .map(([key, results]) => {
          const latestResult = results[results.length - 1];
          return {
            id: key,
            gameType: latestResult.gameType,
            result: latestResult.result,
            confidence: latestResult.confidence,
            timestamp: latestResult.timestamp || new Date().toISOString(),
            source: latestResult.source,
            players: latestResult.players || [],
            matchData: latestResult.matchData || {}
          };
        })
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, limit);
      
      console.log(`✅ Found ${recentResults.length} recent matches`);
      return recentResults;
      
    } catch (error) {
      console.error('❌ Error getting recent matches:', error);
      throw error;
    }
  }
}

module.exports = MatchResultManager; 