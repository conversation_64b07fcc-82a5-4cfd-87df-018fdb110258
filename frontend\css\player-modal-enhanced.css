/* ==========================================================================
   MODERN PLAYER MODAL STYLING
   Clean, optimized design with better UX
   ========================================================================== */

/* ===== MODAL OVERLAY ===== */
#player-stats-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(8px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#player-stats-modal.show {
  opacity: 1;
  visibility: visible;
}

/* ===== MODAL CONTENT ===== */
#player-stats-modal .modal-content {
  background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
  border-radius: 16px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  max-width: 95vw;
  max-height: 95vh;
  width: 1000px;
  height: 85vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  transform: scale(0.9);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#player-stats-modal.show .modal-content {
  transform: scale(1);
}

/* ===== PLAYER MODAL CONTAINER ===== */
.player-modal-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 90vh;
  overflow: hidden;
}

/* ===== PLAYER HEADER - PROFESSIONAL DESIGN ===== */
.player-modal-header {
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.95),
    rgba(30, 41, 59, 0.9));
  border-bottom: 2px solid rgba(212, 175, 55, 0.4);
  padding: 2rem;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  overflow: hidden;
  gap: 2rem;
  backdrop-filter: blur(10px);
  min-height: 140px;
}

.player-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent,
    rgba(212, 175, 55, 0.5),
    transparent);
}

.player-modal-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(212, 175, 55, 0.05) 50%,
    transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0%, 100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

/* ===== PROFESSIONAL HEADER LAYOUT ===== */
.player-header-content {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  flex: 1;
  z-index: 1;
  position: relative;
  width: 100%;
}

.player-basic-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex: 1;
}

.player-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg,
    rgba(212, 175, 55, 0.2),
    rgba(212, 175, 55, 0.1));
  border: 3px solid rgba(212, 175, 55, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow:
    0 8px 20px rgba(212, 175, 55, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.player-avatar:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 25px rgba(212, 175, 55, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(212, 175, 55, 0.8);
}

.player-avatar img {
  width: 70px;
  height: 70px;
  object-fit: cover;
  border-radius: 50%;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.4));
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.player-avatar:hover img {
  transform: scale(1.05);
}

.player-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
  justify-content: center;
}

.player-name {
  font-size: 2.2rem;
  font-weight: 700;
  color: #D4AF37;
  margin: 0;
  line-height: 1.1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  font-family: 'Cinzel', serif;
  letter-spacing: 0.5px;
}

.player-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.player-rank,
.player-rating,
.player-status {
  font-size: 0.9rem;
  font-weight: 600;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
  transition: all 0.3s ease;
}

.player-rank {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
  border-color: rgba(212, 175, 55, 0.4);
  color: #D4AF37;
}

.player-rating {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1));
  border-color: rgba(59, 130, 246, 0.4);
  color: #60a5fa;
}

.player-status.online {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.1));
  border-color: rgba(16, 185, 129, 0.4);
  color: #10b981;
}

.player-status.offline {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.2), rgba(107, 114, 128, 0.1));
  border-color: rgba(107, 114, 128, 0.4);
  color: #9ca3af;
}

/* ===== QUICK STATS SECTION ===== */
.player-quick-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  min-width: 200px;
  flex-shrink: 0;
}

.quick-stat {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.quick-stat:hover {
  background: rgba(255, 255, 255, 0.06);
  border-color: rgba(212, 175, 55, 0.3);
  transform: translateY(-2px);
}

.quick-stat::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.6), transparent);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.quick-stat:hover::before {
  transform: scaleX(1);
}

.stat-value {
  display: block;
  font-size: 1.4rem;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}



/* ===== CLOSE BUTTON - PROFESSIONAL POSITIONING ===== */
.close-modal {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(212, 175, 55, 0.4);
  color: #D4AF37;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  z-index: 100;
  flex-shrink: 0;
  backdrop-filter: blur(10px);
}

.close-modal:hover {
  background: rgba(239, 68, 68, 0.8);
  color: white;
  border-color: #ef4444;
  transform: scale(1.1);
}

/* ===== PROFESSIONAL MODAL TABS ===== */
.modal-tabs {
  display: flex;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9));
  border-bottom: 2px solid rgba(212, 175, 55, 0.3);
  padding: 0 1rem;
  position: relative;
}

.modal-tabs::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.6), transparent);
}

.modal-tab {
  padding: 1rem 1.5rem;
  background: transparent;
  border: none;
  color: #94a3b8;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modal-tab:hover {
  color: #D4AF37;
  background: rgba(255, 255, 255, 0.05);
}

.modal-tab.active {
  color: #D4AF37;
  border-bottom-color: #D4AF37;
  background: rgba(212, 175, 55, 0.05);
}

.modal-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.6), transparent);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.modal-tab.active::before {
  transform: scaleX(1);
}

/* ===== TAB CONTENT ===== */
.modal-tab-content {
  display: none;
  flex: 1;
  overflow: hidden;
  padding: 0;
  min-height: 0;
  background: rgba(15, 23, 42, 0.3);
}

.modal-tab-content.active {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ===== LOADING STATES ===== */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #94a3b8;
  gap: 1rem;
  flex: 1;
}

.loading i {
  font-size: 2rem;
  color: #D4AF37;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== RESPONSIVE DESIGN IMPROVEMENTS ===== */
@media (max-width: 1200px) {
  #player-stats-modal .modal-content {
    width: 95vw;
    height: 90vh;
  }

  .player-name {
    font-size: 1.8rem;
  }

  .player-quick-stats {
    grid-template-columns: 1fr 1fr;
  }

  .performance-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 992px) {
  .player-modal-header {
    padding: 1.5rem;
    min-height: auto;
  }

  .player-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  .player-quick-stats {
    grid-template-columns: repeat(4, 1fr);
    width: 100%;
  }

  .modal-tab {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .player-modal-header {
    padding: 1rem;
  }

  .player-name {
    font-size: 1.5rem;
  }

  .player-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .player-quick-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .modal-tabs {
    padding: 0 0.5rem;
  }

  .modal-tab {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .close-modal {
    top: 0.5rem;
    right: 0.5rem;
    width: 35px;
    height: 35px;
  }
}

@media (max-width: 576px) {
  .player-basic-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
  }

  .player-avatar {
    width: 60px;
    height: 60px;
  }

  .player-avatar img {
    width: 50px;
    height: 50px;
  }

  .player-quick-stats {
    grid-template-columns: 1fr 1fr;
  }

  .modal-tab {
    flex-direction: column;
    gap: 0.25rem;
    padding: 0.5rem;
  }

  .modal-tab i {
    font-size: 1rem;
  }
}

/* ===== OVERVIEW CONTENT ===== */
.overview-content {
  padding: 30px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

/* ===== MATCHES TAB PROFESSIONAL STYLING ===== */
.matches-content {
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.95),
    rgba(30, 41, 59, 0.9));
  border-radius: 12px;
  border: 1px solid rgba(212, 175, 55, 0.2);
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.matches-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(212, 175, 55, 0.6) 50%,
    transparent 100%);
  z-index: 1;
}

.matches-list {
  padding: 1.5rem;
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(212, 175, 55, 0.5) transparent;
}

.matches-list::-webkit-scrollbar {
  width: 6px;
}

.matches-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.matches-list::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.5);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.matches-list::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.7);
}

/* ===== PROFESSIONAL MATCH ITEMS ===== */
.match-item {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  margin-bottom: 1rem;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  min-height: 80px;
  display: block;
}

.match-item:hover {
  background: rgba(255, 255, 255, 0.06);
  border-color: rgba(212, 175, 55, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.match-item.match-win {
  border-left: 4px solid #10b981;
}

.match-item.match-loss {
  border-left: 4px solid #ef4444;
}

.match-item.match-draw {
  border-left: 4px solid #f59e0b;
}

.match-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.05),
    rgba(255, 255, 255, 0.02));
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  gap: 1rem;
}

.match-outcome {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #ffffff;
}

.match-outcome i {
  font-size: 1rem;
}

.match-win .match-outcome {
  color: #10b981;
}

.match-loss .match-outcome {
  color: #ef4444;
}

.match-draw .match-outcome {
  color: #f59e0b;
}

.match-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
  font-weight: 500;
  font-size: 0.85rem;
}

.mmr-change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.3rem 0.6rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.8rem;
  border: 1px solid;
}

.mmr-change.positive {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border-color: rgba(16, 185, 129, 0.3);
}

.mmr-change.negative {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

.match-date {
  color: #e2e8f0;
  font-size: 0.85rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.match-expand-icon {
  color: #D4AF37;
  font-size: 0.9rem;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.match-item:hover .match-expand-icon {
  opacity: 1;
  transform: scale(1.1);
}

/* ===== MATCH CONTENT SECTION ===== */
.match-content {
  padding: 1rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.match-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.match-map {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #e2e8f0;
  font-weight: 500;
  font-size: 0.9rem;
  padding: 0.4rem 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.match-map i {
  color: #D4AF37;
  font-size: 0.9rem;
}

.match-resources {
  font-size: 0.8rem;
  color: #94a3b8;
  padding: 0.25rem 0.5rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.match-players {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.player-link {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  color: #e2e8f0;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.player-link:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(212, 175, 55, 0.3);
  color: #D4AF37;
  text-decoration: none;
}

.player-link.winner {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
  color: #10b981;
}

.player-link.loser {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.player-separator {
  color: #94a3b8;
  font-size: 0.8rem;
  margin: 0 0.25rem;
}

.vs-separator {
  font-size: 0.8rem;
  color: #94a3b8;
  font-weight: 600;
  margin: 0 0.5rem;
}

/* ===== TEAM MATCH STYLING ===== */
.match-players.team-match {
  flex-direction: column;
  align-items: stretch;
  gap: 0.5rem;
}

.team {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 0.5rem;
}

.team.winning-team {
  background: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.2);
}

.team.losing-team {
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.team-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.team-result {
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
}

.team-result.winner {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.team-result.loser {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.team-players {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.team-vs-separator {
  text-align: center;
  font-size: 0.8rem;
  color: #94a3b8;
  font-weight: 600;
  padding: 0.25rem 0;
}

.match-type-badge {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  color: #D4AF37;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  align-self: center;
}

.wc1-badge {
  background: rgba(255, 165, 0, 0.1);
  border: 1px solid rgba(255, 165, 0, 0.3);
  color: #ffa500;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: 0.5rem;
}

/* ===== FFA MATCH STYLING ===== */
.match-players.ffa-match {
  flex-direction: column;
  gap: 0.5rem;
}

.ffa-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #D4AF37;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.25rem 0.5rem;
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 4px;
  align-self: flex-start;
}

.ffa-players {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.ffa-players .player-link {
  font-size: 0.75rem;
  padding: 0.2rem 0.4rem;
}

/* ===== SIMPLIFIED TEAM MATCH STYLING ===== */
.team-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #D4AF37;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.25rem 0.5rem;
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 4px;
  margin-bottom: 0.5rem;
  display: inline-block;
}

.match-players .team-players {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.match-players .team-players .player-link {
  font-size: 0.75rem;
  padding: 0.2rem 0.4rem;
}

/* ===== CURRENT PLAYER HIGHLIGHTING ===== */
.player-link.current-player {
  background: rgba(212, 175, 55, 0.15);
  border-color: rgba(212, 175, 55, 0.4);
  color: #D4AF37;
  font-weight: 600;
}

.player-link.current-player:hover {
  background: rgba(212, 175, 55, 0.25);
  border-color: rgba(212, 175, 55, 0.6);
}

/* ===== AI PLAYER STYLING ===== */
.ai-player {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background: rgba(107, 114, 128, 0.1);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  color: #9ca3af;
  font-weight: 500;
}

/* ===== MATCH INFO FALLBACK ===== */
.match-info {
  font-size: 0.8rem;
  color: #94a3b8;
  font-style: italic;
}

.match-opponents {
  font-size: 0.8rem;
  color: #94a3b8;
  font-weight: 500;
}

/* ===== MATCHES PAGINATION ===== */
.matches-pagination {
  background: rgba(255, 255, 255, 0.02);
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.pagination-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  color: #94a3b8;
  font-size: 0.85rem;
}

.matches-range {
  font-weight: 600;
  color: #e2e8f0;
}

.page-info {
  font-size: 0.8rem;
  opacity: 0.8;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.pagination-controls .btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-controls .btn:hover:not(:disabled) {
  background: rgba(212, 175, 55, 0.1);
  border-color: rgba(212, 175, 55, 0.3);
  color: #D4AF37;
}

.pagination-controls .btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.page-number {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 32px;
  text-align: center;
}

.page-number:hover {
  background: rgba(212, 175, 55, 0.1);
  border-color: rgba(212, 175, 55, 0.3);
  color: #D4AF37;
}

.page-number.active {
  background: linear-gradient(135deg, #D4AF37, #b8860b);
  border-color: #D4AF37;
  color: #1a1a1a;
  font-weight: 700;
}

/* ===== ERROR STATES ===== */
.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: #94a3b8;
  gap: 1rem;
}

.error-message i {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-message h3 {
  color: #e2e8f0;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.error-message p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.error-details {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 6px;
  padding: 0.75rem;
  font-family: monospace;
  font-size: 0.8rem;
  color: #fca5a5;
  max-width: 100%;
  word-break: break-word;
}

.error-message .btn {
  background: linear-gradient(135deg, #D4AF37, #b8860b);
  border: none;
  color: #1a1a1a;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-message .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(212, 175, 55, 0.3);
}

/* ===== STYLISH & CONDENSED PERFORMANCE TAB ===== */
.performance-content {
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.95),
    rgba(30, 41, 59, 0.9));
  border-radius: 12px;
  border: 1px solid rgba(212, 175, 55, 0.2);
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  padding: 1.5rem;
}

.performance-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(212, 175, 55, 0.6) 50%,
    transparent 100%);
  z-index: 1;
}

.performance-section {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1rem;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.performance-section:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(212, 175, 55, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.performance-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #D4AF37;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.performance-section h4 i {
  font-size: 1rem;
  color: #D4AF37;
}

/* ===== CONDENSED RACE CHART STYLING ===== */
.race-chart-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.race-chart {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.race-chart-item {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 0.75rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.race-chart-item:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(212, 175, 55, 0.2);
  transform: translateX(4px);
}

.race-chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.race-icon {
  font-size: 1rem;
  margin-right: 0.5rem;
  color: #D4AF37;
}

.race-name {
  font-weight: 600;
  color: #e2e8f0;
  font-size: 0.9rem;
  flex: 1;
}

.race-winrate {
  font-weight: 700;
  font-size: 0.9rem;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  border: 1px solid;
  min-width: 50px;
  text-align: center;
}

.race-winrate.excellent {
  background: rgba(16, 185, 129, 0.15);
  color: #10b981;
  border-color: rgba(16, 185, 129, 0.4);
}

.race-winrate.good {
  background: rgba(34, 197, 94, 0.15);
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.4);
}

.race-winrate.average {
  background: rgba(245, 158, 11, 0.15);
  color: #f59e0b;
  border-color: rgba(245, 158, 11, 0.4);
}

.race-winrate.poor {
  background: rgba(239, 68, 68, 0.15);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.4);
}

.race-chart-bars {
  display: flex;
  height: 6px;
  border-radius: 3px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  margin-bottom: 0.4rem;
  position: relative;
}

.race-wins-bar {
  background: linear-gradient(90deg, #10b981, #059669);
  transition: width 0.8s ease;
  border-radius: 3px 0 0 3px;
}

.race-losses-bar {
  background: linear-gradient(90deg, #ef4444, #dc2626);
  transition: width 0.8s ease;
  border-radius: 0 3px 3px 0;
}

.race-total {
  font-size: 0.75rem;
  color: #94a3b8;
  text-align: center;
  font-weight: 500;
}

/* ===== GAME TYPE MAPS SECTION ===== */
.game-type-maps-section {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.game-type-maps-section:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(212, 175, 55, 0.2);
}

.game-type-maps-section h4 {
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #D4AF37;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.no-detailed-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #94a3b8;
  gap: 1rem;
}

.no-detailed-data i {
  font-size: 2rem;
  color: #D4AF37;
  opacity: 0.7;
}

.no-detailed-data p {
  margin: 0;
  font-size: 0.9rem;
  color: #94a3b8;
}

/* ===== CONDENSED ADDITIONAL STATS SECTION ===== */
.additional-stats-section {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1rem;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.additional-stats-section:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(212, 175, 55, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.additional-stats-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #D4AF37;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  flex: 1;
}

.stat-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(212, 175, 55, 0.2);
  transform: translateX(4px);
}

.stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
  border: 1px solid rgba(212, 175, 55, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  color: #D4AF37;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  display: block;
  font-size: 1.1rem;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
  margin-bottom: 0.2rem;
}

.stat-label {
  display: block;
  font-size: 0.7rem;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* ===== MATCH DETAILS MODAL STYLING ===== */
#match-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(12px);
  z-index: 10001; /* Higher than player modal */
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  visibility: visible;
}

#match-details-modal.active {
  opacity: 1;
  visibility: visible;
}

.match-details-modal-content {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
  border-radius: 16px;
  border: 2px solid rgba(212, 175, 55, 0.3);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.6);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.match-details-modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.6), transparent);
  z-index: 1;
}

.match-details-modal-content .modal-header {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9));
  border-bottom: 2px solid rgba(212, 175, 55, 0.3);
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.match-details-modal-content .modal-title-area h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #D4AF37;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-family: 'Cinzel', serif;
}

.match-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
}

.match-date {
  font-size: 0.9rem;
  color: #94a3b8;
  font-weight: 500;
}

.match-id {
  font-size: 0.8rem;
  color: #D4AF37;
  background: rgba(212, 175, 55, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  font-family: monospace;
}

.match-details-modal-content .close-modal-btn {
  position: relative;
  top: auto;
  right: auto;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(212, 175, 55, 0.4);
  color: #D4AF37;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  z-index: 10;
  backdrop-filter: blur(10px);
}

.match-details-modal-content .close-modal-btn:hover {
  background: rgba(239, 68, 68, 0.8);
  color: white;
  border-color: #ef4444;
  transform: scale(1.1);
}

.match-details-modal-content .modal-body {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(212, 175, 55, 0.5) transparent;
}

.match-details-modal-content .modal-body::-webkit-scrollbar {
  width: 6px;
}

.match-details-modal-content .modal-body::-webkit-scrollbar-track {
  background: transparent;
}

.match-details-modal-content .modal-body::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.5);
  border-radius: 3px;
}

.match-details-container {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.match-details-section {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.match-details-section:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(212, 175, 55, 0.2);
}

.match-details-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #D4AF37;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.match-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.match-info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-label {
  font-size: 0.8rem;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.info-value {
  font-size: 1rem;
  font-weight: 600;
  color: #e2e8f0;
}

.info-value.match-result {
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid;
}

.info-value.match-result.win {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border-color: rgba(16, 185, 129, 0.3);
}

.info-value.match-result.loss {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

/* ===== RESPONSIVE MATCH DETAILS MODAL ===== */
@media (max-width: 768px) {
  .match-details-modal-content {
    width: 95vw;
    max-height: 95vh;
  }

  .match-details-modal-content .modal-header {
    padding: 1rem;
  }

  .match-details-modal-content .modal-title-area h2 {
    font-size: 1.25rem;
  }

  .match-details-container {
    padding: 1rem;
  }

  .match-info-grid {
    grid-template-columns: 1fr;
  }
}

/* ===== MATCH DETAILS CONTENT STYLING ===== */
.match-overview {
  margin-bottom: 1.5rem;
}

.match-overview .match-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.info-card:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(212, 175, 55, 0.2);
}

.info-card .info-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.info-card .info-label i {
  color: #D4AF37;
}

.info-card .info-value {
  font-size: 1rem;
  font-weight: 600;
  color: #e2e8f0;
}

.players-section {
  margin-top: 1.5rem;
}

.players-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #D4AF37;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.players-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.player-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
  position: relative;
}

.player-card:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(212, 175, 55, 0.2);
  transform: translateY(-2px);
}

.player-card.winner {
  border-left: 4px solid #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.player-card.loser {
  border-left: 4px solid #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.player-info {
  margin-bottom: 0.75rem;
}

.player-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 0.5rem;
}

.player-name i {
  color: #D4AF37;
}

.player-race {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #94a3b8;
}

.player-race i {
  color: #D4AF37;
}

.player-result {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.player-card.winner .player-result {
  color: #10b981;
}

.player-card.loser .player-result {
  color: #ef4444;
}

.player-card .mmr-change {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid;
}

.player-card .mmr-change.positive {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border-color: rgba(16, 185, 129, 0.3);
}

.player-card .mmr-change.negative {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

/* ===== RESPONSIVE MATCH DETAILS CONTENT ===== */
@media (max-width: 768px) {
  .match-overview .match-info-grid {
    grid-template-columns: 1fr;
  }

  .players-grid {
    grid-template-columns: 1fr;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #2a2a2a;
  border-radius: 10px;
  border: 1px solid #D4AF37;
  padding: 20px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(212, 175, 55, 0.2);
}

.stat-content {
  text-align: center;
}

.stat-value {
  font-size: 2.2rem;
  font-weight: 700;
  color: #D4AF37;
  margin-bottom: 8px;
}

.stat-value.positive {
  color: #4ade80;
}

.stat-value.negative {
  color: #f87171;
}

.stat-label {
  font-size: 0.9rem;
  color: #ccc;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== MATCHES CONTENT ===== */
.matches-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  height: 100%;
}

.matches-list {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  min-height: 0;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
}

/* ===== DUPLICATE CSS REMOVED - USING PROFESSIONAL MATCH ITEMS ABOVE ===== */
/* ===== DUPLICATE STYLES REMOVED - USING PROFESSIONAL STYLES ABOVE ===== */

/* Match details section removed - now handled by modal */

/* Match map and players container removed - now handled by modal */

/* Match players and team styles removed - now handled by modal */

/* Player link and MMR change styles removed - now handled by modal */

/* ===== EXPANDED MATCH DETAILS - FIXED VISIBILITY ===== */
.match-details-expanded {
  background: #333;
  border-top: 1px solid #444;
  padding: 20px;
}

.match-details-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.match-details-section {
  background: #2a2a2a;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #444;
}

.match-details-section h4 {
  color: #D4AF37;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.match-players-detailed {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.team-detail {
  background: #333;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #444;
}

.team-detail.winning-team {
  border-color: rgba(74, 222, 128, 0.5);
  background: rgba(74, 222, 128, 0.05);
}

.team-detail.losing-team {
  border-color: rgba(248, 113, 113, 0.5);
  background: rgba(248, 113, 113, 0.05);
}

.team-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
  font-weight: 600;
}

.team-label {
  color: #ccc;
}

.team-result.winner {
  color: #4ade80;
}

.team-result.loser {
  color: #f87171;
}

.team-players-detailed {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.player-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #2a2a2a;
  border-radius: 4px;
  font-size: 0.9rem;
}

.player-detail.current-player {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.player-detail-header {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.player-detail-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #ccc;
  font-size: 0.8rem;
}

.result-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.result-badge.winner {
  background: rgba(74, 222, 128, 0.2);
  color: #4ade80;
  border: 1px solid rgba(74, 222, 128, 0.3);
}

.result-badge.loser {
  background: rgba(248, 113, 113, 0.2);
  color: #f87171;
  border: 1px solid rgba(248, 113, 113, 0.3);
}

.match-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.match-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #333;
  border-radius: 4px;
  font-size: 0.9rem;
}

.info-label {
  color: #ccc;
  font-weight: 500;
}

.info-value {
  color: #fff;
  font-weight: 600;
}

.info-value.positive {
  color: #4ade80;
}

.info-value.negative {
  color: #f87171;
}

/* ===== PAGINATION ===== */
.matches-pagination {
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  border-top: 2px solid #D4AF37;
  padding: 20px 30px;
  box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #ccc;
  background: rgba(255, 255, 255, 0.05);
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid rgba(212, 175, 55, 0.2);
  margin-bottom: 0;
}

.matches-range,
.page-info {
  font-weight: 500;
  color: #e0e0e0;
}

.matches-range {
  color: #D4AF37;
  font-weight: 600;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 12px;
}

.btn {
  padding: 10px 18px;
  border-radius: 8px;
  border: 2px solid #D4AF37;
  background: linear-gradient(135deg, #333, #2a2a2a);
  color: #D4AF37;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: left 0.4s;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #D4AF37, #b8860b);
  color: #1a1a1a;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-primary {
  background: linear-gradient(135deg, #D4AF37, #b8860b);
  color: #1a1a1a;
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, #333, #2a2a2a);
  border-color: #666;
  color: #ccc;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0 8px;
}

.page-ellipsis {
  color: #999;
  padding: 0 8px;
  font-weight: 600;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  #player-stats-modal .modal-content {
    width: 95vw;
    max-height: 95vh;
  }
  
  .player-modal-header {
    padding: 20px 25px;
    min-height: 90px;
  }
  
  .player-info {
    gap: 20px;
  }
  
  .player-rank {
    width: 65px;
    height: 65px;
  }
  
  .player-rank .rank-icon {
    width: 55px;
    height: 55px;
  }
  
  .player-name {
    font-size: 1.7rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
  }
  
  .stat-value {
    font-size: 1.8rem;
  }
}

@media (max-width: 768px) {
  .player-modal-header {
    flex-direction: row;
    gap: 15px;
    text-align: left;
    padding: 15px;
    min-height: auto;
  }
  
  .player-info {
    flex-direction: row;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
  }
  
  .player-rank {
    width: 60px;
    height: 60px;
  }
  
  .player-rank .rank-icon {
    width: 50px;
    height: 50px;
  }
  
  .player-name {
    font-size: 1.5rem;
    text-align: left;
  }
  
  .player-details {
    align-items: flex-start;
    text-align: left;
    flex: 1;
  }
  
  .player-mmr {
    min-width: 80px;
    padding: 12px 16px;
  }
  
  .mmr-value {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .player-modal-header {
    padding: 12px 15px;
    gap: 12px;
  }
  
  .player-info {
    gap: 12px;
  }
  
  .player-rank {
    width: 50px;
    height: 50px;
  }
  
  .player-rank .rank-icon {
    width: 42px;
    height: 42px;
  }
  
  .player-name {
    font-size: 1.3rem;
  }
  
  .player-details {
    gap: 8px;
  }
  
  .race-display {
    font-size: 0.8rem;
    padding: 4px 8px;
  }
  
  .rank-name {
    font-size: 0.9rem;
    padding: 3px 8px;
  }
  
  .player-mmr {
    min-width: 70px;
    padding: 10px 12px;
  }
  
  .mmr-value {
    font-size: 1.1rem;
  }
  
  .mmr-label {
    font-size: 0.7rem;
  }
  
  .modal-tabs {
    padding: 0 10px;
  }
  
  .modal-tab {
    padding: 12px 15px;
    font-size: 0.9rem;
  }
  
  .overview-content,
  .matches-list {
    padding: 15px;
  }
  
  .match-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .match-details {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .match-players {
    flex-direction: column;
    gap: 8px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .match-info-grid {
    grid-template-columns: 1fr;
  }
  
  .matches-pagination {
    padding: 15px 20px;
  }
  
  .pagination-info {
    flex-direction: column;
    gap: 10px;
    text-align: center;
    align-items: center;
  }
  
  .matches-range,
  .page-info {
    font-size: 0.8rem;
  }
  
  .pagination-controls {
    gap: 6px;
  }
  
  .btn {
    padding: 8px 14px;
    font-size: 0.8rem;
  }
}

/* ===== DUPLICATE MODAL CSS REMOVED ===== */

/* ===== DUPLICATE CONTENT REMOVED - USING STYLES ABOVE ===== */

/* ===== DUPLICATE MODAL HEADER CSS REMOVED ===== */

/* ===== ALL DUPLICATE CSS REMOVED - USING ORIGINAL STYLES ABOVE ===== */

