<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Become a Hero - WC Arena</title>
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="/css/black-theme.css" />
  <link rel="stylesheet" href="/css/player-modal-enhanced.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/css/navbar-universal.css" />
</head>
<body>
  <div id="navbar-container"></div>
  <main>
    <!-- Hero Page Header -->
    <div class="page-header">
      <h1 class="page-title" data-theme="blacksmith">🛡️ Become a Hero</h1>
      <!-- Currency selector and subtitle will be inserted here by MembershipManager -->
    </div>

    <!-- Membership Section -->
    <section id="hero-section" class="glass-section membership-section fade-in-up">
      <div id="membership-container" class="membership-showcase centered">
        <div class="membership-tiers" id="membership-tiers">
          <!-- Tiers will be loaded dynamically -->
        </div>
      </div>
    </section>

    <!-- Membership Info Section -->
    <section class="glass-section fade-in-up" style="margin-top:2rem;">
      <div class="membership-info centered">
        <p><i class="fas fa-shield-alt"></i> Secure monthly subscriptions with Square</p>
        <p><i class="fas fa-sync-alt"></i> Easy upgrade/downgrade anytime</p>
        <p><i class="fas fa-times-circle"></i> Cancel subscription anytime - no long-term commitment</p>
      </div>
    </section>

    <!-- Donation Section -->
    <section class="glass-section fade-in-up" style="margin-top:2rem;">
      <div class="donation-links centered">
        <div class="donation-title">One-time donations to support server costs</div>
        <div class="donation-buttons">
          <a href="https://www.paypal.com/paypalme/wolfandman" target="_blank" class="donation-btn paypal">
            <i class="fab fa-paypal"></i>
            <span>PayPal Donation</span>
          </a>
          <a href="https://commerce.coinbase.com/checkout/fce0e325-d5b9-4b8c-a270-5508bdde7eeb" target="_blank" class="donation-btn coinbase">
            <i class="fab fa-bitcoin"></i>
            <span>Coinbase Donation</span>
          </a>
        </div>
      </div>
    </section>
  </main>
  <div id="footer-container"></div>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
  <script type="module" src="/js/modules/ApiClient.js"></script>
  <script type="module" src="/js/app.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/modules/MembershipManager.js?v=20241221-2"></script>
      <script>
      // Navbar and membership manager initialization
      window.addEventListener('load', async () => {
        console.log('🔄 Page fully loaded, initializing components...');
        
        // Initialize membership manager first
        if (window.membershipManager && window.membershipManager.init) {
          await window.membershipManager.init();
          // Force render membership tiers for hero page
          if (window.membershipManager.renderMembershipTiers) {
            console.log('🎨 Forcing membership tiers render for hero page');
            window.membershipManager.renderMembershipTiers();
          }
        }
        
        // main.js will handle navbar loading automatically
        console.log('✅ Hero page initialized - navbar handled by main.js');
      });
    </script>
</body>
</html> 